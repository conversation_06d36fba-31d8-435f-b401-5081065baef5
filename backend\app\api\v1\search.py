import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List, Dict, Any
from uuid import UUID

from app.core.security import get_current_user_id
from app.services import search_service

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=Dict[str, Any])
@router.get("", response_model=Dict[str, Any])
async def global_search(
    q: str = Query(..., description="Search query", min_length=1),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    include_proyectos: bool = Query(True, description="Include proyectos in search"),
    include_procesos: bool = Query(True, description="Include procesos in search"),
    include_tareas: bool = Query(True, description="Include tareas in search"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Perform global search across proyectos, procesos, and tareas.
    """
    try:
        logger.info(f"Global search for query: '{q}' by user {current_user_id}")
        
        results = await search_service.global_search(
            query=q,
            limit=limit,
            include_proyectos=include_proyectos,
            include_procesos=include_procesos,
            include_tareas=include_tareas
        )
        
        logger.info(f"Search returned {results['total_results']} results")
        return results
        
    except Exception as e:
        logger.error(f"Error in global search: {str(e)}")
        raise

@router.get("/by-type/{search_type}", response_model=List[Dict[str, Any]])
async def search_by_type(
    search_type: str,
    q: str = Query(..., description="Search query", min_length=1),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Search within a specific type (proyectos, procesos, or tareas).
    """
    try:
        logger.info(f"Search by type '{search_type}' for query: '{q}' by user {current_user_id}")
        
        if search_type not in ["proyectos", "procesos", "tareas"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid search type: {search_type}. Must be one of: proyectos, procesos, tareas"
            )
        
        results = await search_service.search_by_type(
            query=q,
            search_type=search_type,
            limit=limit
        )
        
        logger.info(f"Search by type returned {len(results)} results")
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search by type: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error in search by type: {str(e)}"
        )

@router.get("/suggestions", response_model=List[str])
async def get_search_suggestions(
    q: str = Query(..., description="Partial search query", min_length=1),
    limit: int = Query(10, ge=1, le=20, description="Maximum number of suggestions"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get search suggestions based on partial query.
    """
    try:
        logger.info(f"Getting search suggestions for query: '{q}' by user {current_user_id}")
        
        suggestions = await search_service.get_search_suggestions(
            query=q,
            limit=limit
        )
        
        logger.info(f"Returned {len(suggestions)} suggestions")
        return suggestions
        
    except Exception as e:
        logger.error(f"Error getting search suggestions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting search suggestions: {str(e)}"
        )

@router.get("/quick", response_model=Dict[str, Any])
async def quick_search(
    q: str = Query(..., description="Search query", min_length=1),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Quick search with limited results for autocomplete/dropdown.
    """
    try:
        logger.info(f"Quick search for query: '{q}' by user {current_user_id}")
        
        # Perform a limited global search
        results = await search_service.global_search(
            query=q,
            limit=15,  # Limit total results for quick search
            include_proyectos=True,
            include_procesos=True,
            include_tareas=True
        )
        
        # Limit each category for quick display
        quick_results = {
            "query": results["query"],
            "proyectos": results["proyectos"][:3],
            "procesos": results["procesos"][:3],
            "tareas": results["tareas"][:5],
            "total_results": results["total_results"],
            "has_more": {
                "proyectos": len(results["proyectos"]) > 3,
                "procesos": len(results["procesos"]) > 3,
                "tareas": len(results["tareas"]) > 5
            }
        }
        
        return quick_results
        
    except Exception as e:
        logger.error(f"Error in quick search: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error in quick search: {str(e)}"
        )

@router.get("/filters/available")
async def get_available_filters(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available filters for search.
    """
    try:
        logger.info(f"Getting available search filters for user {current_user_id}")
        
        return {
            "types": [
                {"value": "proyectos", "label": "Proyectos"},
                {"value": "procesos", "label": "Procesos"},
                {"value": "tareas", "label": "Tareas"}
            ],
            "estados_proyecto": [
                "Planificado", "En Diseño", "En Ejecución", 
                "En Revisión", "Completado", "Pausado", "Cancelado"
            ],
            # estados_proceso removed - procesos table doesn't have estado column
            "estados_tarea": [
                "Pendiente", "En Progreso", "En Revisión", 
                "Bloqueada", "Completada"
            ],
            "prioridades": [
                "Baja", "Media", "Alta", "Urgente"
            ],
            "urgencias": [
                "Urgente", "No Urgente"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting available filters: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting available filters: {str(e)}"
        )
