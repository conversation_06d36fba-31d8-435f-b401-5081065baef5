import logging
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any # Assuming history might be grouped

from supabase import Client # Import Supabase client type
from app.core.database import get_supabase_client # Import Supabase client dependency
from app.models import chat as chat_models # Import chat models
from app.core.security import get_current_user_id # Import auth dependency
from app.services import thread_service, thread_metadata_service # Import thread services
from uuid import UUID # Import UUID for user_id conversion

logger = logging.getLogger(__name__)
router = APIRouter()

# Use the ThreadSummary model for the response
@router.get("/", response_model=List[chat_models.ThreadSummary])
async def get_user_threads(
    # Remove supabase dependency, service handles it
    # supabase: Client = Depends(get_supabase_client),
    current_user_id: str = Depends(get_current_user_id), # Protect endpoint
    skip: int = 0,
    limit: int = 50 # Limit number of threads returned initially
):
    """
    Retrieve a summary list of conversation threads for the current user,
    ordered by the most recent activity.
    Requires authentication.
    """
    logger.info(f"User {current_user_id} requesting thread list (skip={skip}, limit={limit})")
    try:
        # Convert user_id string from token to UUID for the service function
        user_uuid = UUID(current_user_id)
        # Call the service function
        all_threads = await thread_service.get_threads_for_user(user_id=user_uuid)

        # Apply pagination (slicing the list returned by the service)
        # Note: This is less efficient than DB-level pagination.
        # Consider modifying the service if performance becomes an issue.
        paginated_threads = all_threads[skip : skip + limit]

        logger.info(f"Returning {len(paginated_threads)} thread summaries for user {current_user_id}")
        return paginated_threads

    except HTTPException as http_exc:
        # Re-raise exceptions from the service layer
        raise http_exc
    except ValueError:
        # Handle potential UUID conversion error
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error fetching threads for user {current_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while fetching threads."
        )


# Add response_model_by_alias=True to use 'from' instead of 'from_sender' in output JSON
@router.get("/{thread_id}", response_model=List[chat_models.ChatMessage], response_model_by_alias=True)
async def get_thread_messages(
    thread_id: int,
    # Remove supabase dependency, service handles it
    # supabase: Client = Depends(get_supabase_client),
    current_user_id: str = Depends(get_current_user_id), # Protect endpoint
    limit: int = 50, # Reduced default limit for better performance
    offset: int = 0, # Add offset for pagination
    load_recent: bool = True # Load most recent messages by default
):
    """
    Retrieve all messages for a specific conversation thread.
    Checks if the user is authorized to view this thread before fetching.
    Requires authentication.
    """
    logger.info(f"User {current_user_id} requesting messages for thread {thread_id}")
    try:
        user_uuid = UUID(current_user_id)

        # 1. Check authorization using the service
        is_authorized = await thread_service.check_thread_authorization(
            thread_id=thread_id, user_id=user_uuid
        )

        if not is_authorized:
            logger.warning(f"User {current_user_id} is not authorized to access thread {thread_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this thread",
            )

        # 2. If authorized, fetch messages using the service with pagination
        logger.info(f"User {current_user_id} authorized for thread {thread_id}. Fetching messages (limit={limit}, offset={offset}, load_recent={load_recent}).")
        messages = await thread_service.get_messages_for_thread(
            thread_id=thread_id,
            limit=limit,
            offset=offset,
            load_recent=load_recent
        )

        logger.info(f"Returning {len(messages)} messages for thread {thread_id}")
        return messages

    except HTTPException as http_exc:
        # Re-raise known HTTP exceptions (like 403 Forbidden)
        raise http_exc
    except ValueError:
        # Handle potential UUID conversion error
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error fetching messages for thread {thread_id} for user {current_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while fetching thread messages."
        )


@router.get("/{thread_id}/count", response_model=dict)
async def get_thread_message_count(
    thread_id: int,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Get the total number of messages in a thread.
    Useful for implementing pagination in the frontend.
    """
    logger.info(f"User {current_user_id} requesting message count for thread {thread_id}")
    try:
        user_uuid = UUID(current_user_id)

        # Check authorization
        is_authorized = await thread_service.check_thread_authorization(
            thread_id=thread_id, user_id=user_uuid
        )

        if not is_authorized:
            logger.warning(f"User {current_user_id} is not authorized to access thread {thread_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this thread",
            )

        # Get message count
        count = await thread_service.get_thread_message_count(thread_id=thread_id)

        logger.info(f"Thread {thread_id} has {count} messages")
        return {"thread_id": thread_id, "message_count": count}

    except HTTPException as http_exc:
        raise http_exc
    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error getting message count for thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while getting message count."
        )


@router.get("/{thread_id}/metadata", response_model=chat_models.ThreadMetadata)
async def get_thread_metadata(
    thread_id: int,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Get metadata for a specific thread.
    Requires authentication and thread authorization.
    """
    logger.info(f"User {current_user_id} requesting metadata for thread {thread_id}")
    try:
        user_uuid = UUID(current_user_id)

        # Check authorization
        is_authorized = await thread_service.check_thread_authorization(
            thread_id=thread_id, user_id=user_uuid
        )

        if not is_authorized:
            logger.warning(f"User {current_user_id} is not authorized to access thread {thread_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this thread",
            )

        # Get metadata
        metadata = await thread_metadata_service.get_thread_metadata(thread_id=thread_id)

        if not metadata:
            # Create metadata if it doesn't exist
            metadata = await thread_metadata_service.ensure_thread_metadata_exists(thread_id)

        logger.info(f"Returning metadata for thread {thread_id}")
        return metadata

    except HTTPException as http_exc:
        raise http_exc
    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error getting metadata for thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while getting thread metadata."
        )


@router.put("/{thread_id}/metadata", response_model=chat_models.ThreadMetadata)
async def update_thread_metadata(
    thread_id: int,
    metadata_update: chat_models.ThreadMetadataUpdate,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Update metadata for a specific thread.
    Requires authentication and thread authorization.
    """
    logger.info(f"User {current_user_id} updating metadata for thread {thread_id}")
    try:
        user_uuid = UUID(current_user_id)

        # Check authorization
        is_authorized = await thread_service.check_thread_authorization(
            thread_id=thread_id, user_id=user_uuid
        )

        if not is_authorized:
            logger.warning(f"User {current_user_id} is not authorized to access thread {thread_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this thread",
            )

        # Update metadata
        updated_metadata = await thread_metadata_service.update_thread_metadata(
            thread_id=thread_id, metadata_update=metadata_update
        )

        logger.info(f"Successfully updated metadata for thread {thread_id}")
        return updated_metadata

    except HTTPException as http_exc:
        raise http_exc
    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error updating metadata for thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while updating thread metadata."
        )


@router.delete("/{thread_id}")
async def delete_thread(
    thread_id: int,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Delete an entire conversation thread and all associated data.
    This includes:
    - All messages in the thread (from threads table)
    - Thread metadata (from threads_metadata table)
    - Any other related records

    Requires authentication and thread authorization.
    """
    logger.info(f"User {current_user_id} requesting deletion of thread {thread_id}")
    try:
        user_uuid = UUID(current_user_id)

        # Check authorization
        is_authorized = await thread_service.check_thread_authorization(
            thread_id=thread_id, user_id=user_uuid
        )

        if not is_authorized:
            logger.warning(f"User {current_user_id} is not authorized to delete thread {thread_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this thread",
            )

        # Delete the entire thread using the service
        deleted_count = await thread_service.delete_entire_thread(
            thread_id=thread_id, user_id=user_uuid
        )

        logger.info(f"Successfully deleted thread {thread_id} with {deleted_count} messages for user {current_user_id}")
        return {
            "message": "Thread deleted successfully",
            "thread_id": thread_id,
            "deleted_messages": deleted_count
        }

    except HTTPException as http_exc:
        raise http_exc
    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Unexpected error deleting thread {thread_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while deleting the thread."
        )