import React from 'react';
import { useToast } from '../hooks/useToast';
import ToastContainer from '../components/UI/ToastContainer';
import { ToastContext, ToastContextType } from '../contexts/ToastContext';

interface ToastProviderProps {
  children: React.ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const { toasts, removeToast, success, error, warning, info, clearAll } = useToast();

  const contextValue: ToastContextType = {
    success,
    error,
    warning,
    info,
    clearAll,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </ToastContext.Provider>
  );
};
