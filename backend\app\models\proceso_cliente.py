from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum

from app.models.user import User
from app.models.crm_empresa import Empresa


class UnidadRepeticion(str, Enum):
    """Enum for repetition units."""
    DIARIO = "Diario"
    SEMANAL = "Semanal"
    QUINCENAL = "Quincenal"
    MENSUAL = "Mensual"
    BIMESTRAL = "Bimestral"
    TRIMESTRAL = "Trimestral"
    ANUAL = "Anual"
    PUNTUAL = "Puntual"
    BAJO_DEMANDA = "Bajo Demanda"
    # Legacy values found in database - capitalized
    DIARIA_MULTIPLE = "Diaria (múltiples pedidos al día)"
    DIARIA_30_40 = "Diaria (30-40 pedidos/día)"
    POR_PEDIDO_EDI = "Por pedido EDI"
    # Legacy values found in database - lowercase
    DIARIO_LOWER = "diario"
    SEMANAL_LOWER = "semanal"
    MENSUAL_LOWER = "mensual"
    ANUAL_LOWER = "anual"
    INDEFINIDO = "indefinido"


class ValorNegocioCliente(str, Enum):
    """Enum for business value from client perspective."""
    BAJO = "Bajo"
    MEDIO = "Medio"
    ALTO = "Alto"
    CRITICO = "Crítico"


class ComplejidadAutomatizacion(str, Enum):
    """Enum for automation complexity."""
    BAJA = "Baja"
    MEDIA = "Media"
    MEDIO = "Medio"  # Valor encontrado en BD
    BAJA_MEDIA = "Baja-Media"  # Valor encontrado en BD
    ALTA = "Alta"
    MUY_ALTA = "Muy Alta"


class PrioridadAutomatizacion(str, Enum):
    """Enum for automation priority."""
    BAJA = "Baja"
    MEDIA = "Media"
    MEDIO = "Medio"  # Valor encontrado en BD
    ALTA = "Alta"
    URGENTE = "Urgente"


class EstadoAnalisis(str, Enum):
    """Enum for analysis status."""
    PENDIENTE = "Pendiente"
    EN_ANALISIS = "En Análisis"
    ANALIZADO = "Analizado"
    IMPLEMENTADO = "Implementado"
    DESCARTADO = "Descartado"
    ANALISIS_EXPERTO_REALIZADO = "Análisis Experto Realizado"
    IDENTIFICADO = "identificado"  # Legacy value found in database


# Base Models
class ProcesoClienteBase(BaseModel):
    """Base model for proceso cliente data."""
    nombre: str = Field(..., description="Nombre del proceso del cliente")
    descripcion: Optional[str] = Field(None, description="Descripción del proceso")
    departamento_cliente_id: Optional[UUID] = Field(None, description="Departamento del cliente")
    estado_analisis: Optional[EstadoAnalisis] = Field(EstadoAnalisis.PENDIENTE, description="Estado del análisis")
    es_repetitivo: Optional[bool] = Field(False, description="Si el proceso es repetitivo")
    es_cuello_botella: Optional[bool] = Field(False, description="Si es un cuello de botella")
    es_manual: Optional[bool] = Field(True, description="Si el proceso es manual")
    valor_negocio_cliente: Optional[str] = Field(None, description="Valor de negocio para el cliente")
    complejidad_automatizacion_aceleralia: Optional[str] = Field(None, description="Complejidad de automatización")
    prioridad_automatizacion_aceleralia: Optional[str] = Field(None, description="Prioridad de automatización")
    duracion_minutos_por_ejecucion: Optional[int] = Field(None, description="Duración en minutos por ejecución")
    frecuencia_periodo: Optional[str] = Field(None, description="Periodo de frecuencia")
    frecuencia_ocurrencias: Optional[int] = Field(1, description="Número de ocurrencias por periodo")
    herramientas_utilizadas_cliente: Optional[Union[Dict[str, Any], List[Any]]] = Field(None, description="Herramientas utilizadas")
    reunion_origen_id: Optional[UUID] = Field(None, description="Reunión de origen")
    info_adicional: Optional[str] = Field(None, description="Información adicional")


class ProcesoClienteCreate(ProcesoClienteBase):
    """Model for creating a new proceso cliente."""
    empresa_cliente_id: UUID = Field(..., description="ID de la empresa cliente")


class ProcesoClienteUpdate(BaseModel):
    """Model for updating proceso cliente data."""
    nombre: Optional[str] = None
    descripcion: Optional[str] = None
    departamento_cliente_id: Optional[UUID] = None
    estado_analisis: Optional[EstadoAnalisis] = None
    es_repetitivo: Optional[bool] = None
    es_cuello_botella: Optional[bool] = None
    es_manual: Optional[bool] = None
    valor_negocio_cliente: Optional[str] = None
    complejidad_automatizacion_aceleralia: Optional[str] = None
    prioridad_automatizacion_aceleralia: Optional[str] = None
    duracion_minutos_por_ejecucion: Optional[int] = None
    frecuencia_periodo: Optional[str] = None
    frecuencia_ocurrencias: Optional[int] = None
    herramientas_utilizadas_cliente: Optional[Dict[str, Any]] = None
    info_adicional: Optional[str] = None


class ProcesoClienteInDBBase(ProcesoClienteBase):
    """Base model for proceso cliente data stored in the database."""
    id: UUID
    empresa_cliente_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Response Models
class PersonaResponsable(BaseModel):
    """Model for responsible person data."""
    id: UUID
    nombre: str
    apellidos: Optional[str] = None
    cargo: Optional[str] = None
    departamento_nombre: Optional[str] = None


class DepartamentoInfo(BaseModel):
    """Model for department information."""
    id: UUID
    nombre: str
    descripcion: Optional[str] = None


class ProcesoClienteListItem(ProcesoClienteInDBBase):
    """Model for proceso cliente in list views."""
    # Calculated fields
    tiempo_estimado_horas_mes: Optional[float] = Field(0.0, description="Tiempo estimado normalizado a horas/mes")
    numero_tareas: Optional[int] = Field(0, description="Número de tareas asociadas")
    
    # Related data
    responsable_principal: Optional[PersonaResponsable] = None
    departamento: Optional[DepartamentoInfo] = None
    otros_responsables: Optional[List[PersonaResponsable]] = Field(default_factory=list)


class ProcesoClienteDetalle(ProcesoClienteInDBBase):
    """Model for detailed proceso cliente view."""
    # Related data
    empresa_cliente: Optional[Empresa] = None
    responsable_principal: Optional[PersonaResponsable] = None
    departamento: Optional[DepartamentoInfo] = None
    otros_responsables: Optional[List[PersonaResponsable]] = Field(default_factory=list)
    
    # Calculated fields
    tiempo_estimado_horas_mes: Optional[float] = Field(0.0, description="Tiempo estimado normalizado a horas/mes")
    numero_tareas: Optional[int] = Field(0, description="Número de tareas asociadas")
    
    # Tasks will be loaded separately
    tareas: Optional[List['TareaClienteDetalle']] = Field(default_factory=list)


# List Response Models
class ProcesoClienteListResponse(BaseModel):
    """Response model for proceso cliente list endpoints."""
    procesos: List[ProcesoClienteListItem]
    total: int
    tiempo_total_horas_mes: float = Field(0.0, description="Tiempo total estimado de todos los procesos")
    total_tareas: int = Field(0, description="Total de tareas de todos los procesos")


# Filter Models
class ProcesoClienteFilters(BaseModel):
    """Model for proceso cliente filters."""
    responsable_ids: Optional[List[UUID]] = Field(None, description="IDs de responsables (principal + otros + tareas)")
    departamento_id: Optional[UUID] = None
    es_cuello_botella: Optional[bool] = None
    valor_negocio_cliente: Optional[str] = None
    es_manual: Optional[bool] = None
    es_repetitivo: Optional[bool] = None
    complejidad_automatizacion_aceleralia: Optional[str] = None
    prioridad_automatizacion_aceleralia: Optional[str] = None
    search: Optional[str] = Field(None, description="Búsqueda en nombre y descripción")


class ProcesoClienteGroupBy(str, Enum):
    """Enum for grouping options."""
    RESPONSABLE_PRINCIPAL = "responsable_principal"
    DEPARTAMENTO = "departamento"
    PRIORIDAD_AUTOMATIZACION = "prioridad_automatizacion"


# Constants for time calculation
FACTOR_CONVERSION_MENSUAL = {
    UnidadRepeticion.DIARIO: 20,  # días laborables/mes
    UnidadRepeticion.SEMANAL: 4,
    UnidadRepeticion.QUINCENAL: 2,
    UnidadRepeticion.MENSUAL: 1,
    UnidadRepeticion.BIMESTRAL: 0.5,
    UnidadRepeticion.TRIMESTRAL: 1/3,
    UnidadRepeticion.ANUAL: 1/12,
    UnidadRepeticion.PUNTUAL: 0,  # No se incluye en cálculos recurrentes
    UnidadRepeticion.BAJO_DEMANDA: 0,  # No se incluye en cálculos recurrentes
    # Legacy values - treat as daily
    UnidadRepeticion.DIARIA_MULTIPLE: 20,  # días laborables/mes
    UnidadRepeticion.DIARIA_30_40: 20,  # días laborables/mes
    UnidadRepeticion.POR_PEDIDO_EDI: 20,  # días laborables/mes (assuming daily)
    # Legacy values - lowercase
    UnidadRepeticion.DIARIO_LOWER: 20,  # días laborables/mes
    UnidadRepeticion.SEMANAL_LOWER: 4,
    UnidadRepeticion.MENSUAL_LOWER: 1,
    UnidadRepeticion.ANUAL_LOWER: 1/12,
    UnidadRepeticion.INDEFINIDO: 0,  # No se incluye en cálculos recurrentes
}


# Forward reference resolution
from app.models.tarea_cliente import TareaClienteDetalle
ProcesoClienteDetalle.model_rebuild()
