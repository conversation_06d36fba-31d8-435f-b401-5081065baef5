import React, { useState, useRef, useEffect } from 'react';

interface SelectOption {
  value: string;
  label: string;
}

type EditableFieldValue = string | number | boolean | null;

interface EditableFieldProps {
  label: string;
  value: EditableFieldValue;
  type: 'text' | 'textarea' | 'number' | 'select' | 'date' | 'email' | 'tel' | 'checkbox';
  options?: SelectOption[];
  onSave: (value: EditableFieldValue) => Promise<void> | void;
  className?: string;
  renderValue?: (value: EditableFieldValue) => React.ReactNode;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  min?: number;
  max?: number;
  rows?: number;
  enableDoubleClick?: boolean; // Para activar edición con doble clic
  showEditIcon?: boolean; // Para mostrar/ocultar el icono de edición
  autoSave?: boolean; // Para guardar automáticamente al perder el foco
  validation?: (value: EditableFieldValue) => string | null; // Función de validación personalizada
}

export const EditableField: React.FC<EditableFieldProps> = ({
  label,
  value,
  type,
  options = [],
  onSave,
  className = '',
  renderValue,
  placeholder,
  disabled = false,
  required = false,
  min,
  max,
  rows = 3,
  enableDoubleClick = true,
  showEditIcon = true,
  autoSave = false,
  validation,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>(null);

  // Update edit value when prop value changes
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
    }
  }, [value, isEditing]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (type === 'text' || type === 'textarea') {
        (inputRef.current as HTMLInputElement | HTMLTextAreaElement).select();
      }
    }
  }, [isEditing, type]);

  const handleStartEdit = () => {
    if (disabled) return;
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
    setError(null);
  };

  const handleSave = async () => {
    // Validación personalizada
    if (validation) {
      const validationError = validation(editValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    // Validación requerida
    if (
      required &&
      (editValue === null ||
        editValue === undefined ||
        (typeof editValue === 'string' && editValue.trim() === ''))
    ) {
      setError('Este campo es requerido');
      return;
    }

    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      let valueToSave = editValue;

      // Conversión de tipos según el tipo de campo
      if (type === 'number' && typeof editValue === 'string') {
        valueToSave = editValue === '' ? null : Number(editValue);
      } else if (type === 'checkbox') {
        valueToSave = Boolean(editValue);
      }

      await onSave(valueToSave);
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al guardar');
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    if (autoSave && isEditing) {
      handleSave();
    }
  };

  const handleDoubleClick = () => {
    if (enableDoubleClick && !disabled) {
      handleStartEdit();
    }
  };

  const renderInput = () => {
    const getDisplayValue = () => {
      if (type === 'checkbox') {
        return Boolean(editValue);
      }
      if (editValue === null || editValue === undefined) {
        return '';
      }
      return String(editValue);
    };

    const commonProps = {
      ref: inputRef as React.RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
      value: getDisplayValue(),
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const target = e.target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        const newValue = target.type === 'checkbox' ? (target as HTMLInputElement).checked : target.value;
        setEditValue(newValue);
        setError(null); // Limpiar error al cambiar valor
      },
      onKeyDown: handleKeyDown,
      onBlur: handleBlur,
      className: "w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500",
      placeholder,
      required,
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            ref={inputRef as React.RefObject<HTMLTextAreaElement>}
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
            rows={rows}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                handleSave();
              } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
              }
            }}
          />
        );
      case 'number':
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="number"
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
            min={min}
            max={max}
          />
        );
      case 'date':
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="date"
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
          />
        );
      case 'email':
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="email"
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
          />
        );
      case 'tel':
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="tel"
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
          />
        );
      case 'checkbox':
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="checkbox"
            checked={Boolean(editValue)}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            placeholder={commonProps.placeholder}
            required={commonProps.required}
          />
        );
      case 'select':
        return (
          <select
            ref={inputRef as React.RefObject<HTMLSelectElement>}
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            required={commonProps.required}
          >
            <option value="">Seleccionar...</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="text"
            value={String(editValue ?? '')}
            onChange={commonProps.onChange}
            onKeyDown={commonProps.onKeyDown}
            onBlur={commonProps.onBlur}
            className={commonProps.className}
            placeholder={commonProps.placeholder}
            required={commonProps.required}
          />
        );
    }
  };

  const renderDisplayValue = () => {
    if (renderValue) {
      return renderValue(value);
    }

    // Manejo de diferentes tipos de valores
    if (type === 'checkbox') {
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Sí' : 'No'}
        </span>
      );
    }

    if (type === 'date' && value) {
      const date = new Date(value as string);
      return <span>{date.toLocaleDateString('es-ES')}</span>;
    }

    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return <span className="text-gray-400 italic">{placeholder || 'Sin valor'}</span>;
    }

    return <span>{String(value)}</span>;
  };

  if (isEditing) {
    return (
      <div className={`space-y-2 ${className}`}>
        <label className="block text-xs font-medium text-gray-700">
          {label}
        </label>
        <div className="space-y-2">
          {renderInput()}
          {error && (
            <p className="text-xs text-red-600">{error}</p>
          )}
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? 'Guardando...' : 'Guardar'}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="px-2 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`group ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            {label}
          </label>
          <div
            onClick={enableDoubleClick ? undefined : handleStartEdit}
            onDoubleClick={handleDoubleClick}
            className={`p-2 rounded border border-transparent transition-colors ${
              disabled
                ? 'cursor-not-allowed opacity-50'
                : enableDoubleClick
                  ? 'cursor-pointer hover:border-gray-300 hover:bg-gray-50'
                  : 'cursor-pointer hover:border-gray-300 hover:bg-gray-50'
            }`}
            title={enableDoubleClick ? 'Doble clic para editar' : 'Clic para editar'}
          >
            {renderDisplayValue()}
          </div>
        </div>
        {!disabled && showEditIcon && (
          <button
            onClick={handleStartEdit}
            className="opacity-0 group-hover:opacity-100 ml-2 p-1 text-gray-400 hover:text-gray-600 transition-opacity"
            title={`Editar ${label}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};
