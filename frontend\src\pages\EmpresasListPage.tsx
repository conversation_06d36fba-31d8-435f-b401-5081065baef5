/**
 * EmpresasListPage - Lista general de empresas
 * Módulo 1.1: Implementa la pantalla inicial de la sección "Empresas"
 */

import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useEmpresas } from '../hooks/useEmpresas';
import EmpresaCreateForm from '../components/CRM/EmpresaCreateForm';
import Modal from '../components/UI/Modal';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import type { Empresa, EmpresaCreate, TipoRelacion } from '../types/empresa';

const EmpresasListPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTipoRelacion, setSelectedTipoRelacion] = useState<TipoRelacion | ''>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  const {
    empresas,
    loading,
    error,
    tiposRelacion,
    createEmpresa,
    clearError
  } = useEmpresas({
    autoFetch: true,
    activo: true
  });
  
  // Filter empresas based on search and filters
  const filteredEmpresas = useMemo(() => {
    return empresas.filter(empresa => {
      const matchesSearch = !searchTerm || 
        empresa.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (empresa.nif_cif && empresa.nif_cif.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesTipoRelacion = !selectedTipoRelacion || 
        empresa.tipo_relacion === selectedTipoRelacion;
      
      return matchesSearch && matchesTipoRelacion;
    });
  }, [empresas, searchTerm, selectedTipoRelacion]);
  
  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle filter change
  const handleTipoRelacionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    // Validate the value before casting to ensure type safety
    if (value === '' || tiposRelacion.includes(value as TipoRelacion)) {
      setSelectedTipoRelacion(value as TipoRelacion | '');
    } else {
      console.warn('Invalid tipo relacion value:', value);
      setSelectedTipoRelacion('');
    }
  };
  
  // Handle empresa click - navigate to detail view
  const handleEmpresaClick = (empresa: Empresa) => {
    navigate(`/crm/empresas/${empresa.id}`);
  };
  
  // Handle create empresa
  const handleCreateEmpresa = async (data: EmpresaCreate) => {
    try {
      const newEmpresa = await createEmpresa(data);
      if (newEmpresa) {
        setShowCreateModal(false);
        // Optionally navigate to the new empresa detail
        // navigate(`/crm/empresas/${newEmpresa.id}`);
      } else {
        // Handle case where createEmpresa returns null (error occurred)
        console.error('Failed to create empresa: createEmpresa returned null');
        // Error is already handled by the useEmpresas hook and displayed in the UI
      }
    } catch (err) {
      // Additional error handling if needed
      console.error('Unexpected error in handleCreateEmpresa:', err);
    }
  };
  
  // Handle create modal close
  const handleCreateModalClose = () => {
    setShowCreateModal(false);
    clearError();
  };
  
  // Get tipo relacion display text
  const getTipoRelacionDisplay = (tipo: string | null | undefined) => {
    if (!tipo) return '-';
    return tipo;
  };
  
  // Get tipo relacion badge color
  const getTipoRelacionBadgeColor = (tipo: string | null | undefined) => {
    switch (tipo) {
      case 'Cliente':
        return 'bg-green-100 text-green-800';
      case 'Colaborador':
        return 'bg-blue-100 text-blue-800';
      case 'Otro':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-500';
    }
  };
  
  if (loading && empresas.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Empresas</h1>
          <p className="text-gray-600">
            Gestiona las empresas registradas en el sistema
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Nueva Empresa
        </button>
      </div>
      
      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <label htmlFor="search" className="sr-only">Buscar empresas</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                id="search"
                type="text"
                placeholder="Buscar por nombre o NIF/CIF..."
                value={searchTerm}
                onChange={handleSearch}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          {/* Tipo Relacion Filter */}
          <div className="sm:w-48">
            <label htmlFor="tipo-relacion" className="sr-only">Filtrar por tipo de relación</label>
            <select
              id="tipo-relacion"
              value={selectedTipoRelacion}
              onChange={handleTipoRelacionChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Todos los tipos</option>
              {tiposRelacion.map(tipo => (
                <option key={tipo} value={tipo}>{tipo}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="inline-flex text-red-400 hover:text-red-600"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {loading ? (
          <span>Cargando empresas...</span>
        ) : (
          <span>
            Mostrando {filteredEmpresas.length} de {empresas.length} empresas
          </span>
        )}
      </div>
      
      {/* Empresas Table */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {filteredEmpresas.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H7m2 0v-4a2 2 0 012-2h2a2 2 0 012 2v4" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hay empresas</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedTipoRelacion 
                ? 'No se encontraron empresas con los filtros aplicados.'
                : 'Comienza creando tu primera empresa.'
              }
            </p>
            {!searchTerm && !selectedTipoRelacion && (
              <div className="mt-6">
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Nueva Empresa
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Empresa
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo de Relación
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sector
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contacto
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEmpresas.map((empresa) => (
                  <tr
                    key={empresa.id}
                    onClick={() => handleEmpresaClick(empresa)}
                    className="hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {empresa.nombre}
                        </div>
                        {empresa.nif_cif && (
                          <div className="text-sm text-gray-500">
                            {empresa.nif_cif}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTipoRelacionBadgeColor(empresa.tipo_relacion)}`}>
                        {getTipoRelacionDisplay(empresa.tipo_relacion)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {empresa.sector || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {empresa.email_principal || empresa.telefono || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Create Empresa Modal */}
      {showCreateModal && (
        <Modal
          isOpen={showCreateModal}
          onClose={handleCreateModalClose}
          title="Nueva Empresa"
          size="lg"
        >
          <EmpresaCreateForm
            onSubmitSuccess={handleCreateEmpresa}
            onCancel={handleCreateModalClose}
          />
        </Modal>
      )}
    </div>
  );
};

export default EmpresasListPage;
