from fastapi import HTTPException, status
from uuid import UUID, uuid4
import datetime
from typing import List, Dict, Any, Optional

from app.models.tarea import (
    TareaCreate, TareaUpdate, Tarea, TareaSummary, TareaMatrix,
    TareaListResponse, TareaFilters, TareaKanbanBoard, TareaKanbanColumn,
    EstadoTarea, PrioridadTarea, UrgenciaTarea
)
from app.core.database import get_supabase_client

async def create_tarea(tarea_in: TareaCreate, user_id: UUID) -> Tarea:
    """Create a new tarea."""
    supabase = await get_supabase_client()

    # Generate UUID for the new tarea
    tarea_id = uuid4()

    # Prepare tarea data
    tarea_data = {
        "id": str(tarea_id),
        "titulo": tarea_in.titulo,
        "descripcion": tarea_in.descripcion,
        "proyecto_id": str(tarea_in.proyecto_id) if tarea_in.proyecto_id else None,
        "workflow_id": str(tarea_in.workflow_id) if tarea_in.workflow_id else None,
        "estado": tarea_in.estado.value,
        "prioridad": tarea_in.prioridad.value,
        "urgencia": tarea_in.urgencia.value,
        "fecha_vencimiento": tarea_in.fecha_vencimiento.isoformat() if tarea_in.fecha_vencimiento else None,
        "fecha_completado": tarea_in.fecha_completado.isoformat() if tarea_in.fecha_completado else None,
        "asignado_a": str(tarea_in.asignado_a) if tarea_in.asignado_a else None,
        "creado_por": str(user_id),
        "tarea_padre_id": str(tarea_in.tarea_padre_id) if tarea_in.tarea_padre_id else None,
        "info_adicional": tarea_in.info_adicional,
        "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }

    try:
        # Insert tarea
        result = supabase.table('tareas').insert(tarea_data).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create tarea"
            )

        # Create empresa associations if provided
        if tarea_in.empresas_asociadas_ids:
            empresa_links = []
            for empresa_id in tarea_in.empresas_asociadas_ids:
                empresa_links.append({
                    "tarea_id": str(tarea_id),
                    "empresa_id": str(empresa_id)
                })

            if empresa_links:
                supabase.table('tareas_empresas').insert(empresa_links).execute()

        # Create etiqueta associations if provided
        if tarea_in.etiquetas_ids:
            etiqueta_links = []
            for etiqueta_id in tarea_in.etiquetas_ids:
                etiqueta_links.append({
                    "tarea_id": str(tarea_id),
                    "etiqueta_id": str(etiqueta_id)
                })

            if etiqueta_links:
                supabase.table('tareas_etiquetas').insert(etiqueta_links).execute()

        # Fetch and return the created tarea
        return await get_tarea_by_id(tarea_id)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating tarea: {str(e)}"
        )

async def get_tarea_by_id(tarea_id: UUID) -> Tarea:
    """Get a tarea by ID with related data."""
    supabase = await get_supabase_client()

    try:
        # Get tarea with related data
        result = supabase.table('tareas').select(
            """
            *,
            asignado_usuario:usuarios!asignado_a(id, nombre, email),
            creador_usuario:usuarios!creado_por(id, nombre, email),
            proyecto:proyectos!proyecto_id(id, nombre),
            tarea_padre:tareas!tarea_padre_id(titulo)
            """
        ).eq('id', str(tarea_id)).maybe_single().execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tarea not found"
            )

        tarea_data = result.data

        # Get subtareas
        subtareas_result = supabase.table('tareas').select(
            """
            id, titulo, estado, prioridad, urgencia, fecha_vencimiento,
            asignado_a, usuarios!tareas_asignado_a_fkey(nombre)
            """
        ).eq('tarea_padre_id', str(tarea_id)).execute()

        # Get empresas asociadas directly to the task
        empresas_result = supabase.table('tareas_empresas').select(
            "empresas(id, nombre)"
        ).eq('tarea_id', str(tarea_id)).execute()

        empresas_asociadas = []
        if empresas_result.data:
            empresas_asociadas = [e['empresas'] for e in empresas_result.data if e.get('empresas')]

        # Get empresas asociadas through proyecto if task has a project
        proyecto_empresas = []
        if tarea_data.get('proyecto_id'):
            proyecto_empresas_result = supabase.table('proyectos_empresas').select(
                "empresas(id, nombre, tipo_relacion)"
            ).eq('proyecto_id', str(tarea_data['proyecto_id'])).execute()
            if proyecto_empresas_result.data:
                proyecto_empresas = [item['empresas'] for item in proyecto_empresas_result.data if item.get('empresas')]

        # Combine direct and project empresas
        all_empresas = empresas_asociadas + proyecto_empresas
        # Remove duplicates based on empresa id
        unique_empresas = []
        seen_ids = set()
        for empresa in all_empresas:
            if empresa and empresa.get('id') not in seen_ids:
                unique_empresas.append(empresa)
                seen_ids.add(empresa['id'])

        # Get etiquetas
        etiquetas_result = supabase.table('tareas_etiquetas').select(
            "etiquetas(id, nombre, color)"
        ).eq('tarea_id', str(tarea_id)).execute()

        etiquetas = []
        if etiquetas_result.data:
            etiquetas = [e['etiquetas'] for e in etiquetas_result.data if e.get('etiquetas')]

        # Calculate days until due date
        dias_vencimiento = None
        es_vencida = False
        if tarea_data.get('fecha_vencimiento'):
            fecha_vencimiento = datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date()
            today = datetime.date.today()
            dias_vencimiento = (fecha_vencimiento - today).days
            es_vencida = dias_vencimiento < 0 and tarea_data.get('estado') != 'Completada'

        # Build response
        tarea = Tarea(
            id=UUID(tarea_data['id']),
            titulo=tarea_data['titulo'],
            descripcion=tarea_data.get('descripcion'),
            proyecto_id=UUID(tarea_data['proyecto_id']) if tarea_data.get('proyecto_id') else None,
            workflow_id=UUID(tarea_data['workflow_id']) if tarea_data.get('workflow_id') else None,
            estado=EstadoTarea(tarea_data.get('estado') or 'Pendiente'),
            prioridad=PrioridadTarea(tarea_data.get('prioridad') or 'Media'),
            urgencia=UrgenciaTarea(tarea_data.get('urgencia') or 'No Urgente'),
            fecha_vencimiento=datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date() if tarea_data.get('fecha_vencimiento') else None,
            fecha_completado=datetime.datetime.fromisoformat(tarea_data['fecha_completado']).date() if tarea_data.get('fecha_completado') else None,
            asignado_a=UUID(tarea_data['asignado_a']) if tarea_data.get('asignado_a') else None,
            creado_por=UUID(tarea_data['creado_por']) if tarea_data.get('creado_por') else None,
            tarea_padre_id=UUID(tarea_data['tarea_padre_id']) if tarea_data.get('tarea_padre_id') else None,
            info_adicional=tarea_data.get('info_adicional'),
            created_at=datetime.datetime.fromisoformat(tarea_data['created_at']),
            updated_at=datetime.datetime.fromisoformat(tarea_data['updated_at']),
            proyecto_nombre=tarea_data.get('proyecto', {}).get('nombre') if tarea_data.get('proyecto') else None,
            tarea_padre=tarea_data.get('tarea_padre', {}).get('titulo') if tarea_data.get('tarea_padre') else None,
            empresas_asociadas=unique_empresas,
            etiquetas=etiquetas,
            dias_vencimiento=dias_vencimiento,
            es_vencida=es_vencida
        )

        return tarea

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching tarea: {str(e)}"
        )

async def get_tareas(
    skip: int = 0,
    limit: int = 100,
    filters: Optional[TareaFilters] = None
) -> TareaListResponse:
    """Get list of tareas with filtering."""
    supabase = await get_supabase_client()

    try:
        # Build query - exclude subtareas (tareas with tarea_padre_id)
        query = supabase.table('tareas').select(
            """
            *,
            asignado_usuario:usuarios!asignado_a(id, nombre),
            proyecto:proyectos!proyecto_id(id, nombre)
            """,
            count='exact'
        ).is_('tarea_padre_id', 'null')

        # Apply filters
        if filters:
            if filters.estado:
                query = query.eq('estado', filters.estado.value)
            if filters.prioridad:
                query = query.eq('prioridad', filters.prioridad.value)
            if filters.urgencia:
                query = query.eq('urgencia', filters.urgencia.value)
            if filters.proyecto_id:
                query = query.eq('proyecto_id', str(filters.proyecto_id))
            if filters.asignado_a:
                query = query.eq('asignado_a', str(filters.asignado_a))
            if filters.empresa_id:
                # Filter by empresa through proyecto relationship
                proyectos_with_empresa = supabase.table('proyectos_empresas').select('proyecto_id').eq('empresa_id', str(filters.empresa_id)).execute()
                if proyectos_with_empresa.data:
                    proyecto_ids = [p['proyecto_id'] for p in proyectos_with_empresa.data]
                    query = query.in_('proyecto_id', proyecto_ids)
                else:
                    # No projects with this empresa, return empty result
                    query = query.eq('id', 'non-existent-id')
            if filters.vencidas is not None:
                if filters.vencidas:
                    today = datetime.date.today().isoformat()
                    query = query.lt('fecha_vencimiento', today).neq('estado', 'Completada')
                else:
                    today = datetime.date.today().isoformat()
                    query = query.or_(f'fecha_vencimiento.gte.{today},fecha_vencimiento.is.null,estado.eq.Completada')
            if filters.search:
                query = query.or_(f'titulo.ilike.%{filters.search}%,descripcion.ilike.%{filters.search}%')

        # Apply pagination
        query = query.range(skip, skip + limit - 1).order('created_at', desc=True)

        result = query.execute()

        tareas = []
        for tarea_data in result.data or []:
            # Calculate days until due date
            dias_vencimiento = None
            es_vencida = False
            if tarea_data.get('fecha_vencimiento'):
                fecha_vencimiento = datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date()
                today = datetime.date.today()
                dias_vencimiento = (fecha_vencimiento - today).days
                es_vencida = dias_vencimiento < 0 and tarea_data.get('estado') != 'Completada'

            tarea = Tarea(
                id=UUID(tarea_data['id']),
                titulo=tarea_data['titulo'],
                descripcion=tarea_data.get('descripcion'),
                proyecto_id=UUID(tarea_data['proyecto_id']) if tarea_data.get('proyecto_id') else None,
                workflow_id=UUID(tarea_data['workflow_id']) if tarea_data.get('workflow_id') else None,
                estado=EstadoTarea(tarea_data.get('estado') or 'Pendiente'),
                prioridad=PrioridadTarea(tarea_data.get('prioridad') or 'Media'),
                urgencia=UrgenciaTarea(tarea_data.get('urgencia') or 'No Urgente'),
                fecha_vencimiento=datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date() if tarea_data.get('fecha_vencimiento') else None,
                fecha_completado=datetime.datetime.fromisoformat(tarea_data['fecha_completado']).date() if tarea_data.get('fecha_completado') else None,
                asignado_a=UUID(tarea_data['asignado_a']) if tarea_data.get('asignado_a') else None,
                creado_por=UUID(tarea_data['creado_por']) if tarea_data.get('creado_por') else None,
                tarea_padre_id=UUID(tarea_data['tarea_padre_id']) if tarea_data.get('tarea_padre_id') else None,
                info_adicional=tarea_data.get('info_adicional'),
                created_at=datetime.datetime.fromisoformat(tarea_data['created_at']),
                updated_at=datetime.datetime.fromisoformat(tarea_data['updated_at']),
                proyecto_nombre=tarea_data.get('proyecto', {}).get('nombre') if tarea_data.get('proyecto') else None,
                dias_vencimiento=dias_vencimiento,
                es_vencida=es_vencida
            )
            tareas.append(tarea)

        return TareaListResponse(
            tareas=tareas,
            total=result.count or 0,
            page=skip // limit + 1,
            size=limit
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching tareas: {str(e)}"
        )

async def update_tarea(tarea_id: UUID, tarea_update: TareaUpdate) -> Tarea:
    """Update a tarea."""
    supabase = await get_supabase_client()

    try:
        # Prepare update data
        update_data = {}
        if tarea_update.titulo is not None:
            update_data['titulo'] = tarea_update.titulo
        if tarea_update.descripcion is not None:
            update_data['descripcion'] = tarea_update.descripcion
        if tarea_update.proyecto_id is not None:
            update_data['proyecto_id'] = str(tarea_update.proyecto_id)
        if tarea_update.workflow_id is not None:
            update_data['workflow_id'] = str(tarea_update.workflow_id)
        if tarea_update.estado is not None:
            update_data['estado'] = tarea_update.estado.value
            # Auto-set completion date if marking as completed
            if tarea_update.estado == EstadoTarea.COMPLETADA:
                update_data['fecha_completado'] = datetime.date.today().isoformat()
        if tarea_update.prioridad is not None:
            update_data['prioridad'] = tarea_update.prioridad.value
        if tarea_update.urgencia is not None:
            update_data['urgencia'] = tarea_update.urgencia.value
        if tarea_update.fecha_vencimiento is not None:
            update_data['fecha_vencimiento'] = tarea_update.fecha_vencimiento.isoformat()
        if tarea_update.asignado_a is not None:
            update_data['asignado_a'] = str(tarea_update.asignado_a)
        if tarea_update.tarea_padre_id is not None:
            update_data['tarea_padre_id'] = str(tarea_update.tarea_padre_id)
        if tarea_update.info_adicional is not None:
            update_data['info_adicional'] = tarea_update.info_adicional

        update_data['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        # Update tarea
        result = supabase.table('tareas').update(update_data).eq('id', str(tarea_id)).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tarea not found"
            )

        return await get_tarea_by_id(tarea_id)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating tarea: {str(e)}"
        )

async def delete_tarea(tarea_id: UUID) -> Dict[str, str]:
    """Delete a tarea."""
    supabase = await get_supabase_client()

    try:
        # Check if tarea exists
        existing = supabase.table('tareas').select('id').eq('id', str(tarea_id)).maybe_single().execute()

        if not existing.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tarea not found"
            )

        # Delete tarea (cascade will handle related records)
        supabase.table('tareas').delete().eq('id', str(tarea_id)).execute()

        return {"message": "Tarea deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting tarea: {str(e)}"
        )

async def get_tareas_matrix_dashboard() -> TareaMatrix:
    """Get tareas organized in importance/urgency matrix for dashboard."""
    supabase = await get_supabase_client()

    try:
        # Get all active tareas (not completed) - exclude subtareas
        result = supabase.table('tareas').select(
            """
            id, titulo, estado, prioridad, urgencia, fecha_vencimiento, asignado_a,
            proyecto_id, asignado_usuario:usuarios!asignado_a(nombre), proyecto:proyectos!proyecto_id(nombre)
            """
        ).neq('estado', 'Completada').is_('tarea_padre_id', 'null').execute()

        # Initialize matrix
        matrix = TareaMatrix()

        for tarea_data in result.data or []:
            # Calculate days until due date
            dias_vencimiento = None
            es_vencida = False
            if tarea_data.get('fecha_vencimiento'):
                fecha_vencimiento = datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date()
                today = datetime.date.today()
                dias_vencimiento = (fecha_vencimiento - today).days
                es_vencida = dias_vencimiento < 0

            tarea_summary = TareaSummary(
                id=UUID(tarea_data['id']),
                titulo=tarea_data['titulo'],
                estado=EstadoTarea(tarea_data.get('estado') or 'Pendiente'),
                prioridad=PrioridadTarea(tarea_data.get('prioridad') or 'Media'),
                urgencia=UrgenciaTarea(tarea_data.get('urgencia') or 'No Urgente'),
                fecha_vencimiento=datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date() if tarea_data.get('fecha_vencimiento') else None,
                asignado_a=UUID(tarea_data['asignado_a']) if tarea_data.get('asignado_a') else None,
                asignado_nombre=tarea_data.get('asignado_usuario', {}).get('nombre') if tarea_data.get('asignado_usuario') else None,
                proyecto_id=UUID(tarea_data['proyecto_id']) if tarea_data.get('proyecto_id') else None,
                proyecto_nombre=tarea_data.get('proyecto', {}).get('nombre') if tarea_data.get('proyecto') else None,
                dias_vencimiento=dias_vencimiento,
                es_vencida=es_vencida
            )

            # Classify into matrix quadrants
            is_important = tarea_summary.prioridad in [PrioridadTarea.ALTA, PrioridadTarea.URGENTE]
            is_urgent = tarea_summary.urgencia == UrgenciaTarea.URGENTE or es_vencida

            if is_urgent and is_important:
                matrix.urgente_importante.append(tarea_summary)
            elif not is_urgent and is_important:
                matrix.no_urgente_importante.append(tarea_summary)
            elif is_urgent and not is_important:
                matrix.urgente_no_importante.append(tarea_summary)
            else:
                matrix.no_urgente_no_importante.append(tarea_summary)

        # Limit each quadrant to avoid overwhelming the dashboard
        matrix.urgente_importante = matrix.urgente_importante[:8]
        matrix.no_urgente_importante = matrix.no_urgente_importante[:8]
        matrix.urgente_no_importante = matrix.urgente_no_importante[:6]
        matrix.no_urgente_no_importante = matrix.no_urgente_no_importante[:4]

        return matrix

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching tareas matrix: {str(e)}"
        )

async def get_tareas_kanban(
    filters: Optional[TareaFilters] = None
) -> TareaKanbanBoard:
    """Get tareas organized in Kanban board format."""
    supabase = await get_supabase_client()

    try:
        # Build query - exclude subtareas (tareas with tarea_padre_id)
        query = supabase.table('tareas').select(
            """
            id, titulo, estado, prioridad, urgencia, fecha_vencimiento, asignado_a,
            proyecto_id, asignado_usuario:usuarios!asignado_a(nombre), proyecto:proyectos!proyecto_id(nombre)
            """
        ).is_('tarea_padre_id', 'null')

        # Apply filters
        if filters:
            if filters.estado:
                query = query.eq('estado', filters.estado.value)
            if filters.prioridad:
                query = query.eq('prioridad', filters.prioridad.value)
            if filters.urgencia:
                query = query.eq('urgencia', filters.urgencia.value)
            if filters.proyecto_id:
                query = query.eq('proyecto_id', str(filters.proyecto_id))
            if filters.asignado_a:
                query = query.eq('asignado_a', str(filters.asignado_a))
            if filters.empresa_id:
                # Filter by empresa through proyecto relationship
                proyectos_with_empresa = supabase.table('proyectos_empresas').select('proyecto_id').eq('empresa_id', str(filters.empresa_id)).execute()
                if proyectos_with_empresa.data:
                    proyecto_ids = [p['proyecto_id'] for p in proyectos_with_empresa.data]
                    query = query.in_('proyecto_id', proyecto_ids)
                else:
                    # No projects with this empresa, return empty result
                    query = query.eq('id', 'non-existent-id')
            if filters.vencidas is not None:
                if filters.vencidas:
                    today = datetime.date.today().isoformat()
                    query = query.lt('fecha_vencimiento', today).neq('estado', 'Completada')
                else:
                    today = datetime.date.today().isoformat()
                    query = query.or_(f'fecha_vencimiento.gte.{today},fecha_vencimiento.is.null,estado.eq.Completada')
            if filters.search:
                query = query.or_(f'titulo.ilike.%{filters.search}%,descripcion.ilike.%{filters.search}%')

        result = query.execute()

        # Group tareas by estado
        columns_data = {}
        for estado in EstadoTarea:
            columns_data[estado] = []

        total_tareas = 0
        for tarea_data in result.data or []:
            total_tareas += 1

            # Calculate days until due date
            dias_vencimiento = None
            es_vencida = False
            if tarea_data.get('fecha_vencimiento'):
                fecha_vencimiento = datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date()
                today = datetime.date.today()
                dias_vencimiento = (fecha_vencimiento - today).days
                es_vencida = dias_vencimiento < 0 and tarea_data.get('estado') != 'Completada'

            tarea_summary = TareaSummary(
                id=UUID(tarea_data['id']),
                titulo=tarea_data['titulo'],
                estado=EstadoTarea(tarea_data.get('estado') or 'Pendiente'),
                prioridad=PrioridadTarea(tarea_data.get('prioridad') or 'Media'),
                urgencia=UrgenciaTarea(tarea_data.get('urgencia') or 'No Urgente'),
                fecha_vencimiento=datetime.datetime.fromisoformat(tarea_data['fecha_vencimiento']).date() if tarea_data.get('fecha_vencimiento') else None,
                asignado_a=UUID(tarea_data['asignado_a']) if tarea_data.get('asignado_a') else None,
                asignado_nombre=tarea_data.get('asignado_usuario', {}).get('nombre') if tarea_data.get('asignado_usuario') else None,
                proyecto_id=UUID(tarea_data['proyecto_id']) if tarea_data.get('proyecto_id') else None,
                proyecto_nombre=tarea_data.get('proyecto', {}).get('nombre') if tarea_data.get('proyecto') else None,
                dias_vencimiento=dias_vencimiento,
                es_vencida=es_vencida
            )

            estado = EstadoTarea(tarea_data.get('estado') or 'Pendiente')
            columns_data[estado].append(tarea_summary)

        # Build columns
        columnas = []
        for estado in EstadoTarea:
            columnas.append(TareaKanbanColumn(
                estado=estado,
                tareas=columns_data[estado],
                total=len(columns_data[estado])
            ))

        return TareaKanbanBoard(
            columnas=columnas,
            total_tareas=total_tareas
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching tareas kanban: {str(e)}"
        )
