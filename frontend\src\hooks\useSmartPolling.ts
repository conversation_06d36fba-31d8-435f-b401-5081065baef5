import { useRef, useCallback, useEffect } from 'react';

interface UseSmartPollingOptions {
  enabled: boolean;
  url: string;
  expectedValue: unknown;
  checkValue: (data: unknown) => unknown;
  onSuccess: (data: unknown) => void;
  onError?: (error: Error) => void;
  intervalMs?: number;
  maxAttempts?: number;
  headers?: Record<string, string>;
}

export function useSmartPolling({
  enabled,
  url,
  expectedValue,
  checkValue,
  onSuccess,
  onError,
  intervalMs = 3000,
  maxAttempts = 120, // 6 minutes with 3s interval
  headers = {},
}: UseSmartPollingOptions) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const attemptsRef = useRef(0);
  const isPollingRef = useRef(false);
  
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    isPollingRef.current = false;
    attemptsRef.current = 0;
  }, []);
  
  const poll = useCallback(async () => {
    if (!isPollingRef.current || !enabled) { // Added !enabled check here
        if (isPollingRef.current) { // If it was polling but now disabled, stop it
            stopPolling();
        }
        return;
    }
    
    try {
      attemptsRef.current++;
      
      if (attemptsRef.current > maxAttempts) {
        console.error('[useSmartPolling] Max attempts reached');
        stopPolling();
        onError?.(new Error('Polling timeout'));
        return;
      }
      
      const response = await fetch(url, { headers });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const currentValue = checkValue(data);
      
      console.log(`[useSmartPolling] Attempt ${attemptsRef.current}: value = ${currentValue}, expected = ${expectedValue}`);
      
      if (currentValue === expectedValue) {
        console.log('[useSmartPolling] Expected value reached!');
        stopPolling();
        onSuccess(data);
      }
    } catch (error) {
      console.error('[useSmartPolling] Error:', error);
      onError?.(error as Error);
      // Optionally stop polling on error, or let it continue retrying
      // stopPolling(); 
    }
  }, [url, headers, checkValue, expectedValue, onSuccess, onError, stopPolling, maxAttempts, enabled]); // Added enabled
  
  const startPolling = useCallback(() => {
    if (isPollingRef.current) {
      console.log('[useSmartPolling] Already polling');
      return;
    }
    
    console.log('[useSmartPolling] Starting polling');
    isPollingRef.current = true;
    attemptsRef.current = 0;
    
    // Initial poll
    poll();
    
    // Set up interval
    intervalRef.current = setInterval(poll, intervalMs);
  }, [poll, intervalMs]);
  
  // Effect to manage polling lifecycle
  useEffect(() => {
    if (enabled && !isPollingRef.current) {
      startPolling();
    } else if (!enabled && isPollingRef.current) {
      stopPolling();
    }
    
    return () => {
      stopPolling();
    };
  }, [enabled, startPolling, stopPolling]);
  
  return {
    isPolling: isPollingRef.current,
    attempts: attemptsRef.current,
    stopPolling,
    startPolling,
  };
}