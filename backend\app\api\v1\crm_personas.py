from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List # Import List
from uuid import UUID

from app.models.crm_persona import PersonaC<PERSON>, Persona as PersonaResponse
from app.services import crm_persona_service
from app.core.security import get_current_user_id
# from app.core.database import get_db

router = APIRouter()

@router.post("", response_model=PersonaResponse, status_code=status.HTTP_201_CREATED) # Removed trailing slash
async def create_new_persona(
    persona_in: PersonaCreate,
    # db: Session = Depends(get_db), # If using SQLAlchemy sessions
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new persona.
    """
    try:
        # db=None no longer needed
        created_persona_data = await crm_persona_service.create_persona(persona_in=persona_in, user_id=current_user_id)
        
        # Assuming the service returns a dict compatible with PersonaResponse
        # This includes id, created_at, updated_at, and potentially auto-set fecha_alta
        return PersonaResponse(**created_persona_data)
        
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create persona: {str(e)}"
        )

@router.get("", response_model=List[PersonaResponse]) # Removed trailing slash
async def list_personas(
    search: str | None = Query(None, alias="search"),
    empresa_id: UUID | None = Query(None, alias="empresa_id"), # For filtering by company
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve a list of personas, with optional search and filtering by empresa_id.
    """
    try:
        personas_data = await crm_persona_service.get_personas(
            search=search, empresa_id=empresa_id, skip=skip, limit=limit
        )
        # The service now returns a list of Pydantic model instances (or raises an error)
        return personas_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve personas: {str(e)}"
        )

# Add other endpoints (get by ID, update, delete) as needed.
