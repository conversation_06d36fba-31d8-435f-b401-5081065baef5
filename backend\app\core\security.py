import hmac
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from typing import Optional, Dict, Any

from .config import settings

# Define specific exceptions for clarity, although we might just raise HTTPException directly
class CredentialsException(HTTPException):
    def __init__(self, detail: str = "Could not validate credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

def verify_supabase_jwt(token: str) -> Optional[Dict[str, Any]]:
    """
    Verifies a JWT token issued by Supabase.

    Args:
        token: The JWT token string.

    Returns:
        The decoded token payload if verification is successful.

    Raises:
        CredentialsException: If the token is invalid, expired, or cannot be verified.
    """
    if not token:
        raise CredentialsException("No token provided.")

    try:
        # Decode the token using the Supabase JWT secret and specified audience
        payload = jwt.decode(
            token,
            settings.SUPABASE_JWT_SECRET.get_secret_value(),
            algorithms=["HS256"],
            audience=settings.SUPABASE_AUDIENCE,
            options={"verify_aud": True} # Explicitly verify audience
        )
        # Optionally, you could add more checks here, e.g., check 'exp' claim manually if needed,
        # although jwt.decode handles expiration by default.

        # Check if 'sub' (user ID) exists in the payload
        if "sub" not in payload:
             raise CredentialsException("Invalid token: Missing 'sub' claim.")

        return payload

    except jwt.ExpiredSignatureError:
        raise CredentialsException("Token has expired.")
    except jwt.JWTClaimsError as e:
        # Handles audience verification errors and other claim issues
        raise CredentialsException(f"Invalid token claims: {e}")
    except JWTError as e:
        # General JWT errors (e.g., invalid signature, malformed token)
        raise CredentialsException(f"Could not validate token: {e}")
    except Exception as e:
        # Catch any other unexpected errors during decoding/verification
        print(f"Unexpected error verifying token: {e}") # Log unexpected errors
        raise CredentialsException("An unexpected error occurred during token verification.")
from fastapi.security import OAuth2PasswordBearer # Use OAuth2PasswordBearer to extract token from header
from fastapi import Depends

# OAuth2 scheme definition (points to a dummy tokenUrl, as Supabase handles the actual token issuance)
# This is mainly used by FastAPI/Swagger UI to know how to prompt for the Authorization header.
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token") # Dummy URL

async def get_current_user_payload(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    FastAPI dependency to verify the JWT token from the Authorization header
    and return the decoded payload.

    Raises HTTPException (CredentialsException) if verification fails.
    """
    payload = verify_supabase_jwt(token)
    if payload is None:
        # This case should theoretically not be reached if verify_supabase_jwt always raises
        # but added for robustness.
        raise CredentialsException("Could not verify token payload.")
    return payload

async def get_current_user_id(payload: Dict[str, Any] = Depends(get_current_user_payload)) -> str:
    """
    FastAPI dependency that extracts the user ID ('sub' claim) from the
    verified token payload.
    """
    user_id = payload.get("sub")
    if user_id is None:
        raise CredentialsException("User ID ('sub') not found in token payload.")
    return user_id
from fastapi import Header # Add Header import

# ... (keep existing imports and code) ...

async def verify_api_key(api_key: Optional[str] = Header(None, alias="X-API-Key")) -> None:
    """
    FastAPI dependency to verify the API key provided in the X-API-Key header.

    Raises:
        HTTPException 403: If the API key is missing or invalid.
    """
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Missing API Key in X-API-Key header",
        )
    # Use constant-time comparison to prevent timing attacks
    if not api_key or not hmac.compare_digest(api_key, settings.N8N_API_KEY.get_secret_value()):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid API Key",
        )
    # If the key is valid, the dependency succeeds by returning implicitly