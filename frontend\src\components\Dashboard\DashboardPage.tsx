import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useDashboard, useQuickActions, useDashboardNotifications } from '../../hooks/useDashboard';
import { useDashboardRealtime } from '../../hooks/useRealtime';
import TaskMatrix from './TaskMatrix';
import ProjectCards from './ProjectCards';
import ProcessList from './ProcessList';
import QuickActions from './QuickActions';
import { TareaSummary } from '../../types/tarea';
import { ProyectoSummary } from '../../types/proyecto';
import { ProcesoSummary } from '../../types/proceso';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Bell,
  X
} from 'lucide-react';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { 
    dashboardData, 
    loading: dashboardLoading, 
    error: dashboardError, 
    refreshDashboard 
  } = useDashboard();
  
  const { 
    quickActions, 
    loading: actionsLoading 
  } = useQuickActions();
  
  const {
    notifications,
    refreshNotifications
  } = useDashboardNotifications();

  // Setup realtime updates
  useDashboardRealtime(() => {
    refreshDashboard();
    refreshNotifications();
  });

  const handleTaskClick = (task: TareaSummary) => {
    navigate(`/tareas/${task.id}`);
  };

  const handleProjectClick = (project: ProyectoSummary) => {
    navigate(`/proyectos/${project.id}`);
  };

  const handleProcessClick = (process: ProcesoSummary) => {
    navigate(`/procesos/${process.id}`);
  };

  const handleNotificationClick = (route?: string) => {
    if (route) {
      navigate(route);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    trend?: 'up' | 'down' | 'stable';
  }> = ({ title, value, icon, color, trend }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className="mt-2 flex items-center">
          <TrendingUp className={`h-4 w-4 mr-1 ${
            trend === 'up' ? 'text-green-500' : 
            trend === 'down' ? 'text-red-500' : 
            'text-gray-500'
          }`} />
          <span className="text-xs text-gray-600">
            {trend === 'up' ? 'Aumentando' : 
             trend === 'down' ? 'Disminuyendo' : 
             'Estable'}
          </span>
        </div>
      )}
    </div>
  );

  if (dashboardError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar el dashboard
          </h2>
          <p className="text-gray-600 mb-4">{dashboardError}</p>
          <button
            onClick={refreshDashboard}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Resumen de tu actividad y tareas importantes
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            {notifications && notifications.total_count > 0 && (
              <div className="relative">
                <button className="relative p-2 text-gray-600 hover:text-gray-900">
                  <Bell className="h-6 w-6" />
                  <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {notifications.total_count}
                  </span>
                </button>
              </div>
            )}
            
            <button
              onClick={refreshDashboard}
              disabled={dashboardLoading}
              className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${dashboardLoading ? 'animate-spin' : ''}`} />
              Actualizar
            </button>
          </div>
        </div>

        {/* Notifications Banner */}
        {notifications && notifications.notifications.length > 0 && (
          <div className="mb-8 space-y-3">
            {notifications.notifications.slice(0, 3).map((notification) => (
              <div
                key={notification.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  notification.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                  notification.type === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
                  notification.type === 'info' ? 'bg-blue-50 border-blue-200 text-blue-800' :
                  'bg-green-50 border-green-200 text-green-800'
                }`}
                onClick={() => handleNotificationClick(notification.route)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium">{notification.title}</h4>
                    <p className="text-sm mt-1">{notification.message}</p>
                    {notification.action && (
                      <button className="text-sm font-medium mt-2 underline">
                        {notification.action}
                      </button>
                    )}
                  </div>
                  <button className="ml-4 text-gray-400 hover:text-gray-600">
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Stats Cards */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Tareas Activas"
              value={dashboardData.quick_stats.total_tareas_activas}
              icon={<CheckCircle className="h-6 w-6 text-white" />}
              color="bg-blue-500"
              trend="stable"
            />
            <StatCard
              title="Tareas Urgentes"
              value={dashboardData.quick_stats.tareas_urgentes}
              icon={<AlertTriangle className="h-6 w-6 text-white" />}
              color="bg-red-500"
              trend="down"
            />
            <StatCard
              title="Proyectos Activos"
              value={dashboardData.quick_stats.total_proyectos_activos}
              icon={<TrendingUp className="h-6 w-6 text-white" />}
              color="bg-green-500"
              trend="up"
            />
            <StatCard
              title="Procesos Importantes"
              value={dashboardData.quick_stats.total_procesos_importantes}
              icon={<Clock className="h-6 w-6 text-white" />}
              color="bg-purple-500"
              trend="stable"
            />
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Acciones Rápidas
          </h2>
          <QuickActions 
            actions={quickActions?.actions || []} 
            loading={actionsLoading}
          />
        </div>

        {/* Task Matrix */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Matriz de Tareas (Importancia/Urgencia)
          </h2>
          {dashboardData ? (
            <TaskMatrix
              matrix={dashboardData.tareas_matrix}
              onTaskClick={handleTaskClick}
              loading={dashboardLoading}
            />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          )}
        </div>

        {/* Projects and Processes */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Projects */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Proyectos Activos
            </h2>
            {dashboardData ? (
              <ProjectCards
                projects={dashboardData.proyectos_activos}
                onProjectClick={handleProjectClick}
                loading={dashboardLoading}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="bg-gray-100 rounded-lg h-64 animate-pulse" />
                ))}
              </div>
            )}
          </div>

          {/* Important Processes */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Procesos Importantes
            </h2>
            {dashboardData ? (
              <ProcessList
                processes={dashboardData.procesos_importantes}
                onProcessClick={handleProcessClick}
                loading={dashboardLoading}
              />
            ) : (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-gray-100 rounded-lg h-24 animate-pulse" />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
