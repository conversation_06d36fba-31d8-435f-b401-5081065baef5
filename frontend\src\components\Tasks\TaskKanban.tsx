import React, { useState } from 'react';
import { TareaKanbanBoard, TareaSummary, ESTADO_TAREA_COLORS, EstadoTarea, PrioridadTarea, UrgenciaTarea } from '../../types/tarea';
import { Clock } from 'lucide-react';
import TaskCard from './TaskCard';

// Type for possible values in TareaSummary fields
type TareaSummaryFieldValue = string | number | boolean | EstadoTarea | PrioridadTarea | UrgenciaTarea | undefined;

interface TaskKanbanProps {
  kanbanBoard: TareaKanbanBoard;
  onTaskClick: (taskId: string) => void;
  onUpdateTask: (taskId: string, field: keyof TareaSummary, value: TareaSummaryFieldValue) => Promise<void>;
  onDeleteTask: (taskId: string, taskTitle: string) => Promise<void>;
}

export const TaskKanban: React.FC<TaskKanbanProps> = ({
  kanbanBoard,
  onTaskClick,
  onUpdateTask,
  onDeleteTask
}) => {
  const [draggedTask, setDraggedTask] = useState<TareaSummary | null>(null);

  const handleDragStart = (e: React.DragEvent, task: TareaSummary) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, newEstado: string) => {
    e.preventDefault();
    
    if (draggedTask && draggedTask.estado !== newEstado) {
      try {
        await onUpdateTask(draggedTask.id, 'estado', newEstado);
      } catch (error) {
        console.error('Error updating task status:', error);
      }
    }
    
    setDraggedTask(null);
  };

  // formatDate and getDaysText functions are now imported from taskUtils

  // TaskCard component is now imported from separate file

  return (
    <div className="flex space-x-6 overflow-x-auto pb-6" style={{ minWidth: '1200px' }}>
      {kanbanBoard.columnas.map((columna) => (
        <div
          key={columna.estado}
          className="flex-shrink-0 w-80"
          onDragOver={handleDragOver}
          onDrop={(e) => handleDrop(e, columna.estado)}
        >
          {/* Column Header */}
          <div className="bg-white rounded-lg border border-gray-200 mb-4">
            <div className={`px-4 py-3 border-b border-gray-200 rounded-t-lg ${ESTADO_TAREA_COLORS[columna.estado]}`}>
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-sm">{columna.estado}</h3>
                <span className="text-xs font-medium bg-white bg-opacity-20 px-2 py-1 rounded-full">
                  {columna.total}
                </span>
              </div>
            </div>

            {/* Column Content */}
            <div className="p-4 space-y-3 max-h-96 overflow-y-auto min-h-[200px]">
              {columna.tareas.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No hay tareas</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Arrastra tareas aquí
                  </p>
                </div>
              ) : (
                columna.tareas.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onTaskClick={onTaskClick}
                    onDeleteTask={onDeleteTask}
                    onDragStart={handleDragStart}
                    isDragging={draggedTask?.id === task.id}
                    compact={true}
                  />
                ))
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
