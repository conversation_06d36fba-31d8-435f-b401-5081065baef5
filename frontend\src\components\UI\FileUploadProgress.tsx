import React from 'react';
import { DocumentArrowUpIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

interface FileUploadProgressProps {
  fileName: string;
  progress: number;
  isUploading: boolean;
  isComplete: boolean;
  hasError: boolean;
  errorMessage?: string;
  fileSize?: number;
  uploadSpeed?: number;
  estimatedTimeRemaining?: number;
}

const FileUploadProgress: React.FC<FileUploadProgressProps> = ({
  fileName,
  progress,
  isUploading,
  isComplete,
  hasError,
  errorMessage,
  fileSize,
  uploadSpeed,
  estimatedTimeRemaining
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return `${formatFileSize(bytesPerSecond)}/s`;
  };

  const getStatusIcon = () => {
    if (hasError) {
      return <XCircleIcon className="h-8 w-8 text-red-500" />;
    }
    if (isComplete) {
      return <CheckCircleIcon className="h-8 w-8 text-green-500" />;
    }
    return <DocumentArrowUpIcon className="h-8 w-8 text-blue-500" />;
  };

  const getStatusColor = () => {
    if (hasError) return 'text-red-600';
    if (isComplete) return 'text-green-600';
    return 'text-blue-600';
  };

  const getProgressBarColor = () => {
    if (hasError) return 'bg-red-500';
    if (isComplete) return 'bg-green-500';
    return 'bg-blue-500';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      <div className="flex items-start space-x-4">
        {/* Status Icon */}
        <div className="flex-shrink-0">
          {getStatusIcon()}
        </div>

        {/* File Info and Progress */}
        <div className="flex-1 min-w-0">
          {/* File Name */}
          <h3 className="text-lg font-medium text-gray-900 truncate">
            {fileName}
          </h3>

          {/* File Size */}
          {fileSize && (
            <p className="text-sm text-gray-500 mt-1">
              {formatFileSize(fileSize)}
            </p>
          )}

          {/* Status Message */}
          <p className={`text-sm font-medium mt-2 ${getStatusColor()}`}>
            {hasError ? (
              errorMessage || 'Error al subir el archivo'
            ) : isComplete ? (
              'Archivo subido exitosamente'
            ) : isUploading ? (
              'Subiendo archivo...'
            ) : (
              'Preparando subida...'
            )}
          </p>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>{Math.round(progress)}%</span>
              {uploadSpeed && isUploading && (
                <span>{formatSpeed(uploadSpeed)}</span>
              )}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ease-out ${getProgressBarColor()}`}
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Additional Info */}
          {isUploading && estimatedTimeRemaining && estimatedTimeRemaining > 0 && (
            <p className="text-xs text-gray-500 mt-2">
              Tiempo restante estimado: {formatTime(estimatedTimeRemaining)}
            </p>
          )}

          {/* Upload Stats */}
          {(isUploading || isComplete) && fileSize && (
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>
                {formatFileSize((progress / 100) * fileSize)} de {formatFileSize(fileSize)}
              </span>
              {isComplete && (
                <span className="text-green-600 font-medium">
                  ✓ Completado
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Error Details */}
      {hasError && errorMessage && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700">{errorMessage}</p>
        </div>
      )}
    </div>
  );
};

export default FileUploadProgress;
