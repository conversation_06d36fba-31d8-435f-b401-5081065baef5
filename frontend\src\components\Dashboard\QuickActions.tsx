import React from 'react';
import { useNavigate } from 'react-router-dom';
import { QuickAction } from '../../types/dashboard';
import { Plus, CheckSquare, Workflow, BarChart3, LucideIcon } from 'lucide-react';

interface QuickActionsProps {
  actions: QuickAction[];
  loading?: boolean;
}

const iconMap: Record<string, LucideIcon> = {
  plus: Plus,
  task: CheckSquare,
  workflow: Workflow,
  kanban: BarChart3,
};

const ActionButton: React.FC<{
  action: QuickAction;
  onClick: () => void;
}> = ({ action, onClick }) => {
  const IconComponent = iconMap[action.icon] || Plus;
  
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500 hover:bg-blue-600 text-white';
      case 'green':
        return 'bg-green-500 hover:bg-green-600 text-white';
      case 'purple':
        return 'bg-purple-500 hover:bg-purple-600 text-white';
      case 'orange':
        return 'bg-orange-500 hover:bg-orange-600 text-white';
      case 'red':
        return 'bg-red-500 hover:bg-red-600 text-white';
      default:
        return 'bg-gray-500 hover:bg-gray-600 text-white';
    }
  };

  return (
    <button
      onClick={onClick}
      className={`
        group relative p-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg
        ${getColorClasses(action.color)}
      `}
    >
      <div className="flex flex-col items-center text-center">
        <div className="mb-3 p-3 bg-white bg-opacity-20 rounded-full group-hover:bg-opacity-30 transition-all duration-200">
          <IconComponent className="h-6 w-6" />
        </div>
        
        <h3 className="font-semibold text-sm mb-1">
          {action.title}
        </h3>
        
        <p className="text-xs opacity-90 line-clamp-2">
          {action.description}
        </p>
      </div>
      
      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-white bg-opacity-0 group-hover:bg-opacity-10 rounded-xl transition-all duration-200" />
    </button>
  );
};

const QuickActions: React.FC<QuickActionsProps> = ({ 
  actions, 
  loading = false 
}) => {
  const navigate = useNavigate();

  const handleActionClick = (action: QuickAction) => {
    navigate(action.route);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-gray-100 rounded-xl h-32 animate-pulse" />
        ))}
      </div>
    );
  }

  if (actions.length === 0) {
    return (
      <div className="text-center py-8">
        <Plus className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay acciones disponibles
        </h3>
        <p className="text-gray-600">
          Las acciones rápidas aparecerán aquí cuando estén configuradas.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {actions.map((action) => (
        <ActionButton
          key={action.id}
          action={action}
          onClick={() => handleActionClick(action)}
        />
      ))}
    </div>
  );
};

export default QuickActions;
