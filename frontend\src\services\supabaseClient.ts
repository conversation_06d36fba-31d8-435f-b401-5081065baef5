import { createClient } from '@supabase/supabase-js';

// Get Supabase URL and Anon Key from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Basic validation
if (!supabaseUrl) {
  console.error('Error: VITE_SUPABASE_URL environment variable is not set.');
  throw new Error('Supabase URL not configured. Please check your .env file.');
}
if (!supabaseAnonKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set.');
  throw new Error('Supabase Anon Key not configured. Please check your .env file.');
}

// Create and export the Supabase client instance
// Enhanced configuration for better performance and reliability
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 30, // Reduced to prevent overwhelming with large conversations
    },
    heartbeatIntervalMs: 20000, // Increased to reduce overhead
    reconnectAfterMs: (tries: number) => {
      // More conservative reconnection for stability: 2s, 4s, 8s, 16s, max 30s
      return Math.min(Math.pow(2, tries + 1) * 1000, 30000);
    },
    timeout: 15000, // 15 second connection timeout
  },
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
  global: {
    headers: {
      'x-client-info': 'supabase-js-web',
      'x-client-version': '2.0-optimized',
    },
  },
  // Add performance optimizations
  db: {
    schema: 'public',
  },
});

// Add connection monitoring

// Function to check Realtime health
export const checkRealtimeHealth = (): boolean => {
  // Temporarily return true to allow connection attempts
  // We will rely on channel subscription statuses directly for now.
  return true;
};

// Monitor Realtime connection health
export const startRealtimeHealthMonitoring = () => {
  // Temporarily disabled to investigate recursion issues
  // if (connectionCheckInterval) {
  //   clearInterval(connectionCheckInterval);
  // }
  //
  // connectionCheckInterval = setInterval(() => {
  //   const channel = supabase.channel('health-check');
  //   let cleanedUp = false;
  //
  //   const cleanup = () => {
  //     if (cleanedUp) return;
  //     cleanedUp = true;
  //     if (channel) { // Check if channel still exists
  //        supabase.removeChannel(channel).catch(e => console.error("Error removing health check channel:", e));
  //     }
  //   };
  //
  //   const healthTimeout = setTimeout(() => {
  //     isRealtimeHealthy = false;
  //     console.warn('Realtime health check failed - timeout');
  //     cleanup();
  //   }, 5000);
  //
  //   const clearHealthTimeout = () => {
  //     clearTimeout(healthTimeout);
  //   };
  //
  //   channel
  //     .subscribe((status) => {
  //       if (status === 'SUBSCRIBED') {
  //         isRealtimeHealthy = true;
  //         console.log('Realtime health check passed');
  //         clearHealthTimeout();
  //         cleanup();
  //       } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
  //         isRealtimeHealthy = false;
  //         console.warn('Realtime health check failed - connection error');
  //         clearHealthTimeout();
  //         cleanup();
  //       }
  //     });
  // }, 30000);
};

// Stop health monitoring
export const stopRealtimeHealthMonitoring = () => {
  // Temporarily disabled
  // if (connectionCheckInterval) {
  //   clearInterval(connectionCheckInterval);
  //   connectionCheckInterval = null;
  // }
};

// Type for channel options
interface ChannelOptions {
  postgres_changes?: Array<{
    event: string;
    schema: string;
    table: string;
    filter?: string;
  }>;
  presence?: {
    key: string;
  };
  broadcast?: {
    self: boolean;
  };
  [key: string]: unknown;
}

// Enhanced channel creation with better error handling
export const createRealtimeChannel = (channelName: string, options: ChannelOptions = {}) => {
  const channel = supabase.channel(channelName, {
    config: {
      presence: {
        key: 'user-presence',
      },
      broadcast: {
        self: false,
      },
      ...options,
    },
  });

  // Add connection event listeners
  channel.on('system', {}, (payload: { event?: string; [key: string]: unknown }) => {
    console.log('Realtime system event:', payload);
    if (payload.event === 'phx_error') {
      console.error('Realtime Phoenix error:', payload);
    }
  });

  return channel;
};

console.log('Supabase client initialized with enhanced Realtime configuration.'); // Log for confirmation during development