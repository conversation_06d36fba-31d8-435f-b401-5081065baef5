/**
 * Constantes para tipos de hallazgos
 * Estos valores deben coincidir exactamente con el ENUM de la base de datos
 */

// Tipos de hallazgos según el ENUM de la base de datos
export const HALLAZGO_TIPOS = {
  INEFICIENCIA: 'ineficiencia',
  LADRON_TIEMPO: 'ladron_tiempo',
  OPORTUNIDAD_MEJORA: 'oportunidad_mejora',
  RIESGO_IDENTIFICADO: 'riesgo_identificado',
  DEFICIT_GOBERNANZA_DATOS: 'deficit_gobernanza_datos',
  FALTA_ESTANDARIZACION: 'falta_estandarizacion',
  EQUIPAMIENTO_INADECUADO: 'equipamiento_inadecuado',
} as const;

// Tipo TypeScript derivado de las constantes
export type HallazgoTipo = typeof HALLAZGO_TIPOS[keyof typeof HALLAZGO_TIPOS];

// Array de todos los tipos válidos
export const HALLAZGO_TIPOS_ARRAY: HallazgoTipo[] = Object.values(HALLAZGO_TIPOS);

// Etiquetas en español para cada tipo de hallazgo
export const HALLAZGO_TIPO_LABELS: Record<HallazgoTipo, string> = {
  [HALLAZGO_TIPOS.INEFICIENCIA]: 'Ineficiencia',
  [HALLAZGO_TIPOS.LADRON_TIEMPO]: 'Ladrón de Tiempo',
  [HALLAZGO_TIPOS.OPORTUNIDAD_MEJORA]: 'Oportunidad de Mejora',
  [HALLAZGO_TIPOS.RIESGO_IDENTIFICADO]: 'Riesgo Identificado',
  [HALLAZGO_TIPOS.DEFICIT_GOBERNANZA_DATOS]: 'Déficit de Gobernanza de Datos',
  [HALLAZGO_TIPOS.FALTA_ESTANDARIZACION]: 'Falta de Estandarización',
  [HALLAZGO_TIPOS.EQUIPAMIENTO_INADECUADO]: 'Equipamiento Inadecuado',
};

// Mapeo de colores para cada tipo de hallazgo
export const HALLAZGO_TIPO_COLORS: Record<HallazgoTipo, string> = {
  [HALLAZGO_TIPOS.INEFICIENCIA]: 'bg-red-100 text-red-800 border-red-200',
  [HALLAZGO_TIPOS.LADRON_TIEMPO]: 'bg-orange-100 text-orange-800 border-orange-200',
  [HALLAZGO_TIPOS.OPORTUNIDAD_MEJORA]: 'bg-green-100 text-green-800 border-green-200',
  [HALLAZGO_TIPOS.RIESGO_IDENTIFICADO]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [HALLAZGO_TIPOS.DEFICIT_GOBERNANZA_DATOS]: 'bg-purple-100 text-purple-800 border-purple-200',
  [HALLAZGO_TIPOS.FALTA_ESTANDARIZACION]: 'bg-blue-100 text-blue-800 border-blue-200',
  [HALLAZGO_TIPOS.EQUIPAMIENTO_INADECUADO]: 'bg-gray-100 text-gray-800 border-gray-200',
};

// Color por defecto para tipos no reconocidos
export const HALLAZGO_TIPO_COLOR_DEFAULT = 'bg-gray-100 text-gray-800 border-gray-200';

// Función helper para obtener la etiqueta de un tipo
export const getHallazgoTipoLabel = (tipo: string | null | undefined): string => {
  if (!tipo) return 'Sin clasificar';
  return HALLAZGO_TIPO_LABELS[tipo as HallazgoTipo] || 'Sin clasificar';
};

// Función helper para obtener el color de un tipo
export const getHallazgoTipoColor = (tipo: string | null | undefined): string => {
  if (!tipo) return HALLAZGO_TIPO_COLOR_DEFAULT;
  return HALLAZGO_TIPO_COLORS[tipo as HallazgoTipo] || HALLAZGO_TIPO_COLOR_DEFAULT;
};

// Función para validar si un tipo es válido
export const isValidHallazgoTipo = (tipo: string): tipo is HallazgoTipo => {
  return HALLAZGO_TIPOS_ARRAY.includes(tipo as HallazgoTipo);
};
