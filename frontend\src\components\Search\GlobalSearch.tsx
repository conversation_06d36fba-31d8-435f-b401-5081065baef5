import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuickSearch, useSearchSuggestions } from '../../hooks/useSearch';
import { SearchResult, SEARCH_TYPE_LABELS, SEARCH_TYPE_COLORS } from '../../types/search';
import { Search, X, Clock, User, Calendar, Building, AlertTriangle } from 'lucide-react';

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

const SearchResultItem: React.FC<{
  result: SearchResult;
  onClick: () => void;
}> = ({ result, onClick }) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('es-ES', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  };

  return (
    <div
      onClick={onClick}
      className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2 ${SEARCH_TYPE_COLORS[result.type]}`}>
              {SEARCH_TYPE_LABELS[result.type]}
            </span>
            {result.es_vencida && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
          </div>
          
          <h3 className="text-sm font-medium text-gray-900 mb-1 line-clamp-1">
            {result.title}
          </h3>
          
          {result.description && (
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {result.description}
            </p>
          )}
          
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {result.estado && (
              <span>Estado: {result.estado}</span>
            )}
            
            {result.prioridad && (
              <span>Prioridad: {result.prioridad}</span>
            )}
            
            {result.responsable && (
              <div className="flex items-center">
                <User className="h-3 w-3 mr-1" />
                <span>{result.responsable}</span>
              </div>
            )}
            
            {result.empresa && (
              <div className="flex items-center">
                <Building className="h-3 w-3 mr-1" />
                <span>{result.empresa}</span>
              </div>
            )}
            
            {result.fecha_vencimiento && (
              <div className={`flex items-center ${result.es_vencida ? 'text-red-600' : ''}`}>
                <Calendar className="h-3 w-3 mr-1" />
                <span>{formatDate(result.fecha_vencimiento)}</span>
              </div>
            )}
            
            {result.tiempo_estimado && (
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>{result.tiempo_estimado}min</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const GlobalSearch: React.FC<GlobalSearchProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { results, loading, quickSearch, clearResults } = useQuickSearch();
  const { suggestions, getSuggestions, clearSuggestions } = useSearchSuggestions();

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle search
  useEffect(() => {
    if (query.trim()) {
      quickSearch(query);
      getSuggestions(query, 5);
      setShowSuggestions(true);
    } else {
      clearResults();
      clearSuggestions();
      setShowSuggestions(false);
    }
  }, [query, quickSearch, getSuggestions, clearResults, clearSuggestions]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const handleResultClick = (result: SearchResult) => {
    navigate(result.url);
    onClose();
    setQuery('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
  };

  const handleClear = () => {
    setQuery('');
    clearResults();
    clearSuggestions();
    setShowSuggestions(false);
  };

  const handleViewAll = () => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
    onClose();
    setQuery('');
  };

  if (!isOpen) return null;

  const hasResults = results && (
    results.proyectos.length > 0 || 
    results.procesos.length > 0 || 
    results.tareas.length > 0
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20">
      <div ref={containerRef} className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-96 overflow-hidden">
        {/* Search Input */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Buscar proyectos, procesos, tareas..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="w-full pl-10 pr-10 py-3 border-0 focus:ring-0 text-lg placeholder-gray-500"
            />
            {query && (
              <button
                onClick={handleClear}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="max-h-80 overflow-y-auto">
          {loading && (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Buscando...</p>
            </div>
          )}

          {!loading && query && !hasResults && (
            <div className="p-8 text-center">
              <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron resultados
              </h3>
              <p className="text-gray-600">
                Intenta con otros términos de búsqueda.
              </p>
            </div>
          )}

          {!loading && !query && (
            <div className="p-8 text-center">
              <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Búsqueda Global
              </h3>
              <p className="text-gray-600">
                Busca en proyectos, procesos y tareas.
              </p>
            </div>
          )}

          {/* Suggestions */}
          {showSuggestions && suggestions.length > 0 && !hasResults && (
            <div className="p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Sugerencias</h4>
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Results */}
          {hasResults && (
            <div>
              {/* Proyectos */}
              {results.proyectos.length > 0 && (
                <div>
                  <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                    <h4 className="text-sm font-medium text-gray-700">
                      Proyectos ({results.proyectos.length})
                      {results.has_more.proyectos && ' +'}
                    </h4>
                  </div>
                  {results.proyectos.map((result) => (
                    <SearchResultItem
                      key={result.id}
                      result={result}
                      onClick={() => handleResultClick(result)}
                    />
                  ))}
                </div>
              )}

              {/* Procesos */}
              {results.procesos.length > 0 && (
                <div>
                  <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                    <h4 className="text-sm font-medium text-gray-700">
                      Procesos ({results.procesos.length})
                      {results.has_more.procesos && ' +'}
                    </h4>
                  </div>
                  {results.procesos.map((result) => (
                    <SearchResultItem
                      key={result.id}
                      result={result}
                      onClick={() => handleResultClick(result)}
                    />
                  ))}
                </div>
              )}

              {/* Tareas */}
              {results.tareas.length > 0 && (
                <div>
                  <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                    <h4 className="text-sm font-medium text-gray-700">
                      Tareas ({results.tareas.length})
                      {results.has_more.tareas && ' +'}
                    </h4>
                  </div>
                  {results.tareas.map((result) => (
                    <SearchResultItem
                      key={result.id}
                      result={result}
                      onClick={() => handleResultClick(result)}
                    />
                  ))}
                </div>
              )}

              {/* View All Results */}
              {results.total_results > 0 && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <button
                    onClick={handleViewAll}
                    className="w-full text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Ver todos los resultados ({results.total_results})
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalSearch;
