import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
from uuid import UUID

from app.models.proceso import (
    ProcesoCreate, ProcesoUpdate, ProcesoResponse, ProcesoListResponse,
    ProcesoSummary, ProcesoFilters, TipoProceso
)
from app.core.security import get_current_user_id
from app.services import proceso_service

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=ProcesoResponse, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=ProcesoResponse, status_code=status.HTTP_201_CREATED)
async def create_proceso(
    proceso_in: ProcesoCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new proceso.
    """
    try:
        logger.info(f"Creating proceso: {proceso_in.nombre} by user {current_user_id}")
        proceso = await proceso_service.create_proceso(proceso_in, current_user_id)
        return ProcesoResponse(**proceso.model_dump())
    except Exception as e:
        logger.error(f"Error creating proceso: {str(e)}")
        raise

@router.get("/", response_model=ProcesoListResponse)
@router.get("", response_model=ProcesoListResponse)
async def get_procesos(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    tipo_proceso: Optional[TipoProceso] = Query(None, description="Filter by tipo_proceso"),
    empresa_id: Optional[UUID] = Query(None, description="Filter by empresa"),
    es_cuello_botella: Optional[bool] = Query(None, description="Filter by cuello de botella"),
    search: Optional[str] = Query(None, description="Search in nombre and descripcion"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of procesos with optional filtering.
    """
    try:
        logger.info(f"Fetching procesos for user {current_user_id}")
        
        filters = ProcesoFilters(
            tipo_proceso=tipo_proceso,
            empresa_id=empresa_id,
            es_cuello_botella=es_cuello_botella,
            search=search
        )
        
        return await proceso_service.get_procesos(
            skip=skip,
            limit=limit,
            filters=filters
        )
    except Exception as e:
        logger.error(f"Error fetching procesos: {str(e)}")
        raise

@router.get("/dashboard", response_model=List[ProcesoSummary])
async def get_procesos_dashboard(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get active procesos for dashboard display.
    """
    try:
        logger.info(f"Fetching dashboard procesos for user {current_user_id}")
        return await proceso_service.get_procesos_dashboard()
    except Exception as e:
        logger.error(f"Error fetching dashboard procesos: {str(e)}")
        raise

@router.get("/{proceso_id}", response_model=ProcesoResponse)
async def get_proceso(
    proceso_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a specific proceso by ID.
    """
    try:
        logger.info(f"Fetching proceso {proceso_id} for user {current_user_id}")
        proceso = await proceso_service.get_proceso_by_id(proceso_id)
        return ProcesoResponse(**proceso.model_dump())
    except Exception as e:
        logger.error(f"Error fetching proceso {proceso_id}: {str(e)}")
        raise

@router.put("/{proceso_id}", response_model=ProcesoResponse)
async def update_proceso(
    proceso_id: UUID,
    proceso_update: ProcesoUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update a proceso.
    """
    try:
        logger.info(f"Updating proceso {proceso_id} by user {current_user_id}")
        proceso = await proceso_service.update_proceso(proceso_id, proceso_update)
        return ProcesoResponse(**proceso.model_dump())
    except Exception as e:
        logger.error(f"Error updating proceso {proceso_id}: {str(e)}")
        raise

@router.delete("/{proceso_id}")
async def delete_proceso(
    proceso_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a proceso.
    """
    try:
        logger.info(f"Deleting proceso {proceso_id} by user {current_user_id}")
        return await proceso_service.delete_proceso(proceso_id)
    except Exception as e:
        logger.error(f"Error deleting proceso {proceso_id}: {str(e)}")
        raise

# Relationship management
@router.post("/{proceso_id}/link-proyecto/{proyecto_id}")
async def link_proceso_to_proyecto(
    proceso_id: UUID,
    proyecto_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Link a proceso to a proyecto.
    """
    try:
        logger.info(f"Linking proceso {proceso_id} to proyecto {proyecto_id} by user {current_user_id}")
        return await proceso_service.link_proceso_to_proyecto(proceso_id, proyecto_id)
    except Exception as e:
        logger.error(f"Error linking proceso {proceso_id} to proyecto {proyecto_id}: {str(e)}")
        raise

# Enums endpoints
@router.get("/tipos/available")
async def get_available_tipos(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available tipos for procesos.
    """
    return {
        "tipos": [tipo.value for tipo in TipoProceso]
    }

# Estados endpoint removed - procesos table doesn't have estado column

@router.get("/complejidad/available")
async def get_available_complejidad(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available complejidad levels for automatization.
    """
    return {
        "complejidad": [
            "Baja",
            "Media",
            "Alta",
            "Muy Alta"
        ]
    }

@router.get("/prioridad-automatizacion/available")
async def get_available_prioridad_automatizacion(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available prioridad levels for automatization.
    """
    return {
        "prioridades": [
            "Baja",
            "Media",
            "Alta",
            "Urgente"
        ]
    }

@router.get("/valor-negocio/available")
async def get_available_valor_negocio(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available valor de negocio options.
    """
    return {
        "valores": [
            "Bajo",
            "Medio",
            "Alto",
            "Crítico"
        ]
    }
