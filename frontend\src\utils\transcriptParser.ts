import { AudioSegmentInfo } from '../types/meeting.types';

/**
 * Removes time-speaker div elements from HTML transcript for cleaner display
 */
export function cleanTranscriptForDisplay(htmlTranscript: string | null): string | null {
  if (!htmlTranscript) return null;
  const timeSpeakerDivRegex = /<div class="time-speaker">.*?<\/div>/gm;
  return htmlTranscript.replace(timeSpeakerDivRegex, '');
}

/**
 * Extracts unique speaker IDs from transcript HTML
 */
export function extractSpeakerIds(transcript: string | null): string[] {
  if (!transcript) return [];
  
  const parser = new DOMParser();
  const doc = parser.parseFromString(transcript, 'text/html');
  const speakerElements = doc.querySelectorAll('span.text_speaker[id^="speaker_"]');
  const uniqueSpeakerIds: string[] = [];
  
  speakerElements.forEach(el => {
    if (el.id && !uniqueSpeakerIds.includes(el.id)) {
      uniqueSpeakerIds.push(el.id);
    }
  });
  
  return uniqueSpeakerIds.sort();
}

/**
 * Extracts the longest audio segment for each speaker from the transcript
 */
export function extractSpeakerLongestSegments(
  transcripcionRaw: string | null,
  audioSrc: string | null
): Map<string, AudioSegmentInfo> {
  const segmentsMap = new Map<string, AudioSegmentInfo>();
  if (!transcripcionRaw || !audioSrc) {
    return segmentsMap;
  }

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(transcripcionRaw, 'text/html');
    const speakerContainers = doc.querySelectorAll('.container-info-speaker');

    const allSegments: { speakerId: string; startTime: number; endTime: number; duration: number }[] = [];

    speakerContainers.forEach(container => {
      const timeElement = container.querySelector('.time-speaker');
      const speakerElement = container.querySelector('.text_speaker');

      if (timeElement && speakerElement && speakerElement.id) {
        const speakerId = speakerElement.id;
        const timeText = timeElement.textContent;

        if (timeText) {
          const parts = timeText.split(' - ');
          if (parts.length === 2) {
            const timeToSeconds = (timeStr: string): number => {
              const tParts = timeStr.split(':').map(Number);
              if (tParts.length === 3) return tParts[0] * 3600 + tParts[1] * 60 + tParts[2];
              if (tParts.length === 2) return tParts[0] * 60 + tParts[1];
              return 0;
            };

            const startTime = timeToSeconds(parts[0]);
            const endTime = timeToSeconds(parts[1]);
            const duration = endTime - startTime;

            if (duration > 0) {
              allSegments.push({ speakerId, startTime, endTime, duration });
            }
          }
        }
      }
    });

    // Group segments by speakerId and find the longest for each
    const groupedSegments = allSegments.reduce((acc, segment) => {
      if (!acc[segment.speakerId]) {
        acc[segment.speakerId] = [];
      }
      acc[segment.speakerId].push(segment);
      return acc;
    }, {} as Record<string, typeof allSegments>);

    for (const speakerId in groupedSegments) {
      if (Object.prototype.hasOwnProperty.call(groupedSegments, speakerId)) {
        const speakerSpecificSegments = groupedSegments[speakerId];
        if (speakerSpecificSegments.length > 0) {
          const longestSegment = speakerSpecificSegments.reduce((longest, current) =>
            current.duration > longest.duration ? current : longest
          );
          segmentsMap.set(speakerId, {
            startTime: longestSegment.startTime,
            duration: longestSegment.duration,
            audioSrc: audioSrc,
          });
        }
      }
    }
  } catch (error) {
    console.error("Error parsing transcript for audio segments:", error);
  }
  
  return segmentsMap;
}