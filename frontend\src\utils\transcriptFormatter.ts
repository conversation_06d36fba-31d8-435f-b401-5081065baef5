/**
 * Utility functions for formatting and parsing transcripts
 */

export interface TranscriptSegment {
  timeRange: string;
  speaker: string;
  content: string;
  speakerId?: string;
}

/**
 * Parse a transcript text into structured segments
 * Handles both HTML and plain text formats:
 * HTML: <div class="container-info-speaker">...</div>
 * Plain text: "Speaker 0: Content here..."
 * Plain text with time: "0:01 - 2:26 Speaker 0: Content here..."
 */
export function parseTranscriptSegments(transcript: string): TranscriptSegment[] {
  if (!transcript) return [];

  // Debug log to see what we're receiving
  console.log('[TranscriptFormatter] Parsing transcript:', {
    length: transcript.length,
    hasHTML: transcript.includes('<div class="container-info-speaker">'),
    preview: transcript.substring(0, 200) + (transcript.length > 200 ? '...' : '')
  });

  // Check if the transcript contains HTML
  if (transcript.includes('<div class="container-info-speaker">')) {
    return parseHTMLTranscript(transcript);
  } else {
    return parsePlainTextTranscript(transcript);
  }
}

/**
 * Parse HTML transcript format
 */
function parseHTMLTranscript(htmlTranscript: string): TranscriptSegment[] {
  const segments: TranscriptSegment[] = [];

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlTranscript, 'text/html');
    const speakerContainers = doc.querySelectorAll('.container-info-speaker');

    speakerContainers.forEach((container) => {
      const timeElement = container.querySelector('.time-speaker');
      const speakerElement = container.querySelector('.text_speaker');

      if (speakerElement) {
        const fullText = speakerElement.textContent || '';
        const speakerId = speakerElement.id || undefined;
        const timeRange = timeElement?.textContent?.trim() || '';

        // Extract speaker name and content from the text
        // Format is usually "Speaker Name: Content"
        const colonIndex = fullText.indexOf(':');
        let speaker = 'Unknown';
        let content = fullText;

        if (colonIndex > 0) {
          speaker = fullText.substring(0, colonIndex).trim();
          content = fullText.substring(colonIndex + 1).trim();
        }

        segments.push({
          timeRange,
          speaker,
          content,
          speakerId: speakerId || extractSpeakerId(speaker)
        });
      }
    });
  } catch (error) {
    console.error('Error parsing HTML transcript:', error);
    // Fallback to plain text parsing
    return parsePlainTextTranscript(htmlTranscript);
  }

  return segments;
}

/**
 * Parse plain text transcript format
 */
function parsePlainTextTranscript(transcript: string): TranscriptSegment[] {
  const lines = transcript.split('\n').filter(line => line.trim());
  const segments: TranscriptSegment[] = [];

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Try to match time range + speaker pattern: "0:01 - 2:26 Speaker 0: Content"
    const timeAndSpeakerMatch = trimmedLine.match(/^(\d+:\d+\s*-\s*\d+:\d+)\s+([^:]+):\s*(.*)$/);

    if (timeAndSpeakerMatch) {
      const [, timeRange, speaker, content] = timeAndSpeakerMatch;
      segments.push({
        timeRange: timeRange.trim(),
        speaker: speaker.trim(),
        content: content.trim(),
        speakerId: extractSpeakerId(speaker.trim())
      });
      continue;
    }

    // Try to match speaker pattern without time: "Speaker 0: Content" or "Alvaro Hidalgo: Content"
    const speakerMatch = trimmedLine.match(/^([^:]+):\s*(.*)$/);

    if (speakerMatch) {
      const [, speaker, content] = speakerMatch;
      segments.push({
        timeRange: '', // No time range available
        speaker: speaker.trim(),
        content: content.trim(),
        speakerId: extractSpeakerId(speaker.trim())
      });
      continue;
    }

    // If no speaker pattern found, treat as continuation of previous segment
    if (segments.length > 0) {
      segments[segments.length - 1].content += ' ' + trimmedLine;
    } else {
      // First line without speaker, create a generic segment
      segments.push({
        timeRange: '',
        speaker: 'Unknown',
        content: trimmedLine,
        speakerId: undefined
      });
    }
  }

  return segments;
}

/**
 * Extract speaker ID from speaker name for styling purposes
 * Returns email if it looks like an email, otherwise returns the speaker name
 */
function extractSpeakerId(speaker: string): string | undefined {
  // Check if it's an email pattern
  const emailMatch = speaker.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
  if (emailMatch) {
    return emailMatch[1];
  }

  // Check if it's a "Speaker X" pattern
  const speakerMatch = speaker.match(/^Speaker\s+(\d+)$/i);
  if (speakerMatch) {
    return `speaker-${speakerMatch[1]}`;
  }

  // Return a normalized version of the speaker name
  return speaker.toLowerCase().replace(/\s+/g, '-');
}

/**
 * Get speaker color based on speaker ID for consistent styling
 */
export function getSpeakerColor(speakerId: string | undefined): string {
  if (!speakerId) return 'bg-gray-100 text-gray-800';

  // Hash the speaker ID to get a consistent color
  let hash = 0;
  for (let i = 0; i < speakerId.length; i++) {
    const char = speakerId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  const colors = [
    'bg-blue-100 text-blue-800',
    'bg-green-100 text-green-800',
    'bg-purple-100 text-purple-800',
    'bg-orange-100 text-orange-800',
    'bg-pink-100 text-pink-800',
    'bg-indigo-100 text-indigo-800',
    'bg-yellow-100 text-yellow-800',
    'bg-red-100 text-red-800',
  ];

  return colors[Math.abs(hash) % colors.length];
}

/**
 * Format time range for display
 */
export function formatTimeRange(timeRange: string): string {
  if (!timeRange) return '';

  // Clean up the time range format
  return timeRange.replace(/\s+/g, ' ').trim();
}

/**
 * Truncate content for preview while preserving word boundaries
 */
export function truncateContent(content: string, maxLength: number = 150): string {
  if (content.length <= maxLength) return content;

  const truncated = content.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }

  return truncated + '...';
}
