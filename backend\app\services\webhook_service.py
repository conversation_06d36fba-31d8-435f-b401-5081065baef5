import logging
import httpx # Using httpx for async requests
from typing import Dict, Any

from app.core.config import settings
# Import the necessary Pydantic model when it's defined
# from app.models.chat import WebhookPayload

logger = logging.getLogger(__name__)

async def trigger_n8n_webhook(payload: Dict[str, Any]): # Replace Dict with WebhookPayload later
    """
    Triggers the configured n8n CHAT webhook with the provided payload using httpx.
    """
    # Use the renamed setting
    webhook_url = settings.N8N_WEBHOOK_URL_CHAT
    if not webhook_url or webhook_url == "YOUR_N8N_WEBHOOK_URL":
        logger.warning("N8N_WEBHOOK_URL_CHAT not configured. Skipping webhook trigger.")
        # In a real scenario, you might raise an error or handle this differently
        return

    logger.info(f"Attempting to trigger n8n webhook for thread: {payload.get('thread_id')}")
    logger.debug(f"Webhook URL: {webhook_url}")
    logger.debug(f"Webhook Payload: {payload}")

    # Convert UUIDs in payload to strings for JSON serialization
    import uuid # Import uuid module
    json_serializable_payload = {}
    for key, value in payload.items():
        if isinstance(value, uuid.UUID):
            json_serializable_payload[key] = str(value)
        else:
            json_serializable_payload[key] = value
    logger.debug(f"Webhook Payload (JSON Serializable): {json_serializable_payload}")

    # --- REAL IMPLEMENTATION ---
    try:
        async with httpx.AsyncClient() as client:
            # Add a reasonable timeout
            response = await client.post(webhook_url, json=json_serializable_payload, timeout=30.0) # Use sanitized payload
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
            logger.info(f"Successfully triggered n8n webhook for thread {payload.get('thread_id')}. Status: {response.status_code}")
    except httpx.RequestError as exc:
        logger.error(f"An error occurred while requesting {exc.request.url!r}: {exc}")
        # Decide how to handle webhook errors (e.g., retry, notify admin)
        # For now, re-raising to let the caller handle it or FastAPI return 500
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not connect to webhook service: {exc}"
        ) from exc
    except httpx.HTTPStatusError as exc:
        logger.error(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")
        # Decide how to handle webhook errors
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook service returned error status {exc.response.status_code}"
        ) from exc
    except Exception as exc:
        logger.exception(f"An unexpected error occurred during webhook trigger: {exc}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during webhook trigger."
        ) from exc
    # --- END REAL IMPLEMENTATION ---

# Ensure necessary imports are present at the top
from fastapi import HTTPException, status # Added for error handling