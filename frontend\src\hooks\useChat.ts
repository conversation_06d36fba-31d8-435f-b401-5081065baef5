import { useContext } from 'react';
// Adjust the import path based on the actual location of ChatContext
import { ChatContext, ChatContextProps } from '../contexts/ChatContext';

// Create and export the custom hook for using the chat context
export const useChat = (): ChatContextProps => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};