from fastapi import HTTPException, status
from uuid import UUID, uuid4
import datetime
import traceback
from typing import List, Dict, Any, Optional

from app.models.proceso import (
    ProcesoCreate, ProcesoUpdate, Proceso, ProcesoSummary,
    ProcesoListResponse, ProcesoFilters, TipoProceso
)
from app.core.database import get_supabase_client

async def create_proceso(proceso_in: ProcesoCreate, user_id: UUID) -> Proceso:
    """Create a new proceso."""
    supabase = await get_supabase_client()

    # Generate UUID for the new proceso
    proceso_id = uuid4()

    # Prepare proceso data
    proceso_data = {
        "id": str(proceso_id),
        "nombre": proceso_in.nombre,
        "descripcion": proceso_in.descripcion,
        "tipo_proceso": proceso_in.tipo_proceso.value,
        "empresa_id": str(proceso_in.empresa_id) if proceso_in.empresa_id else None,
        "departamento_id": str(proceso_in.departamento_id) if proceso_in.departamento_id else None,
        "persona_id": str(proceso_in.persona_id) if proceso_in.persona_id else None,
        "estado": proceso_in.estado.value,
        "es_repetitivo": proceso_in.es_repetitivo,
        "es_cuello_botella": proceso_in.es_cuello_botella,
        "es_manual": proceso_in.es_manual,
        "valor_negocio": proceso_in.valor_negocio,
        "complejidad_automatizacion": proceso_in.complejidad_automatizacion,
        "prioridad_automatizacion": proceso_in.prioridad_automatizacion,
        "tiempo_estimado_manual": proceso_in.tiempo_estimado_manual,
        "frecuencia": proceso_in.frecuencia,
        "estado_automatizacion": proceso_in.estado_automatizacion,
        "proceso_plantilla_origen_id": str(proceso_in.proceso_plantilla_origen_id) if proceso_in.proceso_plantilla_origen_id else None,
        "herramientas_utilizadas": proceso_in.herramientas_utilizadas,
        "info_adicional": proceso_in.info_adicional,
        "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }

    try:
        # Insert proceso
        result = supabase.table('procesos').insert(proceso_data).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create proceso"
            )

        # Fetch and return the created proceso
        return await get_proceso_by_id(proceso_id)

    except Exception as e:
        print(f"Error creating proceso: {e}")
        print(f"Stack trace: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating proceso: {str(e)}"
        )

async def get_proceso_by_id(proceso_id: UUID) -> Proceso:
    """Get a proceso by ID with related data."""
    supabase = await get_supabase_client()

    try:
        # Get proceso with related data
        result = supabase.table('procesos').select(
            """
            *,
            empresas!procesos_empresa_id_fkey(id, nombre, tipo_relacion),
            personas!procesos_persona_id_fkey(id, nombre, email),
            procesos_plantillas!procesos_proceso_plantilla_origen_id_fkey(nombre_plantilla)
            """
        ).eq('id', str(proceso_id)).maybe_single().execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proceso not found"
            )

        proceso_data = result.data

        # Get associated projects
        proyectos_result = supabase.table('proyectos_procesos').select(
            "proyectos(id, nombre, estado)"
        ).eq('proceso_id', str(proceso_id)).execute()

        proyectos_asociados = []
        if proyectos_result.data:
            proyectos_asociados = [p['proyectos'] for p in proyectos_result.data if p.get('proyectos')]

        # Get related tasks count (through projects)
        total_tareas = 0
        for proyecto in proyectos_asociados:
            tareas_result = supabase.table('tareas').select("id").eq('proyecto_id', proyecto['id']).execute()
            total_tareas += len(tareas_result.data) if tareas_result.data else 0

        # Build response
        proceso = Proceso(
            id=UUID(proceso_data['id']),
            nombre=proceso_data['nombre'],
            descripcion=proceso_data.get('descripcion'),
            tipo_proceso=TipoProceso(proceso_data['tipo_proceso']),
            empresa_id=UUID(proceso_data['empresa_id']) if proceso_data.get('empresa_id') else None,
            departamento_id=UUID(proceso_data['departamento_id']) if proceso_data.get('departamento_id') else None,
            persona_id=UUID(proceso_data['persona_id']) if proceso_data.get('persona_id') else None,
            es_repetitivo=proceso_data.get('es_repetitivo', False),
            es_cuello_botella=proceso_data.get('es_cuello_botella', False),
            es_manual=proceso_data.get('es_manual', True),
            valor_negocio=proceso_data.get('valor_negocio'),
            complejidad_automatizacion=proceso_data.get('complejidad_automatizacion'),
            prioridad_automatizacion=proceso_data.get('prioridad_automatizacion'),
            tiempo_estimado_manual=proceso_data.get('tiempo_estimado_manual'),
            frecuencia=proceso_data.get('frecuencia'),
            estado_automatizacion=proceso_data.get('estado_automatizacion'),
            proceso_plantilla_origen_id=UUID(proceso_data['proceso_plantilla_origen_id']) if proceso_data.get('proceso_plantilla_origen_id') else None,
            herramientas_utilizadas=proceso_data.get('herramientas_utilizadas'),
            info_adicional=proceso_data.get('info_adicional'),
            created_at=datetime.datetime.fromisoformat(proceso_data['created_at']),
            updated_at=datetime.datetime.fromisoformat(proceso_data['updated_at']),
            plantilla_origen=proceso_data.get('procesos_plantillas', {}).get('nombre_plantilla') if proceso_data.get('procesos_plantillas') else None,
            proyectos_asociados=proyectos_asociados,
            total_tareas=total_tareas
        )

        return proceso

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching proceso: {str(e)}"
        )

async def get_procesos(
    skip: int = 0,
    limit: int = 100,
    filters: Optional[ProcesoFilters] = None
) -> ProcesoListResponse:
    """Get list of procesos with filtering."""
    supabase = await get_supabase_client()

    try:
        # Build query
        query = supabase.table('procesos').select(
            """
            *,
            empresas!procesos_empresa_id_fkey(id, nombre),
            personas!procesos_persona_id_fkey(id, nombre),
            procesos_plantillas!procesos_proceso_plantilla_origen_id_fkey(nombre_plantilla)
            """,
            count='exact'
        )

        # Apply filters
        if filters:
            if filters.tipo_proceso:
                query = query.eq('tipo_proceso', filters.tipo_proceso.value)
            if filters.empresa_id:
                query = query.eq('empresa_id', str(filters.empresa_id))
            if filters.es_cuello_botella is not None:
                query = query.eq('es_cuello_botella', filters.es_cuello_botella)
            if filters.search:
                query = query.or_(f'nombre.ilike.%{filters.search}%,descripcion.ilike.%{filters.search}%')

        # Apply pagination
        query = query.range(skip, skip + limit - 1).order('created_at', desc=True)

        result = query.execute()

        procesos = []
        for proceso_data in result.data or []:
            # Get associated projects count
            proyectos_result = supabase.table('proyectos_procesos').select("proyecto_id").eq('proceso_id', proceso_data['id']).execute()
            total_proyectos = len(proyectos_result.data) if proyectos_result.data else 0

            proceso = Proceso(
                id=UUID(proceso_data['id']),
                nombre=proceso_data['nombre'],
                descripcion=proceso_data.get('descripcion'),
                tipo_proceso=TipoProceso(proceso_data['tipo_proceso']),
                empresa_id=UUID(proceso_data['empresa_id']) if proceso_data.get('empresa_id') else None,
                departamento_id=UUID(proceso_data['departamento_id']) if proceso_data.get('departamento_id') else None,
                persona_id=UUID(proceso_data['persona_id']) if proceso_data.get('persona_id') else None,
                es_repetitivo=proceso_data.get('es_repetitivo', False),
                es_cuello_botella=proceso_data.get('es_cuello_botella', False),
                es_manual=proceso_data.get('es_manual', True),
                valor_negocio=proceso_data.get('valor_negocio'),
                complejidad_automatizacion=proceso_data.get('complejidad_automatizacion'),
                prioridad_automatizacion=proceso_data.get('prioridad_automatizacion'),
                tiempo_estimado_manual=proceso_data.get('tiempo_estimado_manual'),
                frecuencia=proceso_data.get('frecuencia'),
                estado_automatizacion=proceso_data.get('estado_automatizacion'),
                proceso_plantilla_origen_id=UUID(proceso_data['proceso_plantilla_origen_id']) if proceso_data.get('proceso_plantilla_origen_id') else None,
                herramientas_utilizadas=proceso_data.get('herramientas_utilizadas'),
                info_adicional=proceso_data.get('info_adicional'),
                created_at=datetime.datetime.fromisoformat(proceso_data['created_at']),
                updated_at=datetime.datetime.fromisoformat(proceso_data['updated_at']),
                plantilla_origen=proceso_data.get('procesos_plantillas', {}).get('nombre_plantilla') if proceso_data.get('procesos_plantillas') else None,
                total_tareas=0  # Will be calculated if needed
            )
            procesos.append(proceso)

        return ProcesoListResponse(
            procesos=procesos,
            total=result.count or 0,
            page=skip // limit + 1,
            size=limit
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching procesos: {str(e)}"
        )

async def update_proceso(proceso_id: UUID, proceso_update: ProcesoUpdate) -> Proceso:
    """Update a proceso."""
    supabase = await get_supabase_client()

    try:
        # Prepare update data
        update_data = {}
        if proceso_update.nombre is not None:
            update_data['nombre'] = proceso_update.nombre
        if proceso_update.descripcion is not None:
            update_data['descripcion'] = proceso_update.descripcion
        if proceso_update.tipo_proceso is not None:
            update_data['tipo_proceso'] = proceso_update.tipo_proceso.value
        if proceso_update.empresa_id is not None:
            update_data['empresa_id'] = str(proceso_update.empresa_id)
        if proceso_update.departamento_id is not None:
            update_data['departamento_id'] = str(proceso_update.departamento_id)
        if proceso_update.persona_id is not None:
            update_data['persona_id'] = str(proceso_update.persona_id)
        if proceso_update.es_repetitivo is not None:
            update_data['es_repetitivo'] = proceso_update.es_repetitivo
        if proceso_update.es_cuello_botella is not None:
            update_data['es_cuello_botella'] = proceso_update.es_cuello_botella
        if proceso_update.es_manual is not None:
            update_data['es_manual'] = proceso_update.es_manual
        if proceso_update.valor_negocio is not None:
            update_data['valor_negocio'] = proceso_update.valor_negocio
        if proceso_update.complejidad_automatizacion is not None:
            update_data['complejidad_automatizacion'] = proceso_update.complejidad_automatizacion
        if proceso_update.prioridad_automatizacion is not None:
            update_data['prioridad_automatizacion'] = proceso_update.prioridad_automatizacion
        if proceso_update.tiempo_estimado_manual is not None:
            update_data['tiempo_estimado_manual'] = proceso_update.tiempo_estimado_manual
        if proceso_update.frecuencia is not None:
            update_data['frecuencia'] = proceso_update.frecuencia
        if proceso_update.estado_automatizacion is not None:
            update_data['estado_automatizacion'] = proceso_update.estado_automatizacion
        if proceso_update.proceso_plantilla_origen_id is not None:
            update_data['proceso_plantilla_origen_id'] = str(proceso_update.proceso_plantilla_origen_id)
        if proceso_update.herramientas_utilizadas is not None:
            update_data['herramientas_utilizadas'] = proceso_update.herramientas_utilizadas
        if proceso_update.info_adicional is not None:
            update_data['info_adicional'] = proceso_update.info_adicional

        update_data['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        # Update proceso
        result = supabase.table('procesos').update(update_data).eq('id', str(proceso_id)).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proceso not found"
            )

        return await get_proceso_by_id(proceso_id)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating proceso: {str(e)}"
        )

async def delete_proceso(proceso_id: UUID) -> Dict[str, str]:
    """Delete a proceso."""
    supabase = await get_supabase_client()

    try:
        # Check if proceso exists
        existing = supabase.table('procesos').select('id').eq('id', str(proceso_id)).maybe_single().execute()

        if not existing.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proceso not found"
            )

        # Delete proceso (cascade will handle related records)
        supabase.table('procesos').delete().eq('id', str(proceso_id)).execute()

        return {"message": "Proceso deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting proceso: {str(e)}"
        )

async def get_procesos_dashboard() -> List[ProcesoSummary]:
    """Get procesos for dashboard."""
    supabase = await get_supabase_client()

    try:
        # Get important procesos (cuellos de botella and recent ones)
        result = supabase.table('procesos').select(
            """
            id, nombre, tipo_proceso, tiempo_estimado_manual, es_cuello_botella,
            empresas!procesos_empresa_id_fkey(nombre),
            personas!procesos_persona_id_fkey(nombre)
            """
        ).order('es_cuello_botella', desc=True).order('created_at', desc=True).limit(8).execute()

        procesos_summary = []
        for proceso_data in result.data or []:
            proceso_summary = ProcesoSummary(
                id=UUID(proceso_data['id']),
                nombre=proceso_data['nombre'],
                tipo_proceso=TipoProceso(proceso_data['tipo_proceso']),
                empresa_nombre=proceso_data.get('empresas', {}).get('nombre') if proceso_data.get('empresas') else None,
                responsable_nombre=proceso_data.get('personas', {}).get('nombre') if proceso_data.get('personas') else None,
                tiempo_estimado_manual=proceso_data.get('tiempo_estimado_manual'),
                es_cuello_botella=proceso_data.get('es_cuello_botella', False)
            )
            procesos_summary.append(proceso_summary)

        return procesos_summary

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard procesos: {str(e)}"
        )

async def link_proceso_to_proyecto(proceso_id: UUID, proyecto_id: UUID) -> Dict[str, str]:
    """Link a proceso to a proyecto."""
    supabase = await get_supabase_client()

    try:
        # Check if both exist
        proceso_exists = supabase.table('procesos').select('id').eq('id', str(proceso_id)).maybe_single().execute()
        proyecto_exists = supabase.table('proyectos').select('id').eq('id', str(proyecto_id)).maybe_single().execute()

        if not proceso_exists.data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Proceso not found")
        if not proyecto_exists.data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Proyecto not found")

        # Check if link already exists
        existing_link = supabase.table('proyectos_procesos').select('id').eq('proceso_id', str(proceso_id)).eq('proyecto_id', str(proyecto_id)).maybe_single().execute()

        if existing_link.data:
            return {"message": "Link already exists"}

        # Create link
        link_data = {
            "id": str(uuid4()),
            "proceso_id": str(proceso_id),
            "proyecto_id": str(proyecto_id),
            "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }

        supabase.table('proyectos_procesos').insert(link_data).execute()

        return {"message": "Proceso linked to proyecto successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error linking proceso to proyecto: {str(e)}"
        )
