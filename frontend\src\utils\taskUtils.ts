import { Tarea, TareaUpdate } from '../types/tarea';
import { FieldValue } from '../hooks/useInlineEdit';

/**
 * Creates a wrapper function for updating task fields with type safety
 * @param updateTaskField - The function to update task fields
 * @returns A wrapper function that handles type conversion and validation
 */
export const createUpdateTaskFieldWrapper = (
  updateTaskField: (id: string, field: keyof TareaUpdate, value: string | number | boolean | null) => Promise<void>
) => {
  return async (id: string, field: keyof Tarea, value: FieldValue) => {
    // Type guard to check if field exists in TareaUpdate
    const isUpdateableField = (field: keyof Tarea): field is keyof TareaUpdate => {
      const updateableFields: (keyof TareaUpdate)[] = [
        'titulo', 'descripcion', 'proyecto_id', 'estado', 'prioridad', 'urgencia',
        'fecha_vencimiento', 'fecha_inicio', 'asignado_a',
        'tarea_padre_id', 'info_adicional'
      ];
      return updateableFields.includes(field as keyof TareaUpdate);
    };

    if (isUpdateableField(field) && value !== undefined) {
      await updateTaskField(id, field, value as string | number | boolean | null);
    }
  };
};

/**
 * Formats a date string to Spanish locale format
 * @param dateValue - The date value to format (string, number, or boolean)
 * @returns Formatted date string or '-' if invalid
 */
export const formatDate = (dateValue?: string | number | boolean | null): string => {
  if (!dateValue || typeof dateValue !== 'string') return '-';
  try {
    return new Date(dateValue).toLocaleDateString('es-ES');
  } catch {
    return '-';
  }
};

/**
 * Formats a date string to Spanish locale format with short month/day
 * @param dateString - The date string to format
 * @returns Formatted date string or null if invalid
 */
export const formatDateShort = (dateString?: string): string | null => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  } catch {
    return null;
  }
};

/**
 * Gets a human-readable text for days until/past due date
 * @param days - Number of days (negative for overdue, positive for future)
 * @returns Human-readable text or null if days is undefined
 */
export const getDaysText = (days?: number): string => {
  if (days === undefined || days === null) return '';
  if (days < 0) return `${Math.abs(days)}d vencida`;
  if (days === 0) return 'Vence hoy';
  if (days === 1) return 'Vence mañana';
  return `${days}d restantes`;
};

/**
 * Alternative version of getDaysText with different wording
 * @param days - Number of days (negative for overdue, positive for future)
 * @returns Human-readable text or empty string if days is undefined
 */
export const getDaysTextAlt = (days?: number): string => {
  if (days === undefined) return '';
  if (days < 0) return `Vencida hace ${Math.abs(days)} días`;
  if (days === 0) return 'Vence hoy';
  return `Vence en ${days} días`;
};

/**
 * Groups tasks by a specified field
 * @param tasks - Array of tasks to group
 * @param groupBy - Field to group by ('estado', 'prioridad', 'urgencia', 'proyecto')
 * @returns Object with grouped tasks
 */
export const groupTasks = (
  tasks: Tarea[], 
  groupBy: 'none' | 'estado' | 'prioridad' | 'urgencia' | 'proyecto'
): Record<string, Tarea[]> => {
  if (groupBy === 'none') return { 'Todas las tareas': tasks };
  
  const grouped: Record<string, Tarea[]> = {};
  
  tasks.forEach(task => {
    let key = '';
    switch (groupBy) {
      case 'estado':
        key = task.estado;
        break;
      case 'prioridad':
        key = task.prioridad;
        break;
      case 'urgencia':
        key = task.urgencia;
        break;
      case 'proyecto':
        key = task.proyecto_nombre || 'Sin proyecto';
        break;
    }
    
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(task);
  });
  
  return grouped;
};

/**
 * Determines the project type based on associated companies
 * @param task - The task to analyze
 * @returns 'Externo' if has companies, 'Interno (Aceleralia)' if not, null if no project
 */
export const getProjectType = (task: Tarea): string | null => {
  if (!task.proyecto_nombre) return null;

  const hasEmpresas = task.empresas_asociadas && task.empresas_asociadas.length > 0;
  return hasEmpresas ? 'Externo' : 'Interno (Aceleralia)';
};

/**
 * Checks if a task is overdue
 * @param task - The task to check
 * @returns true if task is overdue
 */
export const isTaskOverdue = (task: Tarea): boolean => {
  return task.es_vencida || (task.dias_vencimiento !== null && task.dias_vencimiento !== undefined && task.dias_vencimiento < 0);
};

/**
 * Checks if a task is due soon (within specified days)
 * @param task - The task to check
 * @param daysThreshold - Number of days to consider as "soon" (default: 3)
 * @returns true if task is due soon
 */
export const isTaskDueSoon = (task: Tarea, daysThreshold: number = 3): boolean => {
  return task.dias_vencimiento !== null && 
         task.dias_vencimiento !== undefined && 
         task.dias_vencimiento <= daysThreshold && 
         task.dias_vencimiento >= 0;
};
