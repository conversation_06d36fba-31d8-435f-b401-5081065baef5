# Frontend Deployment Guide

## Coolify Configuration

### Build Configuration

1. **Docker Context**: Set to `frontend`
2. **Dockerfile Location**: `Dockerfile` (in the frontend directory)
3. **Port Mappings**: `80:80` (not 3000:3000)

### Build Arguments (Environment Variables)

Configure these as **Build Arguments** in Coolify:

```
VITE_SUPABASE_URL=https://aceleralia-database.aceleralia.com/
VITE_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0MTE4OTQ0MCwiZXhwIjo0ODk2ODYzMDQwLCJyb2xlIjoiYW5vbiJ9.1ul6hLeDwOhMPosVltTeRJaoGpLYonC1rzlxUYdsGXg
VITE_API_URL=https://backend.aceleralia.com
VITE_ENVIRONMENT=production
```

### Important Notes

- **Build Arguments vs Environment Variables**: For Vite applications, variables must be set as **Build Arguments** because they are embedded into the JavaScript bundle at build time.
- **Port Configuration**: The application runs on port 80 inside the container, not 8080.
- **Static Site**: This is a static React application served by nginx.

### Troubleshooting

If the application is still making requests to `localhost:8000`:

1. Check that Build Arguments are correctly set in Coolify
2. Verify the Docker build logs show the correct environment variables
3. Check the browser console for the debug output showing the API URL
4. Rebuild the application after changing Build Arguments

### Local Testing

To test the production build locally:

```bash
# Build with production environment
npm run build:prod

# Preview the production build
npm run preview:prod
```

### Environment Files

- `.env` - Development environment (local)
- `.env.production` - Production environment (used by Vite in production mode)
- `.env.example` - Template with all required variables
