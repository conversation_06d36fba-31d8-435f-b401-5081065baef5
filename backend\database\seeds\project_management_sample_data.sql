-- Sample data for Project Management System
-- This script inserts sample data for testing and demonstration

-- Insert sample proyectos
INSERT INTO proyectos (id, nombre, descripcion, objetivo, estado, fecha_inicio, fecha_fin_estimada, presupuesto, prioridad, progreso) VALUES
(uuid_generate_v4(), 'Implementación CRM', 'Implementar un sistema CRM completo para mejorar la gestión de clientes', 'Aumentar la eficiencia en ventas en un 30%', 'En Ejecución', '2024-01-15', '2024-06-30', 50000.00, 'Alta', 45.5),
(uuid_generate_v4(), 'Migración a la Nube', 'Migrar toda la infraestructura a servicios cloud', 'Reducir costos operativos y mejorar escalabilidad', 'En Diseño', '2024-02-01', '2024-08-31', 75000.00, 'Urgente', 15.0),
(uuid_generate_v4(), 'Automatización de Procesos', 'Automatizar procesos manuales repetitivos', 'Reducir tiempo de procesamiento en 50%', 'Planificado', '2024-03-01', '2024-09-30', 30000.00, 'Media', 0.0),
(uuid_generate_v4(), 'Portal de Empleados', 'Desarrollar portal interno para empleados', 'Mejorar comunicación interna y autoservicio', 'En Revisión', '2023-11-01', '2024-02-29', 25000.00, 'Media', 85.0),
(uuid_generate_v4(), 'Análisis de Datos', 'Implementar sistema de Business Intelligence', 'Mejorar toma de decisiones basada en datos', 'Pausado', '2024-01-01', '2024-07-31', 40000.00, 'Baja', 25.0);

-- Insert sample procesos
INSERT INTO procesos (id, nombre, descripcion, tipo_proceso, estado, es_repetitivo, es_cuello_botella, es_manual, valor_negocio, tiempo_estimado_manual) VALUES
(uuid_generate_v4(), 'Onboarding de Clientes', 'Proceso completo de incorporación de nuevos clientes', 'Externo', 'En Progreso', true, false, true, 'Alto', 240),
(uuid_generate_v4(), 'Aprobación de Facturas', 'Proceso de revisión y aprobación de facturas', 'Interno', 'En Progreso', true, true, true, 'Medio', 30),
(uuid_generate_v4(), 'Gestión de Inventario', 'Control y seguimiento del inventario', 'Interno', 'Completado', true, false, false, 'Alto', 60),
(uuid_generate_v4(), 'Soporte Técnico', 'Atención y resolución de incidencias técnicas', 'Externo', 'En Progreso', true, true, true, 'Crítico', 45),
(uuid_generate_v4(), 'Backup de Datos', 'Proceso de respaldo de información crítica', 'Interno', 'Planificado', true, false, false, 'Crítico', 15);

-- Insert sample tareas
INSERT INTO tareas (id, titulo, descripcion, estado, prioridad, urgencia, fecha_vencimiento, asignado_a) VALUES
(uuid_generate_v4(), 'Configurar base de datos CRM', 'Instalar y configurar la base de datos para el sistema CRM', 'En Progreso', 'Alta', 'Urgente', '2024-02-15', NULL),
(uuid_generate_v4(), 'Diseñar interfaz de usuario', 'Crear mockups y prototipos de la interfaz', 'Pendiente', 'Media', 'No Urgente', '2024-02-20', NULL),
(uuid_generate_v4(), 'Implementar autenticación', 'Desarrollar sistema de login y seguridad', 'Completada', 'Alta', 'Urgente', '2024-02-10', NULL),
(uuid_generate_v4(), 'Migrar datos de clientes', 'Transferir datos existentes al nuevo sistema', 'Bloqueada', 'Urgente', 'Urgente', '2024-02-25', NULL),
(uuid_generate_v4(), 'Pruebas de integración', 'Realizar pruebas completas del sistema', 'Pendiente', 'Alta', 'No Urgente', '2024-03-01', NULL),
(uuid_generate_v4(), 'Documentar API', 'Crear documentación técnica de la API', 'En Progreso', 'Media', 'No Urgente', '2024-02-28', NULL),
(uuid_generate_v4(), 'Capacitar usuarios', 'Entrenar al equipo en el uso del nuevo sistema', 'Pendiente', 'Media', 'No Urgente', '2024-03-15', NULL),
(uuid_generate_v4(), 'Optimizar rendimiento', 'Mejorar velocidad de respuesta del sistema', 'En Revisión', 'Baja', 'No Urgente', '2024-03-10', NULL);

-- Insert sample plantillas de procesos
INSERT INTO procesos_plantillas (id, nombre_plantilla, descripcion_plantilla, objetivo_plantilla) VALUES
(uuid_generate_v4(), 'Plantilla Onboarding Cliente', 'Proceso estándar para incorporar nuevos clientes', 'Asegurar una experiencia consistente de onboarding'),
(uuid_generate_v4(), 'Plantilla Desarrollo Software', 'Proceso estándar para proyectos de desarrollo', 'Mantener calidad y tiempos en desarrollos'),
(uuid_generate_v4(), 'Plantilla Auditoría Interna', 'Proceso para realizar auditorías internas', 'Asegurar cumplimiento de procedimientos');

-- Insert sample plantillas de tareas
INSERT INTO tareas_plantillas (id, titulo_plantilla, descripcion_base, duracion_estimada_horas, prioridad_predeterminada) VALUES
(uuid_generate_v4(), 'Recopilar Requisitos', 'Reunir y documentar todos los requisitos del proyecto', 8.0, 'Alta'),
(uuid_generate_v4(), 'Análisis de Factibilidad', 'Evaluar la viabilidad técnica y económica', 4.0, 'Alta'),
(uuid_generate_v4(), 'Diseño de Arquitectura', 'Definir la arquitectura técnica del sistema', 12.0, 'Alta'),
(uuid_generate_v4(), 'Desarrollo Backend', 'Implementar la lógica de negocio del sistema', 40.0, 'Media'),
(uuid_generate_v4(), 'Desarrollo Frontend', 'Crear la interfaz de usuario', 32.0, 'Media'),
(uuid_generate_v4(), 'Pruebas Unitarias', 'Desarrollar y ejecutar pruebas unitarias', 16.0, 'Media'),
(uuid_generate_v4(), 'Pruebas de Integración', 'Realizar pruebas de integración del sistema', 8.0, 'Alta'),
(uuid_generate_v4(), 'Documentación Técnica', 'Crear documentación técnica completa', 6.0, 'Baja'),
(uuid_generate_v4(), 'Capacitación Usuarios', 'Entrenar a los usuarios finales', 4.0, 'Media'),
(uuid_generate_v4(), 'Despliegue Producción', 'Poner el sistema en producción', 2.0, 'Urgente');

-- Link some plantillas de tareas to plantillas de procesos
INSERT INTO procesos_tareas_plantillas (proceso_plantilla_id, plantilla_tarea_id, orden_en_proceso, dias_desplazamiento, es_obligatoria) 
SELECT 
    pp.id,
    tp.id,
    ROW_NUMBER() OVER (PARTITION BY pp.id ORDER BY tp.titulo_plantilla),
    (ROW_NUMBER() OVER (PARTITION BY pp.id ORDER BY tp.titulo_plantilla) - 1) * 2,
    true
FROM procesos_plantillas pp
CROSS JOIN tareas_plantillas tp
WHERE pp.nombre_plantilla = 'Plantilla Desarrollo Software'
AND tp.titulo_plantilla IN ('Recopilar Requisitos', 'Análisis de Factibilidad', 'Diseño de Arquitectura', 'Desarrollo Backend', 'Desarrollo Frontend', 'Pruebas de Integración', 'Despliegue Producción');

-- Insert some sample etiquetas associations
INSERT INTO tareas_etiquetas (tarea_id, etiqueta_id)
SELECT t.id, e.id
FROM tareas t
CROSS JOIN etiquetas e
WHERE (t.titulo LIKE '%CRM%' AND e.nombre = 'Cliente')
   OR (t.titulo LIKE '%Pruebas%' AND e.nombre = 'Testing')
   OR (t.titulo LIKE '%Documentar%' AND e.nombre = 'Documentación')
   OR (t.urgencia = 'Urgente' AND e.nombre = 'Urgente')
   OR (t.prioridad = 'Alta' AND e.nombre = 'Importante')
   OR (t.estado = 'Bloqueada' AND e.nombre = 'Bloqueada')
   OR (t.estado = 'En Revisión' AND e.nombre = 'Revisión');

-- Update some tareas to have proyecto_id (link to projects)
UPDATE tareas 
SET proyecto_id = (SELECT id FROM proyectos WHERE nombre = 'Implementación CRM' LIMIT 1)
WHERE titulo IN ('Configurar base de datos CRM', 'Diseñar interfaz de usuario', 'Implementar autenticación', 'Migrar datos de clientes');

UPDATE tareas 
SET proyecto_id = (SELECT id FROM proyectos WHERE nombre = 'Portal de Empleados' LIMIT 1)
WHERE titulo IN ('Pruebas de integración', 'Documentar API', 'Capacitar usuarios');

UPDATE tareas 
SET proyecto_id = (SELECT id FROM proyectos WHERE nombre = 'Migración a la Nube' LIMIT 1)
WHERE titulo IN ('Optimizar rendimiento');

-- Create some relationships between proyectos and procesos
INSERT INTO proyectos_procesos (proyecto_id, proceso_id)
SELECT p.id, pr.id
FROM proyectos p
CROSS JOIN procesos pr
WHERE (p.nombre = 'Implementación CRM' AND pr.nombre = 'Onboarding de Clientes')
   OR (p.nombre = 'Portal de Empleados' AND pr.nombre = 'Soporte Técnico')
   OR (p.nombre = 'Automatización de Procesos' AND pr.nombre IN ('Aprobación de Facturas', 'Gestión de Inventario'));

-- Add some comments for clarity
COMMENT ON TABLE proyectos IS 'Proyectos principales de la organización con datos de ejemplo';
COMMENT ON TABLE procesos IS 'Procesos de negocio con ejemplos de diferentes tipos y estados';
COMMENT ON TABLE tareas IS 'Tareas individuales con diferentes estados y prioridades';

-- Show summary of inserted data
DO $$
BEGIN
    RAISE NOTICE 'Datos de ejemplo insertados:';
    RAISE NOTICE '- % proyectos', (SELECT COUNT(*) FROM proyectos);
    RAISE NOTICE '- % procesos', (SELECT COUNT(*) FROM procesos);
    RAISE NOTICE '- % tareas', (SELECT COUNT(*) FROM tareas);
    RAISE NOTICE '- % plantillas de procesos', (SELECT COUNT(*) FROM procesos_plantillas);
    RAISE NOTICE '- % plantillas de tareas', (SELECT COUNT(*) FROM tareas_plantillas);
    RAISE NOTICE '- % etiquetas', (SELECT COUNT(*) FROM etiquetas);
END $$;
