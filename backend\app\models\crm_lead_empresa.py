from pydantic import BaseModel
from typing import Optional
from uuid import UUID
import datetime

class LeadEmpresaBase(BaseModel):
    nombre: str
    fuente: Optional[str] = None # Consider ENUM if predefined list (e.g., "Web", "Referral")
    estado: Optional[str] = None # Consider ENUM (e.g., "Nuevo", "Contactado", "Calificado")
    descripcion: Optional[str] = None
    asignado_usuario_id: Optional[UUID] = None # Link to usuarios.id
    # asignado_agente_id is in DB schema but not in form doc, can be added if needed
    info_adicional: Optional[str] = None

class LeadEmpresaCreate(LeadEmpresaBase):
    pass

class LeadEmpresa(LeadEmpresaBase):
    id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    # Fields from DB not in form: fecha_conversion, convertido_a_empresa_id, etc.
    # These are typically set later in the lead lifecycle.
    fecha_conversion: Optional[datetime.datetime] = None
    convertido_a_empresa_id: Optional[UUID] = None
    convertido_a_oportunidad_id: Optional[UUID] = None
    convertido_a_persona_id: Optional[UUID] = None


    class Config:
        from_attributes = True
