import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MeetingState } from '../../../types/meeting.types';
import LoadingSpinner from '../../UI/LoadingSpinner'; // Assuming this path is correct

interface ResultsStepProps {
  meetingState: MeetingState;
  onSetCurrentStep: (step: number) => void;
  // isWaitingForAI is derived from meetingState.status
  // connectionMethod is meetingState.connectionStatus
}

const ResultsStep: React.FC<ResultsStepProps> = ({
  meetingState,
  onSetCurrentStep,
}) => {
  const navigate = useNavigate();
  const isWaitingForAI = meetingState.status === 'procesando_ia';

  // Auto-navigate to meeting details when processing is completed
  useEffect(() => {
    if (meetingState.status === 'completado' && meetingState.id) {
      // Small delay to show the success message briefly
      const timer = setTimeout(() => {
        navigate(`/meetings/${meetingState.id}`);
      }, 2000); // 2 second delay

      return () => clearTimeout(timer);
    }
  }, [meetingState.status, meetingState.id, navigate]);

  const getStatusMessage = (status: string | null) => {
    switch (status) {
      case 'procesando_ia':
        return {
          title: 'Procesando con IA...',
          description: 'Generando resumen automático y extrayendo puntos clave.',
          estimated: 'Tiempo estimado: 30-60 segundos'
        };
      case 'completado':
        return {
          title: 'Procesamiento completado',
          description: 'La reunión ha sido procesada exitosamente.',
          estimated: ''
        };
      default:
        return {
          title: 'Estado Desconocido',
          description: `La reunión está en estado: ${status || 'No disponible'}.`,
          estimated: ''
        };
    }
  };

  const statusInfo = getStatusMessage(meetingState.status);

  // --- Common Form Styling ---
  const secondaryButtonClass = "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50";

  return (
    <div className="bg-white p-6 shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-700 mb-4">Paso 3: Resultados del Procesamiento</h2>

      {isWaitingForAI && (
        <div className="my-6">
          <LoadingSpinner
            message={statusInfo.title}
            subMessage={statusInfo.description}
            connectionStatus={meetingState.connectionStatus} // Pass the connection status
            estimatedTime={statusInfo.estimated}
          />
        </div>
      )}

      {meetingState.status === 'completado' && !isWaitingForAI && (
        <div className="space-y-4">
          <div className="p-4 bg-green-50 border border-green-300 rounded-md">
            <div className="flex items-center">
              <span className="text-green-600 text-lg mr-2">✅</span>
              <h3 className="text-md font-medium text-green-700">{statusInfo.title}</h3>
            </div>
            <p className="text-sm text-green-600 mt-1">{statusInfo.description}</p>
          </div>
          <div className="p-4 bg-blue-50 border border-blue-300 rounded-md">
            <div className="flex items-center">
              <span className="text-blue-600 text-lg mr-2">🔄</span>
              <h3 className="text-md font-medium text-blue-700">Redirigiendo automáticamente...</h3>
            </div>
            <p className="text-sm text-blue-600 mt-1">
              Serás redirigido a los detalles de la reunión en unos segundos.
              También puedes hacer <a
                href={`/meetings/${meetingState.id}`}
                className="text-indigo-600 hover:underline font-medium"
                onClick={(e) => {
                  e.preventDefault();
                  navigate(`/meetings/${meetingState.id}`);
                }}
              >clic aquí</a> para ir ahora.
            </p>
          </div>
        </div>
      )}

      {meetingState.status && meetingState.status !== 'completado' && !isWaitingForAI && (
        <div className="p-4 bg-blue-50 border border-blue-300 rounded-md">
          <h3 className="text-md font-medium text-blue-700">Estado Actual: {meetingState.status}</h3>
          <p className="text-sm text-blue-600 mt-1">{statusInfo.description}</p>
        </div>
      )}

       <div className="flex justify-start mt-6">
        <button
          onClick={() => onSetCurrentStep(2)} // Go back to speaker assignment
          className={secondaryButtonClass}
          disabled={isWaitingForAI}
        >
          Anterior (Asignar Speakers)
        </button>
      </div>
    </div>
  );
};

export default ResultsStep;