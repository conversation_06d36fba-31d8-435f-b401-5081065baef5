from fastapi import APIRouter, Depends, HTTPException, status # Removed UploadFile, File, Form
from uuid import UUID
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.models.reunion import ReunionCreate, Reunion as ReunionResponse, SpeakerAssignmentCreate
from app.services import reunion_service
from app.core.security import get_current_user_id

router = APIRouter()

# Pydantic model for adding associations
class AddAssociationRequest(BaseModel):
    entity_id: UUID
    entity_type: str  # 'persona' or 'leadContacto'

# Pydantic model for updating entrevista field
class UpdateEntrevistaRequest(BaseModel):
    entrevista: bool

# Pydantic model for updating video field
class UpdateVideoRequest(BaseModel):
    video: bool

@router.post("/", response_model=ReunionResponse, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=ReunionResponse, status_code=status.HTTP_201_CREATED)  # Add route without trailing slash
async def create_new_reunion(
    reunion_in: ReunionCreate, # Expects a JSON body matching the updated ReunionCreate model
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new reunion record using file reference from frontend TUS upload.
    The request body should contain all reunion metadata including
    `url_grabacion_publica` and `file_storage_path`.
    """
    try:
        # The ReunionCreate model now includes url_grabacion_publica and file_storage_path
        # Validation of these fields (e.g., if they are proper URLs/paths) is handled by Pydantic.

        created_reunion_data = await reunion_service.create_reunion_record_from_file_reference(
            reunion_in=reunion_in,
            user_id=current_user_id
        )
        return ReunionResponse(**created_reunion_data)

    except HTTPException as he: # Re-raise HTTPExceptions from service
        raise he
    except Exception as e:
        # Log the exception e
        print(f"Error in create_new_reunion endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create reunion: {str(e)}"
        )

@router.put("/{reunion_id}/speaker_assignments", response_model=ReunionResponse)
async def assign_speakers_to_reunion(
    reunion_id: UUID,
    speaker_assignments: List[SpeakerAssignmentCreate], # Expect a list of assignments in request body
    current_user_id: UUID = Depends(get_current_user_id)
    # db: Session = Depends(get_db) # If needed
):
    """
    Assign speakers to a reunion, generate final transcript, and trigger AI analysis.
    """
    try:
        updated_reunion_data = await reunion_service.assign_speakers_and_trigger_analysis(
            reunion_id=reunion_id,
            speaker_assignments_in=speaker_assignments,
            user_id=current_user_id
        )
        return ReunionResponse(**updated_reunion_data)
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in assign_speakers_to_reunion endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign speakers: {str(e)}"
        )

@router.get("/", response_model=List[ReunionResponse])
@router.get("", response_model=List[ReunionResponse])  # Add route without trailing slash
async def get_all_user_reuniones(
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id)
    # db: Session = Depends(get_db) # If needed
):
    """
    Retrieve all reunions for the current authenticated user.
    """
    try:
        reuniones_data = await reunion_service.get_all_reuniones_for_user(
            user_id=current_user_id, skip=skip, limit=limit
        )
        return [ReunionResponse(**reunion) for reunion in reuniones_data]
    except Exception as e:
        print(f"Error in get_all_user_reuniones endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve reunions: {str(e)}"
        )

@router.get("/{reunion_id}", response_model=ReunionResponse)
async def get_specific_reunion(
    reunion_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
    # db: Session = Depends(get_db) # If needed
):
    """
    Retrieve a specific reunion by its ID for the current authenticated user.
    """
    try:
        # The service now returns a Reunion Pydantic model instance
        reunion_model_instance = await reunion_service.get_reunion_by_id(
            reunion_id=reunion_id, user_id=current_user_id
        )
        if not reunion_model_instance:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found or access denied.")
        return reunion_model_instance # Directly return the Pydantic model instance
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in get_specific_reunion endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve reunion: {str(e)}"
        )

@router.post("/{reunion_id}/associations", status_code=status.HTTP_201_CREATED)
async def add_association_to_reunion(
    reunion_id: UUID,
    association_request: AddAssociationRequest,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Add a new association (persona or leadContacto) to an existing reunion.
    This is used when creating new entities in Step 2 of the meeting processing.
    """
    try:
        result = await reunion_service.add_association_to_reunion(
            reunion_id=reunion_id,
            entity_id=association_request.entity_id,
            entity_type=association_request.entity_type,
            user_id=current_user_id
        )
        return result
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in add_association_to_reunion endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add association: {str(e)}"
        )

@router.patch("/{reunion_id}/entrevista", response_model=ReunionResponse)
async def update_reunion_entrevista(
    reunion_id: UUID,
    entrevista_request: UpdateEntrevistaRequest,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update the 'entrevista' field of a reunion.
    This is used in Step 2 to mark if the meeting is an interview.
    """
    try:
        result = await reunion_service.update_reunion_entrevista(
            reunion_id=reunion_id,
            entrevista=entrevista_request.entrevista,
            user_id=current_user_id
        )
        return result
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in update_reunion_entrevista endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update entrevista field: {str(e)}"
        )

@router.patch("/{reunion_id}/video", response_model=ReunionResponse)
async def update_reunion_video(
    reunion_id: UUID,
    video_request: UpdateVideoRequest,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update the 'video' field of a reunion.
    This is used in Step 2 to mark if the meeting video should be included in processing.
    """
    try:
        result = await reunion_service.update_reunion_video(
            reunion_id=reunion_id,
            video=video_request.video,
            user_id=current_user_id
        )
        return result
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in update_reunion_video endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update video field: {str(e)}"
        )

@router.delete("/{reunion_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_reunion_endpoint(
    reunion_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a specific reunion by its ID.
    This will also delete associated records and the file from storage.
    """
    try:
        success = await reunion_service.delete_reunion(
            reunion_id=reunion_id,
            user_id=current_user_id
        )
        if not success:
            # This case might occur if the service function returns False for a non-exception reason,
            # e.g., reunion not found but not raising HTTPException directly.
            # However, delete_reunion is expected to raise HTTPException for not found or auth errors.
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found or deletion failed.")
        # On success, a 204 No Content is returned automatically by FastAPI
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in delete_reunion_endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete reunion: {str(e)}"
        )
