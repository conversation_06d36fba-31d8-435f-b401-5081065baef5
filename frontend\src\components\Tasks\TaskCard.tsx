import React, { memo, useState } from 'react';
import { TareaSummary, PRIORIDAD_TAREA_COLORS, URGENCIA_COLORS } from '../../types/tarea';
import { Calendar, User, AlertTriangle, MoreVertical, Trash2, Building } from 'lucide-react';
import { formatDate, getDaysText } from '../../utils/taskUtils';

interface TaskCardProps {
  task: TareaSummary;
  onTaskClick: (taskId: string) => void;
  onDeleteTask: (taskId: string, taskTitle: string) => Promise<void>;
  onDragStart?: (e: React.DragEvent, task: TareaSummary) => void;
  isDragging?: boolean;
  showActions?: boolean;
  compact?: boolean;
}

const TaskCard: React.FC<TaskCardProps> = memo(({
  task,
  onTaskClick,
  onDeleteTask,
  onDragStart,
  isDragging = false,
  showActions = true,
  compact = false
}) => {
  const [showMenu, setShowMenu] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent navigation when clicking on menu or actions
    if ((e.target as HTMLElement).closest('.task-card-menu')) {
      return;
    }
    onTaskClick(task.id);
  };

  const handleDeleteClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    await onDeleteTask(task.id, task.titulo);
  };

  const getPriorityColor = (prioridad: string) => {
    return PRIORIDAD_TAREA_COLORS[prioridad as keyof typeof PRIORIDAD_TAREA_COLORS] || 'bg-gray-100 text-gray-800';
  };

  const getUrgencyColor = (urgencia: string) => {
    return URGENCIA_COLORS[urgencia as keyof typeof URGENCIA_COLORS] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div
      draggable={!!onDragStart}
      onDragStart={onDragStart ? (e) => onDragStart(e, task) : undefined}
      className={`
        bg-white rounded-lg border p-3 mb-3 cursor-move hover:shadow-md transition-all
        ${task.es_vencida ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'}
        ${isDragging ? 'opacity-50' : ''}
        ${compact ? 'p-2' : 'p-3'}
      `}
      onClick={handleCardClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <h4 
          className={`font-medium text-gray-900 line-clamp-2 flex-1 cursor-pointer hover:text-blue-600 ${
            compact ? 'text-xs' : 'text-sm'
          }`}
        >
          {task.titulo}
        </h4>
        <div className="flex items-center space-x-1 ml-2">
          {task.es_vencida && (
            <AlertTriangle className={`text-red-500 flex-shrink-0 ${compact ? 'h-3 w-3' : 'h-4 w-4'}`} />
          )}
          {showActions && (
            <div className="relative task-card-menu">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMenu(!showMenu);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <MoreVertical className={compact ? 'h-3 w-3' : 'h-4 w-4'} />
              </button>
              {showMenu && (
                <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowMenu(false);
                      onTaskClick(task.id);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Ver detalles
                  </button>
                  <button
                    onClick={handleDeleteClick}
                    className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                  >
                    <Trash2 className="h-3 w-3 mr-2" />
                    Eliminar
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Description - TareaSummary doesn't have descripcion field */}

      {/* Priority and Urgency badges */}
      <div className="flex items-center space-x-2 mb-2">
        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.prioridad)}`}>
          {task.prioridad}
        </span>
        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getUrgencyColor(task.urgencia)}`}>
          {task.urgencia}
        </span>
      </div>

      {/* Footer */}
      <div className="space-y-1">
        {/* Assigned user */}
        {task.asignado_nombre && (
          <div className={`flex items-center text-gray-600 ${compact ? 'text-xs' : 'text-xs'}`}>
            <User className={`mr-1 ${compact ? 'h-3 w-3' : 'h-3 w-3'}`} />
            <span className="truncate">{task.asignado_nombre}</span>
          </div>
        )}

        {/* Due date */}
        {task.fecha_vencimiento && (
          <div className={`flex items-center ${
            task.es_vencida ? 'text-red-600' : 'text-gray-600'
          } ${compact ? 'text-xs' : 'text-xs'}`}>
            <Calendar className={`mr-1 ${compact ? 'h-3 w-3' : 'h-3 w-3'}`} />
            <div>
              <div>{formatDate(task.fecha_vencimiento)}</div>
              {task.dias_vencimiento !== undefined && (
                <div className="text-xs">
                  {getDaysText(task.dias_vencimiento)}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Project */}
        {task.proyecto_nombre && (
          <div className={`flex items-center text-gray-500 truncate ${compact ? 'text-xs' : 'text-xs'}`}>
            <Building className={`mr-1 ${compact ? 'h-3 w-3' : 'h-3 w-3'}`} />
            <span className="truncate">{task.proyecto_nombre}</span>
          </div>
        )}

        {/* Associated companies - TareaSummary doesn't have empresas_asociadas field */}
      </div>

      {/* Click outside handler for menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
});

TaskCard.displayName = 'TaskCard';

export default TaskCard;
