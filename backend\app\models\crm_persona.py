from pydantic import BaseModel, EmailStr, HttpUrl
from typing import Optional, List
from uuid import UUID
import datetime

# Basic model for nested Empresa information
class EmpresaBasicInfo(BaseModel):
    id: UUID
    nombre: str

    class Config:
        from_attributes = True

class PersonaBase(BaseModel):
    nombre: str
    apellidos: str
    email: Optional[EmailStr] = None
    telefono: Optional[str] = None
    cargo: Optional[str] = None
    empresa_id: Optional[UUID] = None # Link to existing empresa
    departamento_id: Optional[UUID] = None # Link to existing departamento
    tipo: Optional[List[str]] = None # Array of text, e.g., ["Contacto Principal", "Tecnico"]
    es_decision_maker: Optional[bool] = False
    responsable_departamento: Optional[bool] = False
    linkedin_url: Optional[HttpUrl] = None
    activo: bool = True
    info_adicional: Optional[str] = None

class PersonaCreate(PersonaBase):
    pass

class Persona(PersonaBase):
    id: UUID
    # fecha_alta and fecha_baja are in DB but not typically set on creation via this form
    fecha_alta: Optional[datetime.datetime] = None # From DB
    fecha_baja: Optional[datetime.datetime] = None # From DB
    
    empresa: Optional[EmpresaBasicInfo] = None # For nested data from service

    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True
