import React from 'react';
import { CheckIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface StepTransitionProps {
  currentStep: number;
  status: string | null;
  title: string;
  description: string;
  estimatedTime?: string;
  progress?: number;
}

const StepTransition: React.FC<StepTransitionProps> = ({
  currentStep,
  status,
  title,
  description,
  estimatedTime,
  progress = 0
}) => {
  const getStepIcon = (stepNumber: number) => {
    if (currentStep > stepNumber) {
      return <CheckIcon className="h-6 w-6 text-green-500" />;
    } else if (currentStep === stepNumber) {
      return <ClockIcon className="h-6 w-6 text-blue-500 animate-pulse" />;
    } else {
      return <div className="h-6 w-6 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStepStatus = (stepNumber: number) => {
    if (currentStep > stepNumber) return 'completed';
    if (currentStep === stepNumber) return 'current';
    return 'pending';
  };

  const steps = [
    { number: 1, title: 'Subida de archivo', description: 'Archivo cargado y enviado' },
    { number: 1.5, title: 'Transcripción', description: 'Procesando audio a texto' },
    { number: 2, title: 'Asignación de participantes', description: 'Identificar quién habla' },
    { number: 2.5, title: 'Análisis con IA', description: 'Generando resumen y puntos clave' },
    { number: 3, title: 'Completado', description: 'Reunión procesada exitosamente' }
  ];

  const isError = status?.startsWith('error');

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            {isError ? (
              <ExclamationTriangleIcon className="h-16 w-16 text-red-500" />
            ) : (
              <div className="relative">
                <div className="h-16 w-16 rounded-full border-4 border-blue-200 flex items-center justify-center">
                  <ClockIcon className="h-8 w-8 text-blue-500 animate-pulse" />
                </div>
                {progress > 0 && (
                  <div className="absolute inset-0 rounded-full border-4 border-transparent">
                    <div 
                      className="h-full w-full rounded-full border-4 border-blue-500 border-t-transparent animate-spin"
                      style={{ 
                        background: `conic-gradient(from 0deg, #3b82f6 ${progress * 3.6}deg, transparent ${progress * 3.6}deg)` 
                      }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
          <h1 className={`text-2xl font-bold mb-2 ${isError ? 'text-red-600' : 'text-gray-800'}`}>
            {isError ? 'Error en el procesamiento' : title}
          </h1>
          <p className={`text-lg ${isError ? 'text-red-600' : 'text-gray-600'}`}>
            {description}
          </p>
          {estimatedTime && !isError && (
            <p className="text-sm text-gray-500 mt-2">{estimatedTime}</p>
          )}
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => {
              const stepStatus = getStepStatus(step.number);
              return (
                <div key={step.number} className="flex flex-col items-center flex-1">
                  <div className={`
                    flex items-center justify-center w-12 h-12 rounded-full border-2 mb-2
                    ${stepStatus === 'completed' ? 'bg-green-100 border-green-500' : 
                      stepStatus === 'current' ? 'bg-blue-100 border-blue-500' : 
                      'bg-gray-100 border-gray-300'}
                  `}>
                    {getStepIcon(step.number)}
                  </div>
                  <div className="text-center">
                    <p className={`text-sm font-medium ${
                      stepStatus === 'completed' ? 'text-green-600' :
                      stepStatus === 'current' ? 'text-blue-600' :
                      'text-gray-400'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{step.description}</p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`
                      absolute top-6 left-1/2 w-full h-0.5 -z-10
                      ${stepStatus === 'completed' ? 'bg-green-500' : 'bg-gray-300'}
                    `} style={{ transform: 'translateX(50%)' }} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Progress Bar */}
        {progress > 0 && !isError && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progreso</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Status Message */}
        <div className="text-center">
          <p className="text-gray-600">
            {isError ? 
              'Ha ocurrido un error durante el procesamiento. Por favor, inténtelo de nuevo.' :
              'Por favor, mantenga esta página abierta mientras se completa el procesamiento.'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default StepTransition;
