from uuid import UUID
from datetime import datetime
from typing import Optional, Literal

from pydantic import BaseModel

class DocExportBase(BaseModel):
    threads_row_id: UUID # Renamed from message_id to match your DB change
    user_id: UUID
    status: Literal['pending', 'success', 'error']
    doc_url: Optional[str] = None

class DocExportCreate(DocExportBase):
    pass

class DocExport(DocExportBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        # orm_mode = True # For SQLAlchemy compatibility, good practice - Deprecated in V2
        # In Pydantic V2, orm_mode is from_attributes
        from_attributes = True

class DocExportUpdate(BaseModel):
    status: Optional[Literal['pending', 'success', 'error']] = None
    doc_url: Optional[str] = None