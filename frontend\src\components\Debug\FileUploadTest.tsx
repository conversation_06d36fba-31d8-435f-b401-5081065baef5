import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import FileDropzone from '../UI/FileDropzone';
import { useToastContext } from '../../hooks/useToastContext';

// Test schema similar to the one in UploadStep
const testSchema = z.object({
  file: z
    .any()
    .refine((files) => {
      if (!files) return false;
      if (files instanceof FileList) return files.length > 0;
      if (typeof files === 'object' && files.length !== undefined) return files.length > 0;
      return false;
    }, "Debe seleccionar un archivo.")
    .refine((files) => {
      const file = files instanceof FileList ? files[0] : files[0];
      if (!file) return false;
      return file.type.startsWith('audio/') || file.type.startsWith('video/');
    }, "El archivo debe ser de audio o video.")
    .refine((files) => {
      const file = files instanceof FileList ? files[0] : files[0];
      if (!file) return false;
      return file.size <= 500 * 1024 * 1024; // 500MB
    }, "El archivo no puede ser mayor a 500MB."),
  title: z.string().min(1, "El título es requerido"),
});

type TestFormValues = z.infer<typeof testSchema>;

const FileUploadTest: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { success, error: showError } = useToastContext();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<TestFormValues>({
    resolver: zodResolver(testSchema),
  });

  const onSubmit = (data: TestFormValues) => {
    console.log('Form submitted:', data);
    success('Formulario enviado', 'Los datos se han procesado correctamente');
  };

  const getErrorMessage = (error: unknown): string => {
    if (typeof error === 'string') return error;
    if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
      return error.message;
    }
    return 'Error en el campo';
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Test de Subida de Archivos</h2>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Archivo de prueba
          </label>
          <FileDropzone
            onFileSelect={(file) => {
              setSelectedFile(file);
              // Create a compatible FileList
              const dt = new DataTransfer();
              dt.items.add(file);
              const fileList = dt.files;
              setValue('file', fileList);
            }}
            currentFile={selectedFile}
            acceptedTypes={['audio/*', 'video/*']}
            maxSize={500 * 1024 * 1024}
          />
          {errors.file && (
            <p className="mt-1 text-sm text-red-600">{getErrorMessage(errors.file)}</p>
          )}
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Título
          </label>
          <input
            type="text"
            {...register('title')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Ingrese un título"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{getErrorMessage(errors.title)}</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          Enviar Formulario de Prueba
        </button>
      </form>

      {/* Debug Info */}
      {selectedFile && (
        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Información del archivo:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li><strong>Nombre:</strong> {selectedFile.name}</li>
            <li><strong>Tipo:</strong> {selectedFile.type}</li>
            <li><strong>Tamaño:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</li>
            <li><strong>Última modificación:</strong> {new Date(selectedFile.lastModified).toLocaleString()}</li>
          </ul>
        </div>
      )}

      {/* Test Buttons */}
      <div className="mt-6 flex space-x-4">
        <button
          type="button"
          onClick={() => success('Test Success', 'Esta es una notificación de éxito')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Test Success Toast
        </button>
        <button
          type="button"
          onClick={() => showError('Test Error', 'Esta es una notificación de error')}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Test Error Toast
        </button>
      </div>
    </div>
  );
};

export default FileUploadTest;
