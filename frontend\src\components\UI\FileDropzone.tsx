import React, { useCallback, useState } from 'react';
import { CloudArrowUpIcon, DocumentIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface FileDropzoneProps {
  onFileSelect: (file: File) => void;
  acceptedTypes?: string[];
  maxSize?: number; // in bytes
  disabled?: boolean;
  currentFile?: File | null;
  className?: string;
}

const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFileSelect,
  acceptedTypes = ['audio/*', 'video/*'],
  maxSize = 500 * 1024 * 1024, // 500MB default
  disabled = false,
  currentFile,
  className = ''
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (file.size > maxSize) {
      return `El archivo es demasiado grande. Tamaño máximo: ${formatFileSize(maxSize)}`;
    }

    // Check file type
    const isValidType = acceptedTypes.some(type => {
      if (type.endsWith('/*')) {
        const baseType = type.replace('/*', '');
        return file.type.startsWith(baseType);
      }
      return file.type === type;
    });

    if (!isValidType) {
      return `Tipo de archivo no válido. Tipos aceptados: ${acceptedTypes.join(', ')}`;
    }

    return null;
  }, [maxSize, acceptedTypes]);

  const handleFileSelect = useCallback((file: File) => {
    setError(null);
    const validationError = validateFile(file);

    if (validationError) {
      setError(validationError);
      return;
    }

    onFileSelect(file);
  }, [onFileSelect, validateFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled, handleFileSelect]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);



  const getDropzoneClasses = () => {
    const baseClasses = `
      relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
      ${className}
    `;

    if (disabled) {
      return `${baseClasses} border-gray-200 bg-gray-50 cursor-not-allowed`;
    }

    if (error) {
      return `${baseClasses} border-red-300 bg-red-50`;
    }

    if (isDragOver) {
      return `${baseClasses} border-blue-400 bg-blue-50`;
    }

    if (currentFile) {
      return `${baseClasses} border-green-300 bg-green-50`;
    }

    return `${baseClasses} border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100 cursor-pointer`;
  };

  return (
    <div className="w-full">
      <div
        className={getDropzoneClasses()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById('file-input')?.click()}
      >
        <input
          id="file-input"
          type="file"
          className="hidden"
          accept={acceptedTypes.join(',')}
          onChange={handleInputChange}
          disabled={disabled}
        />

        <div className="flex flex-col items-center space-y-4">
          {/* Icon */}
          <div className="flex-shrink-0">
            {error ? (
              <ExclamationTriangleIcon className="h-12 w-12 text-red-400" />
            ) : currentFile ? (
              <DocumentIcon className="h-12 w-12 text-green-500" />
            ) : (
              <CloudArrowUpIcon className={`h-12 w-12 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
            )}
          </div>

          {/* Content */}
          <div className="text-center">
            {error ? (
              <div>
                <p className="text-sm font-medium text-red-600">Error</p>
                <p className="text-xs text-red-500 mt-1">{error}</p>
              </div>
            ) : currentFile ? (
              <div>
                <p className="text-sm font-medium text-green-600">Archivo seleccionado</p>
                <p className="text-xs text-gray-600 mt-1">{currentFile.name}</p>
                <p className="text-xs text-gray-500">{formatFileSize(currentFile.size)}</p>
              </div>
            ) : (
              <div>
                <p className={`text-sm font-medium ${isDragOver ? 'text-blue-600' : 'text-gray-600'}`}>
                  {isDragOver ? 'Suelta el archivo aquí' : 'Arrastra y suelta tu archivo aquí'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  o <span className="text-blue-600 underline">haz clic para seleccionar</span>
                </p>
              </div>
            )}
          </div>

          {/* File requirements */}
          {!currentFile && !error && (
            <div className="text-xs text-gray-500 space-y-1">
              <p>Tipos aceptados: Audio y Video</p>
              <p>Tamaño máximo: {formatFileSize(maxSize)}</p>
            </div>
          )}

          {/* Change file button */}
          {currentFile && !disabled && (
            <button
              type="button"
              className="text-xs text-blue-600 hover:text-blue-800 underline"
              onClick={(e) => {
                e.stopPropagation();
                document.getElementById('file-input')?.click();
              }}
            >
              Cambiar archivo
            </button>
          )}
        </div>
      </div>

      {/* Error message outside dropzone */}
      {error && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}
    </div>
  );
};

export default FileDropzone;
