import { useMemo, useRef, useCallback } from 'react';
import { ChatMessage } from '../contexts/ChatContext';

// Simple token estimation (words * 1.3 for approximate token count)
const estimateTokens = (text: string): number => {
  if (!text || typeof text !== 'string') return 0;
  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  return Math.ceil(words.length * 1.3); // Rough approximation
};

// Cache for token counts to avoid recalculation
const tokenCache = new Map<string, number>();
const MAX_CACHE_SIZE = 1000;

// Optimized token counting with caching and memoization
export const useOptimizedTokenCount = (messages: ChatMessage[]) => {
  const lastCalculatedRef = useRef<{
    messageCount: number;
    totalTokens: number;
    messageIds: string[];
  }>({
    messageCount: 0,
    totalTokens: 0,
    messageIds: [],
  });

  const calculateTokens = useCallback((content: string, messageId?: string): number => {
    if (!content) return 0;

    // Use cache if available
    const cacheKey = messageId || content.substring(0, 100);
    if (tokenCache.has(cacheKey)) {
      return tokenCache.get(cacheKey)!;
    }

    const tokens = estimateTokens(content);

    // Cache the result (with size limit)
    if (tokenCache.size >= MAX_CACHE_SIZE) {
      // Remove oldest entries
      const keysToDelete = Array.from(tokenCache.keys()).slice(0, 100);
      keysToDelete.forEach(key => tokenCache.delete(key));
    }
    
    tokenCache.set(cacheKey, tokens);
    return tokens;
  }, []);

  const totalTokens = useMemo(() => {
    const currentMessageIds = messages.map(msg => msg.id || msg.created_at).filter(Boolean);
    const lastCalculated = lastCalculatedRef.current;

    // Quick check: if message count and IDs haven't changed, return cached result
    if (
      messages.length === lastCalculated.messageCount &&
      currentMessageIds.length === lastCalculated.messageIds.length &&
      currentMessageIds.every((id, index) => id === lastCalculated.messageIds[index])
    ) {
      return lastCalculated.totalTokens;
    }

    // Calculate tokens for new/changed messages only
    let newTotalTokens = 0;
    const processedIds: string[] = [];

    for (const message of messages) {
      const messageId = message.id || message.created_at;
      processedIds.push(messageId);

      // Calculate tokens for main content
      if (message.content) {
        newTotalTokens += calculateTokens(message.content, messageId);
      }

      // Calculate tokens for intermediate steps (if any)
      if (message.intermediate_steps && message.intermediate_steps.length > 0) {
        for (const step of message.intermediate_steps) {
          if (step.content) {
            const stepId = `${messageId}-step-${step.id || step.created_at}`;
            newTotalTokens += calculateTokens(step.content, stepId);
          }
        }
      }
    }

    // Cache the result
    lastCalculatedRef.current = {
      messageCount: messages.length,
      totalTokens: newTotalTokens,
      messageIds: processedIds,
    };

    return newTotalTokens;
  }, [messages, calculateTokens]);

  // Function to get token count for a specific message (useful for individual message analysis)
  const getMessageTokens = useCallback((message: ChatMessage): number => {
    let tokens = 0;
    
    if (message.content) {
      tokens += calculateTokens(message.content, message.id);
    }

    if (message.intermediate_steps && message.intermediate_steps.length > 0) {
      for (const step of message.intermediate_steps) {
        if (step.content) {
          const stepId = `${message.id}-step-${step.id || step.created_at}`;
          tokens += calculateTokens(step.content, stepId);
        }
      }
    }

    return tokens;
  }, [calculateTokens]);

  // Function to clear token cache (useful for memory management)
  const clearTokenCache = useCallback(() => {
    tokenCache.clear();
    lastCalculatedRef.current = {
      messageCount: 0,
      totalTokens: 0,
      messageIds: [],
    };
  }, []);

  return {
    totalTokens,
    getMessageTokens,
    clearTokenCache,
    cacheSize: tokenCache.size,
  };
};

// Hook for monitoring performance metrics
export const usePerformanceMetrics = () => {
  const metricsRef = useRef({
    renderCount: 0,
    lastRenderTime: Date.now(),
    averageRenderTime: 0,
    renderTimes: [] as number[],
  });

  const recordRender = useCallback(() => {
    const now = Date.now();
    const renderTime = now - metricsRef.current.lastRenderTime;
    
    metricsRef.current.renderCount++;
    metricsRef.current.lastRenderTime = now;
    metricsRef.current.renderTimes.push(renderTime);

    // Keep only last 10 render times for average calculation
    if (metricsRef.current.renderTimes.length > 10) {
      metricsRef.current.renderTimes.shift();
    }

    metricsRef.current.averageRenderTime = 
      metricsRef.current.renderTimes.reduce((sum, time) => sum + time, 0) / 
      metricsRef.current.renderTimes.length;
  }, []);

  const getMetrics = useCallback(() => ({
    renderCount: metricsRef.current.renderCount,
    averageRenderTime: Math.round(metricsRef.current.averageRenderTime),
    lastRenderTime: metricsRef.current.lastRenderTime,
  }), []);

  const resetMetrics = useCallback(() => {
    metricsRef.current = {
      renderCount: 0,
      lastRenderTime: Date.now(),
      averageRenderTime: 0,
      renderTimes: [],
    };
  }, []);

  return {
    recordRender,
    getMetrics,
    resetMetrics,
  };
};
