import React, { memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Proyecto, ESTADOS_PROYECTO, PRIORIDADES_PROYECTO, ESTADO_COLORS } from '../../types/proyecto';
import { EditableField } from '../UI/EditableField';
import { ArrowLeft, Trash2 } from 'lucide-react';

interface ProjectHeaderProps {
  project: Proyecto;
  onUpdateProject: (field: keyof Proyecto, value: string | number | boolean | null) => Promise<void>;
  onDeleteProject: () => Promise<void>;
}

const ProjectHeader: React.FC<ProjectHeaderProps> = memo(({
  project,
  onUpdateProject,
  onDeleteProject
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-between mb-8">
      <div className="flex items-center">
        <button
          onClick={() => navigate('/proyectos')}
          className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <EditableField
            label="Nombre del proyecto"
            value={project.nombre}
            type="text"
            onSave={(value) => onUpdateProject('nombre', value)}
            className="text-3xl font-bold text-gray-900"
            required
          />
          <div className="flex items-center mt-2 space-x-4">
            <EditableField
              label="Estado"
              value={project.estado}
              type="select"
              options={ESTADOS_PROYECTO.map(estado => ({ 
                value: estado, 
                label: estado.charAt(0).toUpperCase() + estado.slice(1).replace('_', ' ')
              }))}
              onSave={(value) => onUpdateProject('estado', value)}
              renderValue={(value) => (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${ESTADO_COLORS[value as keyof typeof ESTADO_COLORS] || 'bg-gray-100 text-gray-800'}`}>
                  {value}
                </span>
              )}
            />
            <EditableField
              label="Prioridad"
              value={project.prioridad}
              type="select"
              options={PRIORIDADES_PROYECTO.map(prioridad => ({ value: prioridad, label: prioridad }))}
              onSave={(value) => onUpdateProject('prioridad', value)}
            />
          </div>
        </div>
      </div>
      
      <button
        onClick={onDeleteProject}
        className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Eliminar Proyecto
      </button>
    </div>
  );
});

ProjectHeader.displayName = 'ProjectHeader';

export default ProjectHeader;
