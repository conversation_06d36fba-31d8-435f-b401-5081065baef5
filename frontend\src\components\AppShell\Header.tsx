import React, { useState } from 'react';
import { Search } from 'lucide-react';
import GlobalSearch from '../Search/GlobalSearch';

interface HeaderProps {
  toggleMobileMenu: () => void;
}

const Header: React.FC<HeaderProps> = ({ toggleMobileMenu }) => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const handleSearchToggle = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const handleSearchClose = () => {
    setIsSearchOpen(false);
  };

  // Handle keyboard shortcut for search (Ctrl/Cmd + K)
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setIsSearchOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <>
      <header className="bg-gray-800 text-white p-3 md:p-4 shadow-md flex-shrink-0">
        <div className="flex justify-between items-center max-w-full">
          <div className="flex items-center space-x-4">
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className="text-white p-2 rounded-md hover:bg-gray-700 focus:outline-none focus:bg-gray-700 transition-colors"
                aria-label="Open navigation menu"
              >
                {/* Hamburger Icon */}
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                </svg>
              </button>
            </div>
            <div className="text-lg md:text-xl font-semibold truncate">Aceleralia</div>
          </div>

          {/* Search and User Info */}
          <div className="flex items-center space-x-4">
            {/* Search Button */}
            <button
              onClick={handleSearchToggle}
              className="hidden md:flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-sm"
              title="Buscar (Ctrl+K)"
            >
              <Search className="h-4 w-4" />
              <span className="text-gray-300">Buscar...</span>
              <kbd className="hidden lg:inline-block px-2 py-1 text-xs bg-gray-600 rounded">
                ⌘K
              </kbd>
            </button>

            {/* Mobile Search Button */}
            <button
              onClick={handleSearchToggle}
              className="md:hidden p-2 text-white hover:bg-gray-700 rounded-md transition-colors"
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </button>

            <div className="text-sm md:text-base">User Info / Logout</div>
          </div>
        </div>
      </header>

      {/* Global Search Modal */}
      <GlobalSearch isOpen={isSearchOpen} onClose={handleSearchClose} />
    </>
  );
};

export default Header;