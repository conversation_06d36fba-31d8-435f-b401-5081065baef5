import React, { useState } from 'react';
import { Proyecto, ESTADOS_PROYECTO, ESTADO_COLORS } from '../../types/proyecto';
import { Calendar, User, TrendingUp, MoreVertical, Trash2 } from 'lucide-react';



interface ProjectKanbanProps {
  projects: Proyecto[];
  onProjectClick: (projectId: string) => void;
  onUpdateProject: (projectId: string, field: keyof Proyecto, value: string | number | boolean | null) => Promise<void>;
  onDeleteProject: (projectId: string, projectName: string) => Promise<void>;
  getProgressColor: (progress: number) => string;
}

interface KanbanColumn {
  id: string;
  title: string;
  projects: Proyecto[];
}

export const ProjectKanban: React.FC<ProjectKanbanProps> = ({
  projects,
  onProjectClick,
  onUpdateProject,
  onDeleteProject,
  getProgressColor
}) => {
  const [draggedProject, setDraggedProject] = useState<Proyecto | null>(null);

  // Organizar proyectos por estado
  const columns: KanbanColumn[] = ESTADOS_PROYECTO.map(estado => ({
    id: estado,
    title: estado.charAt(0).toUpperCase() + estado.slice(1).replace('_', ' '),
    projects: projects.filter(project => project.estado === estado)
  }));

  const handleDragStart = (e: React.DragEvent, project: Proyecto) => {
    setDraggedProject(project);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, newEstado: string) => {
    e.preventDefault();
    
    if (draggedProject && draggedProject.estado !== newEstado) {
      try {
        await onUpdateProject(draggedProject.id, 'estado', newEstado);
      } catch (error) {
        console.error('Error updating project status:', error);
      }
    }
    
    setDraggedProject(null);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const ProjectCard: React.FC<{ project: Proyecto }> = ({ project }) => {
    const [showMenu, setShowMenu] = useState(false);

    return (
      <div
        draggable
        onDragStart={(e) => handleDragStart(e, project)}
        className="bg-white rounded-lg border border-gray-200 p-4 mb-3 cursor-move hover:shadow-md transition-shadow"
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <h3 
            className="font-medium text-gray-900 cursor-pointer hover:text-blue-600"
            onClick={() => onProjectClick(project.id)}
          >
            {project.nombre}
          </h3>
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <MoreVertical className="h-4 w-4" />
            </button>
            {showMenu && (
              <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                <button
                  onClick={() => {
                    onProjectClick(project.id);
                    setShowMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Ver detalles
                </button>
                <button
                  onClick={() => {
                    onDeleteProject(project.id, project.nombre);
                    setShowMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3 inline mr-2" />
                  Eliminar
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Description */}
        {project.descripcion && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {project.descripcion}
          </p>
        )}

        {/* Progress */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-700">Progreso</span>
            <span className="text-xs text-gray-600">{project.progreso}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${getProgressColor(project.progreso)}`}
              style={{ width: `${project.progreso}%` }}
            />
          </div>
        </div>

        {/* Tasks count */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <span>
            {project.tareas_completadas || 0} / {project.total_tareas || 0} tareas
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${ESTADO_COLORS[project.estado as keyof typeof ESTADO_COLORS] || 'bg-gray-100 text-gray-800'}`}>
            {project.estado}
          </span>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          {/* Responsible */}
          <div className="flex items-center">
            {project.responsable_usuario ? (
              <div className="flex items-center">
                <User className="h-3 w-3 text-gray-400 mr-1" />
                <span className="text-xs text-gray-600">
                  {project.responsable_usuario.nombre}
                </span>
              </div>
            ) : (
              <span className="text-xs text-gray-400">Sin asignar</span>
            )}
          </div>

          {/* Due date */}
          {project.fecha_fin_estimada && (
            <div className="flex items-center">
              <Calendar className="h-3 w-3 text-gray-400 mr-1" />
              <span className="text-xs text-gray-600">
                {formatDate(project.fecha_fin_estimada)}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex space-x-6 overflow-x-auto pb-6">
      {columns.map((column) => (
        <div
          key={column.id}
          className="flex-shrink-0 w-80"
          onDragOver={handleDragOver}
          onDrop={(e) => handleDrop(e, column.id)}
        >
          {/* Column Header */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">{column.title}</h3>
              <span className="bg-gray-200 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
                {column.projects.length}
              </span>
            </div>
          </div>

          {/* Column Content */}
          <div className="space-y-3 min-h-[200px]">
            {column.projects.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">No hay proyectos</p>
              </div>
            ) : (
              column.projects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
