/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}", // Configure paths to all template files
  ],
  theme: {
    extend: {
      screens: {
        'xs': '480px',
        // Default breakpoints: sm: 640px, md: 768px, lg: 1024px, xl: 1280px, 2xl: 1536px
        'mobile': {'max': '767px'}, // Mobile-first approach
        'tablet': {'min': '768px', 'max': '1023px'}, // Tablet range
        'desktop': {'min': '1024px'}, // Desktop and up
      },
      height: {
        'screen-ios': 'calc(var(--vh, 1vh) * 100)', // iOS Safari viewport fix
      },
      minHeight: {
        'screen-ios': 'calc(var(--vh, 1vh) * 100)', // iOS Safari viewport fix
      },
    },
  },
  plugins: [],
}