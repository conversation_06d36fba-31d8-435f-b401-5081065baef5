from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List, Literal, Union, Annotated
from uuid import UUID
from datetime import datetime
import re
from fastapi import HTTPException

class ToolBase(BaseModel):
    """Base model for tool data."""
    tool_name: str
    tool_description: Optional[str] = None
    tool_params: Dict[str, Any] = Field(default_factory=dict) # For jsonb
    function_identifier: Optional[str] = None # Identifier for backend function/logic

class ToolCreate(ToolBase):
    """Model for creating a new tool."""
    pass

class ToolUpdate(BaseModel):
    """Model for updating tool data."""
    tool_name: Optional[str] = None
    tool_description: Optional[str] = None
    tool_params: Optional[Dict[str, Any]] = None
    function_identifier: Optional[str] = None

class ToolInDBBase(ToolBase):
    """Base model for tool data stored in the database."""
    id: UUID
    creado_en: Optional[datetime] = None # Allow null based on schema? Default is now()
    updated_at: Optional[datetime] = None # Allow null based on schema? Default is now()

    class Config:
        from_attributes = True # Pydantic V2 alias for orm_mode

class Tool(ToolInDBBase):
    """Model representing a tool as returned from the API."""
    pass

class ToolInDB(ToolInDBBase):
    """Model representing a tool stored in the database."""
    pass
# --- Models for SQL Execution Tool Endpoint ---

class SqlExecutionRequest(BaseModel):
    """Request body for the SQL execution endpoint."""
    sql: str = Field(..., description="The SQL command to execute.")

class SqlExecutionSuccessResponse(BaseModel):
    """Response body for successful SQL execution."""
    status: Optional[str] = Field(None, description="Status message, e.g., 'success' for non-SELECT queries.")
    result: Optional[list[dict[str, Any]]] = Field(None, description="Query results for SELECT statements (list of dicts).")
    rows_affected: Optional[int] = Field(None, description="Number of rows affected by INSERT/UPDATE/DELETE.")

class ErrorDetail(BaseModel):
    """Generic error detail model for API responses."""
    detail: str

# --- Models for Text Field Modification Tool Endpoint ---

class InstruccionCambio(BaseModel):
    """Base model for text modification instructions."""
    accion: Literal["reemplazar_fragmento", "anadir_texto", "eliminar_fragmento", "eliminar_seccion_delimitada"]

    # Fields for reemplazar_fragmento
    texto_a_buscar: Optional[str] = None
    nuevo_texto: Optional[str] = None
    reemplazar_todas_las_ocurrencias: Optional[bool] = False

    # Fields for anadir_texto
    posicion: Optional[Literal["inicio_campo", "final_campo", "despues_de_marcador", "antes_de_marcador"]] = None
    texto_a_anadir: Optional[str] = None
    texto_marcador_referencia: Optional[str] = None
    crear_marcador_si_no_existe: Optional[bool] = False

    # Fields for eliminar_fragmento
    texto_a_eliminar: Optional[str] = None
    eliminar_todas_las_ocurrencias: Optional[bool] = False

    # Fields for eliminar_seccion_delimitada
    marcador_inicio_seccion: Optional[str] = None
    marcador_fin_seccion: Optional[str] = None
    eliminar_solo_primera_seccion: Optional[bool] = True

class TextModificationRequest(BaseModel):
    """Request body for the text field modification endpoint."""
    tabla: str = Field(..., description="Nombre de la tabla en la base de datos")
    columna: str = Field(..., description="Nombre de la columna de texto a modificar")
    id_fila: str = Field(..., description="ID (UUID) del registro a modificar")
    instrucciones_de_cambio: List[InstruccionCambio] = Field(..., description="Lista de instrucciones de cambio a aplicar")

class TextModificationResponse(BaseModel):
    """Response body for text field modification operations."""
    status: Literal["success", "partial_success", "no_change_applied", "error"]
    message: str = Field(..., description="Mensaje descriptivo del resultado")
    cambios_realizados_en_bd: bool = Field(..., description="Indica si se realizaron cambios en la base de datos")
    instrucciones_procesadas: Optional[int] = Field(None, description="Número total de instrucciones procesadas")
    instrucciones_exitosas: Optional[int] = Field(None, description="Número de instrucciones aplicadas exitosamente")

# --- Utility Functions for New Database Tools ---

# Regex for validating SQL identifiers (table names, column names)
VALID_IDENT = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$')

def validate_identifier(ident: str) -> str:
    """
    Validates and returns a SQL identifier (table name, column name).
    Raises HTTPException if invalid.
    """
    if not VALID_IDENT.fullmatch(ident):
        raise HTTPException(status_code=400, detail=f"Invalid SQL identifier: {ident}")
    return ident

# --- Models for New Database Tools ---

class SelectRequest(BaseModel):
    """Request body for the SELECT query endpoint."""
    statement: str = Field(..., description="SELECT SQL statement with placeholders ($1, $2, etc.)")
    values: Union[List[Any], List[List[Any]]] = Field(default_factory=list, description="Parameter values for placeholders")

class SelectResponse(BaseModel):
    """Response body for successful SELECT operations."""
    status: str = Field(default="success", description="Operation status")
    result: List[Dict[str, Any]] = Field(..., description="Query results as list of dictionaries")

class InsertRequest(BaseModel):
    """Request body for INSERT operations."""
    operation: Literal["insert"] = "insert"
    table: str = Field(..., description="Target table name")
    records: List[Dict[str, Any]] = Field(..., description="List of records to insert")
    returning: Optional[List[str]] = Field(None, description="Columns to return after insert")

class UpdateRequest(BaseModel):
    """Request body for UPDATE operations."""
    operation: Literal["update"] = "update"
    table: str = Field(..., description="Target table name")
    set: Dict[str, Any] = Field(..., description="Column-value pairs to update")
    where: Dict[str, Any] = Field(..., description="WHERE conditions (required)")
    returning: Optional[List[str]] = Field(None, description="Columns to return after update")

class DeleteRequest(BaseModel):
    """Request body for DELETE operations."""
    operation: Literal["delete"] = "delete"
    table: str = Field(..., description="Target table name")
    where: Dict[str, Any] = Field(..., description="WHERE conditions (required)")
    returning: Optional[List[str]] = Field(None, description="Columns to return after delete")

# Discriminated union for database operations
DbOpRequest = Annotated[Union[InsertRequest, UpdateRequest, DeleteRequest], Field(discriminator='operation')]

class DbOpResponse(BaseModel):
    """Response body for successful database operations (INSERT/UPDATE/DELETE)."""
    status: str = Field(default="success", description="Operation status")
    rows_affected: int = Field(..., description="Number of rows affected by the operation")
    result: Optional[List[Dict[str, Any]]] = Field(None, description="Returned data if RETURNING clause was used")