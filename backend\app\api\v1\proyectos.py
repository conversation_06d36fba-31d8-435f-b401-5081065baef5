import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
from uuid import UUID

from app.models.proyecto import (
    ProyectoCreate, ProyectoUpdate, ProyectoResponse, ProyectoListResponse,
    ProyectoSummary
)
from app.core.security import get_current_user_id
from app.services import proyecto_service

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=ProyectoResponse, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=ProyectoResponse, status_code=status.HTTP_201_CREATED)
async def create_proyecto(
    proyecto_in: ProyectoCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new proyecto.
    """
    try:
        logger.info(f"Creating proyecto: {proyecto_in.nombre} by user {current_user_id}")
        proyecto = await proyecto_service.create_proyecto(proyecto_in, current_user_id)
        return ProyectoResponse(**proyecto.model_dump())
    except Exception as e:
        logger.error(f"Error creating proyecto: {str(e)}")
        raise

@router.get("/", response_model=ProyectoListResponse)
@router.get("", response_model=ProyectoListResponse)
async def get_proyectos(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    estado: Optional[str] = Query(None, description="Filter by estado"),
    prioridad: Optional[str] = Query(None, description="Filter by prioridad"),
    responsable_id: Optional[UUID] = Query(None, description="Filter by responsable"),
    search: Optional[str] = Query(None, description="Search in nombre and descripcion"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of proyectos with optional filtering.
    """
    try:
        logger.info(f"Fetching proyectos for user {current_user_id}")
        return await proyecto_service.get_proyectos(
            skip=skip,
            limit=limit,
            estado=estado,
            prioridad=prioridad,
            responsable_id=responsable_id,
            search=search
        )
    except Exception as e:
        logger.error(f"Error fetching proyectos: {str(e)}")
        raise

@router.get("/dashboard", response_model=List[ProyectoSummary])
async def get_proyectos_dashboard(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get active proyectos for dashboard display.
    """
    try:
        logger.info(f"Fetching dashboard proyectos for user {current_user_id}")
        return await proyecto_service.get_proyectos_activos_dashboard()
    except Exception as e:
        logger.error(f"Error fetching dashboard proyectos: {str(e)}")
        raise

@router.get("/{proyecto_id}", response_model=ProyectoResponse)
async def get_proyecto(
    proyecto_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a specific proyecto by ID.
    """
    try:
        logger.info(f"Fetching proyecto {proyecto_id} for user {current_user_id}")
        proyecto = await proyecto_service.get_proyecto_by_id(proyecto_id)
        return ProyectoResponse(**proyecto.model_dump())
    except Exception as e:
        logger.error(f"Error fetching proyecto {proyecto_id}: {str(e)}")
        raise

@router.put("/{proyecto_id}", response_model=ProyectoResponse)
async def update_proyecto(
    proyecto_id: UUID,
    proyecto_update: ProyectoUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update a proyecto.
    """
    try:
        logger.info(f"Updating proyecto {proyecto_id} by user {current_user_id}")
        proyecto = await proyecto_service.update_proyecto(proyecto_id, proyecto_update)
        return ProyectoResponse(**proyecto.model_dump())
    except Exception as e:
        logger.error(f"Error updating proyecto {proyecto_id}: {str(e)}")
        raise

@router.delete("/{proyecto_id}")
async def delete_proyecto(
    proyecto_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a proyecto.
    """
    try:
        logger.info(f"Deleting proyecto {proyecto_id} by user {current_user_id}")
        return await proyecto_service.delete_proyecto(proyecto_id)
    except Exception as e:
        logger.error(f"Error deleting proyecto {proyecto_id}: {str(e)}")
        raise

# Kanban specific endpoints
@router.get("/{proyecto_id}/kanban")
async def get_proyecto_kanban(
    proyecto_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get proyecto data organized for Kanban view.
    This endpoint returns the proyecto with its associated processes and tasks
    organized by status for Kanban display.
    """
    try:
        logger.info(f"Fetching proyecto {proyecto_id} kanban view for user {current_user_id}")
        
        # Get the proyecto with full details
        proyecto = await proyecto_service.get_proyecto_by_id(proyecto_id)
        
        # For now, return the proyecto data
        # In the future, this could be enhanced to return specific Kanban organization
        return {
            "proyecto": proyecto.model_dump(),
            "message": "Kanban view data - to be enhanced with specific Kanban organization"
        }
    except Exception as e:
        logger.error(f"Error fetching proyecto {proyecto_id} kanban: {str(e)}")
        raise

# Estados management
@router.get("/estados/available")
async def get_available_estados(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available estados for proyectos.
    """
    return {
        "estados": [
            "Planificado",
            "En Diseño", 
            "En Ejecución",
            "En Revisión",
            "Completado",
            "Pausado",
            "Cancelado"
        ]
    }

@router.get("/prioridades/available")
async def get_available_prioridades(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available prioridades for proyectos.
    """
    return {
        "prioridades": [
            "Baja",
            "Media",
            "Alta",
            "Urgente"
        ]
    }
