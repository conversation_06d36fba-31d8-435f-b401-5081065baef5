import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import {
  NavigationConfig,
  NavigationItem,
  getFilteredNavigation,
  findNavigationItem
} from '../config/navigation';

interface UseNavigationReturn {
  navigation: NavigationConfig;
  currentItem: NavigationItem | undefined;
  isCurrentPath: (path: string) => boolean;
  getAllItems: () => NavigationItem[];
}

/**
 * Hook for managing application navigation
 * Provides filtered navigation based on user permissions and current route context
 */
export const useNavigation = (): UseNavigationReturn => {
  const location = useLocation();
  
  // Get filtered navigation (future: based on user roles)
  const navigation = useMemo(() => {
    // For now, return all navigation
    // Future: Pass user roles to getFilteredNavigation
    return getFilteredNavigation();
  }, []);
  
  // Get current navigation item based on current path
  const currentItem = useMemo(() => {
    return findNavigationItem(location.pathname);
  }, [location.pathname]);
  
  // Check if a path is the current active path
  const isCurrentPath = (path: string): boolean => {
    return location.pathname === path;
  };
  
  // Get all navigation items flattened
  const getAllItems = (): NavigationItem[] => {
    const allItems: NavigationItem[] = [...navigation.main];
    navigation.groups.forEach(group => {
      allItems.push(...group.items);
    });
    return allItems;
  };
  
  return {
    navigation,
    currentItem,
    isCurrentPath,
    getAllItems
  };
};

// Hook specifically for mobile navigation in chat context
export const useMobileNavigation = () => {
  const { navigation, isCurrentPath } = useNavigation();
  
  // Return navigation optimized for mobile chat panel
  return {
    mainSections: navigation.main,
    groupedSections: navigation.groups,
    isCurrentPath,
    // Helper to get navigation with current state
    getNavigationWithState: () => {
      return {
        main: navigation.main.map(item => ({
          ...item,
          isActive: isCurrentPath(item.path)
        })),
        groups: navigation.groups.map(group => ({
          ...group,
          items: group.items.map(item => ({
            ...item,
            isActive: isCurrentPath(item.path)
          }))
        }))
      };
    }
  };
};

export default useNavigation;
