# from sqlalchemy.orm import Session # Removed unused import
from fastapi import UploadFile, HTTPException, status
from uuid import uuid4, UUID
import datetime
import traceback
import aiohttp # For making async HTTP requests to n8n
from typing import List, Dict, Any, Optional

from app.models.reunion import Reunion<PERSON><PERSON>, SpeakerAssignmentCreate, Reunion, ReunionUploaderInfo
from app.models.crm_empresa import Empresa
from app.models.crm_persona import Persona
from app.models.crm_lead_empresa import LeadEmpresa
from app.models.crm_lead_contacto import LeadContacto
from app.models.user import User as UserModel # Alias to avoid conflict

from app.core.database import get_supabase_client
from app.core.config import settings

# Define a constant for the Supabase storage bucket name
REUNIONES_BUCKET = "almacenamiento" # Corrected bucket name

def format_speaker_tag(tag: str) -> str:
    """
    Helper function to format speaker tags consistently.
    Converts speaker_tag format (e.g., "speaker_0") to display format (e.g., "Speaker 0").

    Args:
        tag: The speaker tag to format

    Returns:
        Formatted speaker tag for display
    """
    if tag.startswith('speaker_'):
        speaker_number = tag.replace('speaker_', '')
        return f"Speaker {speaker_number}"
    return tag

async def create_reunion_record_from_file_reference( # Renamed function
    reunion_in: ReunionCreate, # Input model now contains file URLs/paths
    user_id: UUID
) -> dict:
    """
    Create a new reunion record using a pre-uploaded file's reference (path/URL)
    and trigger the first n8n workflow for transcription.
    """
    supabase = await get_supabase_client()
    print(f"[ReunionService] create_reunion_record_from_file_reference started for user {user_id}, file path: {reunion_in.file_storage_path}")

    reunion_id = uuid4() # Generate new ID for the reunion record

    # 1. Prepare reunion record for database insertion
    #    File is already uploaded by frontend; paths/URLs are in reunion_in.
    reunion_db_data = reunion_in.model_dump(
        exclude_none=True,
        exclude={
            "empresas_asociadas_ids",
            "personas_asociadas_ids",
            "leads_empresas_asociadas_ids",
            "lead_contactos_asociados_ids",
            "url_grabacion_publica", # Will be taken directly
            "file_storage_path"      # Will be used for url_grabacion_original
        }
    )
    reunion_db_data['id'] = str(reunion_id)
    reunion_db_data['user_id'] = str(user_id)
    reunion_db_data['url_grabacion_original'] = reunion_in.file_storage_path
    reunion_db_data['url_grabacion_publica'] = str(reunion_in.url_grabacion_publica) # Ensure it's a string for DB
    reunion_db_data['estado_procesamiento'] = 'pending_transcripcion'
    reunion_db_data['created_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
    reunion_db_data['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
    if reunion_in.fecha_reunion:
         reunion_db_data['fecha_reunion'] = reunion_in.fecha_reunion.isoformat()

    print(f"[ReunionService] Preparing to insert reunion record into DB: {reunion_db_data}")
    try:
        response = supabase.table('reuniones').insert(reunion_db_data).execute()
        print(f"[ReunionService] DB insert response: {response}")
        if not response.data or len(response.data) == 0:
            # No file to delete from storage here as it was a pre-existing reference.
            # If DB insert fails, the frontend might need to be notified to allow user to retry or cleanup storage if desired.
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create reunion record in DB.")

        created_reunion = response.data[0]

        # 2. Create association records (if any IDs are provided) - (Was step 3)
        # This needs to be done carefully, handling potential errors for each association type.
        # Example for empresas_asociadas:
        if reunion_in.empresas_asociadas_ids:
            empresa_associations = [
                {"reunion_id": str(reunion_id), "empresa_id": str(emp_id)}
                for emp_id in reunion_in.empresas_asociadas_ids
            ]
            supabase.table('reunion_empresas_asociadas').insert(empresa_associations).execute()

        if reunion_in.personas_asociadas_ids:
            persona_associations = [
                {"reunion_id": str(reunion_id), "persona_id": str(p_id)}
                for p_id in reunion_in.personas_asociadas_ids
            ]
            supabase.table('reunion_personas_asociadas').insert(persona_associations).execute()

        if reunion_in.leads_empresas_asociadas_ids:
            lead_empresa_associations = [
                {"reunion_id": str(reunion_id), "lead_empresa_id": str(le_id)}
                for le_id in reunion_in.leads_empresas_asociadas_ids
            ]
            supabase.table('reunion_leads_empresas_asociadas').insert(lead_empresa_associations).execute()

        if reunion_in.lead_contactos_asociados_ids:
            lead_contacto_associations = [
                {"reunion_id": str(reunion_id), "lead_contacto_id": str(lc_id)}
                for lc_id in reunion_in.lead_contactos_asociados_ids
            ]
            supabase.table('reunion_lead_contactos_asociados').insert(lead_contacto_associations).execute()

    except Exception as e:
        # If DB operations fail, the pre-uploaded file on Supabase Storage remains.
        # Manual cleanup or a separate cleanup mechanism might be needed if the record creation is critical.
        print(f"Error creating reunion DB record or associations: {e}")
        print(f"Stack trace: {traceback.format_exc()}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"DB error: {str(e)}")

    # 3. Trigger n8n workflow 1 (transcription) - (Was step 4)
    n8n_webhook_url = settings.N8N_WEBHOOK_URL_REUNION_TRANSCRIPCION
    if not n8n_webhook_url:
        print("N8N_WEBHOOK_URL_REUNION_TRANSCRIPCION not configured. Skipping n8n trigger.")
    else:
        payload = {
            "reunion_id": str(reunion_id),
            "file_url": str(reunion_in.url_grabacion_publica), # Use the public URL from input
            # "original_filename": reunion_in.file_storage_path.split('/')[-1], # Extract filename if needed by n8n
            # "content_type": "audio/mp4", # Or get from frontend if available, or make it generic
        }
        print(f"[ReunionService] Triggering n8n transcription workflow with payload: {payload}")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(n8n_webhook_url, json=payload) as resp:
                    responseText = await resp.text()
                    if resp.status not in [200, 201, 202]:
                        print(f"Error triggering n8n transcription workflow. Status: {resp.status}, Response: {responseText}")
                    else:
                        print(f"Successfully triggered n8n transcription workflow for reunion {reunion_id}. Response: {responseText}")
        except Exception as e:
            print(f"Exception triggering n8n: {e}")
            print(f"Stack trace: {traceback.format_exc()}")

    print(f"[ReunionService] create_reunion_record_from_file_reference finished for reunion {reunion_id}.")
    return created_reunion

async def assign_speakers_and_trigger_analysis(
    reunion_id: UUID,
    speaker_assignments_in: List[SpeakerAssignmentCreate],
    user_id: UUID # For audit/context if needed
) -> Dict[str, Any]:
    """
    Save speaker assignments, generate final transcript, update reunion status,
    and trigger the second n8n workflow for AI analysis.
    """
    supabase = await get_supabase_client()

    # 1. Fetch the reunion record, especially the raw transcription
    reunion_response = supabase.table('reuniones').select('id, transcripcion_raw, observaciones_iniciales, entrevista, video').eq('id', str(reunion_id)).maybe_single().execute()
    if not reunion_response.data:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found.")

    reunion_data = reunion_response.data
    raw_transcript = reunion_data.get('transcripcion_raw')
    if not raw_transcript:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Reunion has no raw transcription to assign speakers to.")

    # 2. Save speaker assignments and build enhanced speaker map
    assignments_to_create = []
    speaker_map = {} # To build the map for final transcript generation

    for assignment_in in speaker_assignments_in:
        assignments_to_create.append({
            "id": str(uuid4()),
            "reunion_id": str(reunion_id),
            "speaker_tag": assignment_in.speaker_tag,
            "asignado_a_tipo": assignment_in.asignado_a_tipo,
            "asignado_a_id": str(assignment_in.asignado_a_id),
            "nombre_asignado": assignment_in.nombre_asignado,
            "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
            "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        })

        # Build enhanced speaker name with full name + ID
        enhanced_name = await _build_enhanced_speaker_name(
            assignment_in.asignado_a_tipo,
            assignment_in.asignado_a_id,
            assignment_in.nombre_asignado
        )
        speaker_map[assignment_in.speaker_tag] = enhanced_name

    if assignments_to_create:
        supabase.table('reunion_speaker_asignaciones').insert(assignments_to_create).execute()

    # 3. Generate final transcript with intelligent speaker replacement
    # Handle both HTML and plain text formats
    final_transcript = raw_transcript

    print(f"[ReunionService] Starting speaker replacement for reunion {reunion_id}")
    print(f"[ReunionService] Speaker map: {speaker_map}")
    print(f"[ReunionService] Raw transcript preview: {raw_transcript[:500]}...")

    # Check if the transcript is in HTML format
    if '<div class="container-info-speaker">' in raw_transcript:
        # HTML format - replace speaker names within the text_speaker elements
        import re
        for tag, name in speaker_map.items():
            # Use helper function to format speaker tag consistently
            display_tag = format_speaker_tag(tag)

            print(f"[ReunionService] Replacing '{tag}' -> '{display_tag}' with '{name}'")

            # Pattern to match speaker tag at the beginning of text_speaker content
            # Matches: "Speaker 0: content" -> "Name: content"
            pattern = rf'(<span[^>]*class="text_speaker"[^>]*>)\s*{re.escape(display_tag)}\s*:\s*'
            replacement = rf'\1{name}: '
            matches_before = len(re.findall(pattern, final_transcript))
            final_transcript = re.sub(pattern, replacement, final_transcript)
            matches_after = len(re.findall(pattern, final_transcript))
            print(f"[ReunionService] Pattern '{pattern}' matched {matches_before} times, replaced {matches_before - matches_after}")

            # Also handle cases where speaker tag appears without colon
            pattern_no_colon = rf'(<span[^>]*class="text_speaker"[^>]*>)\s*{re.escape(display_tag)}\s+'
            replacement_no_colon = rf'\1{name} '
            matches_before_no_colon = len(re.findall(pattern_no_colon, final_transcript))
            final_transcript = re.sub(pattern_no_colon, replacement_no_colon, final_transcript)
            matches_after_no_colon = len(re.findall(pattern_no_colon, final_transcript))
            print(f"[ReunionService] Pattern no colon '{pattern_no_colon}' matched {matches_before_no_colon} times, replaced {matches_before_no_colon - matches_after_no_colon}")
    else:
        # Plain text format - simple replacement
        for tag, name in speaker_map.items():
            # Use helper function to format speaker tag consistently
            display_tag = format_speaker_tag(tag)

            # Replace "Speaker 0:" with "Name:"
            final_transcript = final_transcript.replace(f"{display_tag}:", f"{name}:")
            # Also replace standalone speaker tags
            final_transcript = final_transcript.replace(display_tag, name)

    print(f"[ReunionService] Speaker replacement completed. Final transcript preview: {final_transcript[:500]}...")

    # 4. Update reunion record with final transcript and new status
    update_payload = {
        "transcripcion_final": final_transcript,
        "estado_procesamiento": "procesando_ia", # New status
        "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }
    updated_reunion_response = supabase.table('reuniones').update(update_payload).eq('id', str(reunion_id)).execute()

    if not updated_reunion_response.data:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update reunion with final transcript.")

    # 5. Trigger n8n workflow 2 (AI Analysis)
    n8n_webhook_url_analysis = settings.N8N_WEBHOOK_URL_REUNION_ANALYSIS
    if not n8n_webhook_url_analysis:
        print(f"N8N_WEBHOOK_URL_REUNION_ANALYSIS not configured for reunion {reunion_id}. Skipping AI analysis trigger.")
        # Potentially update status to 'pending_ai_trigger_config' or similar
    else:
        n8n_payload = {
            "reunion_id": str(reunion_id),
            "transcripcion_final": final_transcript,
            "observaciones_iniciales": reunion_data.get('observaciones_iniciales'),
            "entrevista": reunion_data.get('entrevista', False),  # Include entrevista field
            "video": reunion_data.get('video', False),  # Include video field
            # Add any other relevant data for AI analysis
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(n8n_webhook_url_analysis, json=n8n_payload) as resp:
                    if resp.status not in [200, 201, 202]:
                        print(f"Error triggering n8n AI analysis workflow for reunion {reunion_id}. Status: {resp.status}, Response: {await resp.text()}")
                        # Optionally update reunion status to 'error_triggering_ai'
                    else:
                        print(f"Successfully triggered n8n AI analysis workflow for reunion {reunion_id}.")
        except Exception as e:
            print(f"Exception triggering n8n AI analysis: {e}")
            print(f"Stack trace: {traceback.format_exc()}")
            # Log error

    return updated_reunion_response.data[0] # Return the updated reunion data

async def get_reunion_by_id(reunion_id: UUID, user_id: UUID) -> Optional[Reunion]:
    """
    Get a specific reunion by its ID, including all associated CRM entities and uploader info.
    Ensures the reunion belongs to the user.
    """
    supabase = await get_supabase_client()

    # 1. Fetch the main reunion record
    reunion_resp = supabase.table('reuniones').select("*").eq('id', str(reunion_id)).eq('user_id', str(user_id)).maybe_single().execute()

    if not reunion_resp.data:
        return None

    reunion_dict = reunion_resp.data

    # 2. Fetch uploader information
    uploader_info_data = None
    if reunion_dict.get('user_id'):
        user_resp = supabase.table('usuarios').select("id, nombre, email").eq('id', str(reunion_dict['user_id'])).maybe_single().execute()
        if user_resp.data:
            # Ensure all fields required by ReunionUploaderInfo are present or handled if optional
            uploader_data_for_model = {
                "id": user_resp.data.get("id"),
                "nombre": user_resp.data.get("nombre"),
                "email": user_resp.data.get("email")
            }
            if uploader_data_for_model["email"]: # Email is mandatory for ReunionUploaderInfo
                 uploader_info_data = ReunionUploaderInfo(**uploader_data_for_model)
            else:
                print(f"[ReunionService] Warning: Uploader {user_resp.data.get('id')} missing email, cannot create ReunionUploaderInfo.")


    reunion_dict['uploader_info'] = uploader_info_data

    # 3. Fetch associated entities
    # Empresas
    empresa_ids_resp = supabase.table('reunion_empresas_asociadas').select('empresa_id').eq('reunion_id', str(reunion_id)).execute()
    empresas_asociadas = []
    if empresa_ids_resp.data:
        empresa_ids = [item['empresa_id'] for item in empresa_ids_resp.data]
        if empresa_ids:
            empresas_resp = supabase.table('empresas').select('*').in_('id', empresa_ids).execute()
            if empresas_resp.data:
                empresas_asociadas = [Empresa(**data) for data in empresas_resp.data]
    reunion_dict['empresas_asociadas'] = empresas_asociadas

    # Personas
    persona_ids_resp = supabase.table('reunion_personas_asociadas').select('persona_id').eq('reunion_id', str(reunion_id)).execute()
    personas_asociadas = []
    if persona_ids_resp.data:
        persona_ids = [item['persona_id'] for item in persona_ids_resp.data]
        if persona_ids:
            personas_resp = supabase.table('personas').select('*, empresa:empresas(id, nombre)').in_('id', persona_ids).execute() # Fetch nested empresa basic info
            if personas_resp.data:
                personas_asociadas = [Persona(**data) for data in personas_resp.data]
    reunion_dict['personas_asociadas'] = personas_asociadas

    # Leads Empresas
    lead_empresa_ids_resp = supabase.table('reunion_leads_empresas_asociadas').select('lead_empresa_id').eq('reunion_id', str(reunion_id)).execute()
    leads_empresas_asociadas = []
    if lead_empresa_ids_resp.data:
        lead_empresa_ids = [item['lead_empresa_id'] for item in lead_empresa_ids_resp.data]
        if lead_empresa_ids:
            leads_empresas_resp = supabase.table('leads_empresas').select('*').in_('id', lead_empresa_ids).execute()
            if leads_empresas_resp.data:
                leads_empresas_asociadas = [LeadEmpresa(**data) for data in leads_empresas_resp.data]
    reunion_dict['leads_empresas_asociadas'] = leads_empresas_asociadas

    # Lead Contactos
    lead_contacto_ids_resp = supabase.table('reunion_lead_contactos_asociados').select('lead_contacto_id').eq('reunion_id', str(reunion_id)).execute()
    lead_contactos_asociados = []
    if lead_contacto_ids_resp.data:
        lead_contacto_ids = [item['lead_contacto_id'] for item in lead_contacto_ids_resp.data]
        if lead_contacto_ids:
            # For LeadContacto, fetch nested lead_empresa basic info
            lead_contactos_resp = supabase.table('lead_contactos').select('*, lead_empresa:leads_empresas(id, nombre)').in_('id', lead_contacto_ids).execute()
            if lead_contactos_resp.data:
                lead_contactos_asociados = [LeadContacto(**data) for data in lead_contactos_resp.data]
    reunion_dict['lead_contactos_asociados'] = lead_contactos_asociados

    # 4. Parse the fully populated dict into the Reunion Pydantic model
    try:
        # Ensure all required fields for Reunion model are present in reunion_dict before parsing
        # The model itself will validate, but good to be mindful
        # Default empty lists for associations are handled by the Reunion model if not populated above
        return Reunion(**reunion_dict)
    except Exception as e:
        print(f"Error parsing reunion_dict into Reunion model: {e}, dict: {reunion_dict}")
        # This might indicate a mismatch between DB data and Pydantic model definitions
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error processing reunion data.")


async def get_all_reuniones_for_user(user_id: UUID, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]: # Return type could be List[ReunionSummary] or similar if needed
    """
    Get all reunions for a specific user with pagination.
    """
    supabase = await get_supabase_client()
    # Order by creation date or meeting date
    response = supabase.table('reuniones').select("*").eq('user_id', str(user_id)).order('created_at', desc=True).range(skip, skip + limit - 1).execute()

    return response.data if response.data else []

async def delete_reunion(reunion_id: UUID, user_id: UUID) -> bool:
    """
    Delete a reunion, its associated records, and its recording from storage.
    Ensures the user owns the reunion before deleting.
    """
    supabase = await get_supabase_client()

    # 1. Fetch the reunion to verify ownership and get file path
    reunion_to_delete_response = supabase.table('reuniones').select("id, user_id, url_grabacion_original").eq('id', str(reunion_id)).maybe_single().execute()

    if not reunion_to_delete_response.data:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found.")

    reunion_data = reunion_to_delete_response.data
    if str(reunion_data['user_id']) != str(user_id):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="User not authorized to delete this reunion.")

    file_path_on_storage = reunion_data.get('url_grabacion_original')

    # 2. Delete associated records (order might matter depending on FK constraints, or use CASCADE)
    # Assuming cascade delete is NOT set up, so manual deletion is needed.
    # If CASCADE is set, these can be skipped, but explicit is safer.
    try:
        print(f"[ReunionService] Deleting associations for reunion {reunion_id}")
        supabase.table('reunion_speaker_asignaciones').delete().eq('reunion_id', str(reunion_id)).execute()
        supabase.table('reunion_empresas_asociadas').delete().eq('reunion_id', str(reunion_id)).execute()
        supabase.table('reunion_personas_asociadas').delete().eq('reunion_id', str(reunion_id)).execute()
        supabase.table('reunion_leads_empresas_asociadas').delete().eq('reunion_id', str(reunion_id)).execute()
        supabase.table('reunion_lead_contactos_asociados').delete().eq('reunion_id', str(reunion_id)).execute()

        # 3. Delete the main reunion record
        print(f"[ReunionService] Deleting reunion record {reunion_id}")
        delete_main_response = supabase.table('reuniones').delete().eq('id', str(reunion_id)).execute()
        if not delete_main_response.data and delete_main_response.error: # Check if deletion actually happened or if there was an error
             # This check might need refinement based on actual supabase-py behavior for delete errors
            print(f"[ReunionService] Error deleting reunion DB record: {delete_main_response.error}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete reunion record from database.")


        # 4. Delete the file from Supabase Storage
        if file_path_on_storage:
            print(f"[ReunionService] Deleting file from storage: {file_path_on_storage}")
            storage_response = supabase.storage.from_(REUNIONES_BUCKET).remove([file_path_on_storage])
            # Check storage_response for errors if necessary, though remove often doesn't error if file not found.
            if storage_response and hasattr(storage_response, 'error') and storage_response.error:
                 print(f"[ReunionService] Error deleting file from storage, but DB record deleted. Path: {file_path_on_storage}, Error: {storage_response.error}")
                 # Decide if this should be a critical failure or just a warning.
                 # For now, we'll consider the main DB deletion a success if it reached here.

        print(f"[ReunionService] Reunion {reunion_id} and associated data/file deleted successfully.")
        return True

    except Exception as e:
        print(f"Error during deletion process for reunion {reunion_id}: {e}")
        # This is a general catch-all. Specific errors from Supabase might be Pydantic models or specific exceptions.
        # Re-raise as HTTPException or handle accordingly.
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred during deletion: {str(e)}")


async def _build_enhanced_speaker_name(
    asignado_a_tipo: str,
    asignado_a_id: UUID,
    fallback_name: str
) -> str:
    """
    Build enhanced speaker name with full name (nombre + apellidos) and ID.
    Format: "Nombre Apellidos (ID)"

    Args:
        asignado_a_tipo: Type of entity ('persona', 'leadContacto', 'usuario')
        asignado_a_id: ID of the assigned entity
        fallback_name: Fallback name if entity data cannot be retrieved

    Returns:
        Enhanced name string with format "Nombre Apellidos (ID)"
    """
    supabase = await get_supabase_client()

    try:
        if asignado_a_tipo == 'persona':
            # Fetch persona data
            response = supabase.table('personas').select('nombre, apellidos').eq('id', str(asignado_a_id)).maybe_single().execute()
            if response.data:
                nombre = response.data.get('nombre', '')
                apellidos = response.data.get('apellidos', '')
                full_name = f"{nombre} {apellidos}".strip()
                return f"{full_name} ({asignado_a_id})"

        elif asignado_a_tipo == 'leadContacto':
            # Fetch lead contacto data
            response = supabase.table('lead_contactos').select('nombre, apellidos').eq('id', str(asignado_a_id)).maybe_single().execute()
            if response.data:
                nombre = response.data.get('nombre', '')
                apellidos = response.data.get('apellidos', '')
                full_name = f"{nombre} {apellidos}".strip()
                return f"{full_name} ({asignado_a_id})"

        elif asignado_a_tipo == 'usuario':
            # Fetch usuario data
            response = supabase.table('usuarios').select('nombre, apellidos').eq('id', str(asignado_a_id)).maybe_single().execute()
            if response.data:
                nombre = response.data.get('nombre', '')
                apellidos = response.data.get('apellidos', '')
                full_name = f"{nombre} {apellidos}".strip()
                return f"{full_name} ({asignado_a_id})"

        # If we couldn't fetch data or unknown type, use fallback
        print(f"[ReunionService] Could not fetch enhanced name for {asignado_a_tipo} {asignado_a_id}, using fallback: {fallback_name}")
        return f"{fallback_name} ({asignado_a_id})"

    except Exception as e:
        print(f"[ReunionService] Error building enhanced speaker name for {asignado_a_tipo} {asignado_a_id}: {e}")
        return f"{fallback_name} ({asignado_a_id})"


async def add_association_to_reunion(
    reunion_id: UUID,
    entity_id: UUID,
    entity_type: str,
    user_id: UUID
) -> dict:
    """
    Add a new association (persona or leadContacto) to an existing reunion.
    This is used when creating new entities in Step 2 of the meeting processing.

    Args:
        reunion_id: ID of the reunion
        entity_id: ID of the entity to associate
        entity_type: Type of entity ('persona' or 'leadContacto')
        user_id: ID of the user making the request (for authorization)

    Returns:
        Success message
    """
    # Validate entity_type parameter
    valid_entity_types = ['persona', 'leadContacto']
    if entity_type not in valid_entity_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid entity_type: {entity_type}. Must be one of: {valid_entity_types}"
        )

    supabase = await get_supabase_client()

    try:
        # 1. Verify the reunion exists and belongs to the user
        reunion_response = supabase.table('reuniones').select('id, user_id').eq('id', str(reunion_id)).eq('user_id', str(user_id)).maybe_single().execute()
        if not reunion_response.data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found or access denied.")

        # 2. Verify the entity exists
        if entity_type == 'persona':
            entity_response = supabase.table('personas').select('id').eq('id', str(entity_id)).maybe_single().execute()
            if not entity_response.data:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Persona not found.")

            # 3. Check if association already exists
            existing_association = supabase.table('reunion_personas_asociadas').select('reunion_id').eq('reunion_id', str(reunion_id)).eq('persona_id', str(entity_id)).maybe_single().execute()
            if existing_association.data:
                return {"message": "Association already exists"}

            # 4. Create the association
            association_data = {
                "reunion_id": str(reunion_id),
                "persona_id": str(entity_id),
                "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
            supabase.table('reunion_personas_asociadas').insert(association_data).execute()

        elif entity_type == 'leadContacto':
            entity_response = supabase.table('lead_contactos').select('id').eq('id', str(entity_id)).maybe_single().execute()
            if not entity_response.data:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Lead Contacto not found.")

            # 3. Check if association already exists
            existing_association = supabase.table('reunion_lead_contactos_asociados').select('reunion_id').eq('reunion_id', str(reunion_id)).eq('lead_contacto_id', str(entity_id)).maybe_single().execute()
            if existing_association.data:
                return {"message": "Association already exists"}

            # 4. Create the association
            association_data = {
                "reunion_id": str(reunion_id),
                "lead_contacto_id": str(entity_id),
                "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
            supabase.table('reunion_lead_contactos_asociados').insert(association_data).execute()

        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Unsupported entity type: {entity_type}")

        print(f"[ReunionService] Successfully added {entity_type} {entity_id} association to reunion {reunion_id}")
        return {"message": f"Successfully added {entity_type} association to reunion"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ReunionService] Error adding association to reunion {reunion_id}: {e}")
        print(f"Stack trace: {traceback.format_exc()}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to add association: {str(e)}")

async def update_reunion_entrevista(
    reunion_id: UUID,
    entrevista: bool,
    user_id: UUID
) -> dict:
    """
    Update the 'entrevista' field of a reunion.
    This is used in Step 2 to mark if the meeting is an interview.

    Args:
        reunion_id: ID of the reunion
        entrevista: Boolean value indicating if it's an interview
        user_id: ID of the user making the request (for authorization)

    Returns:
        Updated reunion data
    """
    supabase = await get_supabase_client()

    try:
        # 1. Verify the reunion exists and belongs to the user, then update
        update_response = supabase.table('reuniones').update({
            'entrevista': entrevista
        }).eq('id', str(reunion_id)).eq('user_id', str(user_id)).execute()

        if not update_response.data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found or access denied.")

        print(f"[ReunionService] Successfully updated entrevista field to {entrevista} for reunion {reunion_id}")

        # 2. Return the updated reunion data
        updated_reunion = await get_reunion_by_id(reunion_id, user_id)
        if not updated_reunion:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Failed to fetch updated reunion data.")

        return updated_reunion.model_dump()

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ReunionService] Error updating entrevista field for reunion {reunion_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to update entrevista field: {str(e)}")

async def update_reunion_video(
    reunion_id: UUID,
    video: bool,
    user_id: UUID
) -> dict:
    """
    Update the 'video' field of a reunion.
    This is used in Step 2 to mark if the meeting video should be included in processing.

    Args:
        reunion_id: ID of the reunion
        video: Boolean value indicating if video should be included
        user_id: ID of the user making the request (for authorization)

    Returns:
        Updated reunion data
    """
    supabase = await get_supabase_client()

    try:
        # 1. Verify the reunion exists and belongs to the user, then update
        update_response = supabase.table('reuniones').update({
            'video': video
        }).eq('id', str(reunion_id)).eq('user_id', str(user_id)).execute()

        if not update_response.data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reunion not found or access denied.")

        print(f"[ReunionService] Successfully updated video field to {video} for reunion {reunion_id}")

        # 2. Return the updated reunion data
        updated_reunion = await get_reunion_by_id(reunion_id, user_id)
        if not updated_reunion:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Failed to fetch updated reunion data.")

        return updated_reunion.model_dump()

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ReunionService] Error updating video field for reunion {reunion_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to update video field: {str(e)}")
