import React from 'react';
import LoadingSpinner from '../../UI/LoadingSpinner'; // Assuming this path is correct
import { MeetingState } from '../../../types/meeting.types'; // For connectionMethod type

interface TranscriptionLoaderProps {
  status: string | null; // e.g., 'pending_transcripcion'
  connectionMethod: MeetingState['connectionStatus'];
}

const TranscriptionLoader: React.FC<TranscriptionLoaderProps> = ({ status, connectionMethod }) => {
  const getStatusMessage = (currentStatus: string | null) => {
    if (currentStatus === 'pending_transcripcion') {
      return {
        title: 'Transcribiendo audio...',
        description: 'El sistema está procesando el archivo de audio y generando la transcripción.',
        estimated: 'Tiempo estimado: 2-5 minutos según duración'
      };
    }
    return {
      title: 'Procesando Grabación...',
      description: 'Esperando la finalización del proceso de transcripción.',
      estimated: 'Esto puede tardar unos minutos.'
    };
  };

  const messageDetails = getStatusMessage(status);

  return (
    <div className="bg-white p-6 shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-700 mb-4">{messageDetails.title}</h2>
      <div className="my-6">
        <LoadingSpinner
          message={messageDetails.title}
          subMessage={messageDetails.description}
          connectionStatus={connectionMethod} // Pass the connection status
          estimatedTime={messageDetails.estimated}
        />
      </div>
      <p className="text-sm text-gray-600 text-center">
        Por favor, espera mientras se completa la transcripción. La página se actualizará automáticamente.
      </p>
    </div>
  );
};

export default TranscriptionLoader;