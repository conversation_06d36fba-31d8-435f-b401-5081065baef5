import React from 'react';
import { ProyectoSummary } from '../../types/proyecto';
import { Calendar, User, Building, TrendingUp, CheckCircle } from 'lucide-react';

interface ProjectCardsProps {
  projects: ProyectoSummary[];
  onProjectClick?: (project: ProyectoSummary) => void;
  loading?: boolean;
}

const ProjectCard: React.FC<{
  project: ProyectoSummary;
  onClick?: () => void;
}> = ({ project, onClick }) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: '2-digit', 
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getStatusColor = (estado: string) => {
    switch (estado) {
      case 'Completado':
        return 'bg-green-100 text-green-800';
      case 'En Ejecución':
        return 'bg-blue-100 text-blue-800';
      case 'En Revisión':
        return 'bg-purple-100 text-purple-800';
      case 'Pausado':
        return 'bg-orange-100 text-orange-800';
      case 'Cancelado':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDaysUntilDeadlineLocal = (): number | null => {
    if (!project.fecha_fin_estimada) return null;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const endDate = new Date(project.fecha_fin_estimada);
    endDate.setHours(0, 0, 0, 0);
    const diffTime = endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const isNearDeadline = () => {
    const diffDays = getDaysUntilDeadlineLocal();
    if (diffDays === null) return false;
    return diffDays <= 7 && diffDays >= 0;
  };

  const isOverdue = () => {
    const diffDays = getDaysUntilDeadlineLocal();
    return diffDays !== null && diffDays < 0 && project.estado !== 'Completado';
  };

  return (
    <div
      className={`
        bg-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer
        ${isOverdue() ? 'border-red-200 bg-red-50' : 
          isNearDeadline() ? 'border-yellow-200 bg-yellow-50' : 
          'border-gray-200 hover:border-gray-300'}
      `}
      onClick={onClick}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">
              {project.nombre}
            </h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.estado)}`}>
              {project.estado}
            </span>
          </div>
          
          {(isOverdue() || isNearDeadline()) && (
            <div className={`ml-3 p-1 rounded-full ${isOverdue() ? 'bg-red-100' : 'bg-yellow-100'}`}>
              <Calendar className={`h-4 w-4 ${isOverdue() ? 'text-red-600' : 'text-yellow-600'}`} />
            </div>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Progreso</span>
            <span className="text-sm text-gray-600">{project.progreso}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.progreso)}`}
              style={{ width: `${project.progreso}%` }}
            />
          </div>
        </div>

        {/* Task Progress */}
        <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm text-gray-700">Tareas</span>
          </div>
          <div className="text-sm font-medium text-gray-900">
            {project.tareas_completadas} / {project.total_tareas}
          </div>
        </div>

        {/* Details */}
        <div className="space-y-2">
          {project.responsable_nombre && (
            <div className="flex items-center text-sm text-gray-600">
              <User className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{project.responsable_nombre}</span>
            </div>
          )}
          
          {project.empresa_principal && (
            <div className="flex items-center text-sm text-gray-600">
              <Building className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{project.empresa_principal}</span>
            </div>
          )}
          
          {project.fecha_fin_estimada && (
            <div className={`flex items-center text-sm ${
              isOverdue() ? 'text-red-600' : 
              isNearDeadline() ? 'text-yellow-600' : 
              'text-gray-600'
            }`}>
              <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>
                {isOverdue() ? 'Vencido: ' : 'Vence: '}
                {formatDate(project.fecha_fin_estimada)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const ProjectCards: React.FC<ProjectCardsProps> = ({ 
  projects, 
  onProjectClick, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="bg-gray-100 rounded-lg h-64 animate-pulse" />
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="text-center py-12">
        <TrendingUp className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay proyectos activos
        </h3>
        <p className="text-gray-600">
          Crea tu primer proyecto para comenzar a gestionar tu trabajo.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onClick={() => onProjectClick?.(project)}
        />
      ))}
    </div>
  );
};

export default ProjectCards;
