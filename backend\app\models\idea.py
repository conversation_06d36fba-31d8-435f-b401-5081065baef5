"""
Pydantic models for Ideas functionality
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from enum import Enum

class EstadoIdea(str, Enum):
    """Estado enum for ideas"""
    PENDIENTE = "pendiente"
    IMPLEMENTADA = "implementada"
    DESCARTADA = "descartada"

class PrioridadIdea(str, Enum):
    """Prioridad enum for ideas"""
    BAJA = "Baja"
    MEDIA = "Media"
    ALTA = "Alta"
    URGENTE = "Urgente"

class IdeaBase(BaseModel):
    """Base model for idea data"""
    titulo: str = Field(..., min_length=1, max_length=255, description="Título de la idea")
    descripcion: Optional[str] = Field(None, description="Descripción detallada de la idea")
    empresa_relacionada_id: Optional[UUID] = Field(None, description="ID de la empresa relacionada")
    proyecto_relacionado_id: Optional[UUID] = Field(None, description="ID del proyecto relacionado")
    estado: EstadoIdea = Field(EstadoIdea.PENDIENTE, description="Estado de la idea")
    prioridad: PrioridadIdea = Field(PrioridadIdea.MEDIA, description="Prioridad de la idea")

class IdeaCreate(IdeaBase):
    """Model for creating a new idea"""
    pass

class IdeaUpdate(BaseModel):
    """Model for updating an existing idea"""
    titulo: Optional[str] = Field(None, min_length=1, max_length=255, description="Título de la idea")
    descripcion: Optional[str] = Field(None, description="Descripción detallada de la idea")
    empresa_relacionada_id: Optional[UUID] = Field(None, description="ID de la empresa relacionada")
    proyecto_relacionado_id: Optional[UUID] = Field(None, description="ID del proyecto relacionado")
    estado: Optional[EstadoIdea] = Field(None, description="Estado de la idea")
    prioridad: Optional[PrioridadIdea] = Field(None, description="Prioridad de la idea")

# Related entity models for populated data
class EmpresaRelacionada(BaseModel):
    """Model for related empresa data"""
    id: UUID
    nombre: str

class ProyectoRelacionado(BaseModel):
    """Model for related proyecto data"""
    id: UUID
    nombre: str

class Idea(IdeaBase):
    """Full idea model with database fields"""
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Related data (populated by service layer)
    empresa_relacionada: Optional[EmpresaRelacionada] = None
    proyecto_relacionado: Optional[ProyectoRelacionado] = None

    class Config:
        from_attributes = True

class IdeaResponse(Idea):
    """Response model for idea endpoints"""
    pass

class IdeasListResponse(BaseModel):
    """Response model for ideas list endpoints"""
    ideas: List[Idea]
    total: int
    filtered_count: int

class IdeaFilters(BaseModel):
    """Model for idea filtering parameters"""
    empresa_id: Optional[UUID] = Field(None, description="Filter by empresa ID")
    proyecto_id: Optional[UUID] = Field(None, description="Filter by proyecto ID")
    estado: Optional[EstadoIdea] = Field(None, description="Filter by estado")
    prioridad: Optional[PrioridadIdea] = Field(None, description="Filter by prioridad")
    search: Optional[str] = Field(None, min_length=1, description="Search term for titulo and descripcion")
    limit: Optional[int] = Field(50, ge=1, le=100, description="Maximum number of results")
    offset: Optional[int] = Field(0, ge=0, description="Number of results to skip")

# Empresa Ideas Details for company subtab
class EmpresaIdeasDetails(BaseModel):
    """Model for empresa ideas details in company subtab"""
    ideas: List[Idea]
    total_ideas: int
    ideas_por_estado: dict = Field(default_factory=dict)
    ideas_por_prioridad: dict = Field(default_factory=dict)
    
    class Config:
        from_attributes = True
