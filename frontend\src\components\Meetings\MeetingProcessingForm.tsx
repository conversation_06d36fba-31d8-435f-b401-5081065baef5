import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMeetingProcessing } from '../../hooks/useMeetingProcessing';
import UploadStep from './MeetingSteps/UploadStep';
import SpeakerAssignmentStep from './MeetingSteps/SpeakerAssignmentStep';
import ResultsStep from './MeetingSteps/ResultsStep';

import StepTransition from './MeetingSteps/StepTransition';
import ConnectionStatus from '../UI/ConnectionStatus'; // Assuming this path is correct
// import { AssociatedEntity, SpeakerAssignable } from '../../types/meeting.types'; // For props drilling if needed - Removed as unused for now

const MeetingProcessingForm: React.FC = () => {
  const navigate = useNavigate();
  const {
    state,
    // isConnected, // isConnected is part of state.connectionStatus now
    // isPolling, // isPolling is part of state.connectionStatus now
    addAssociatedEntity,
    removeAssociatedEntity,
    updateSpeakerAssignment,
    updateEntrevista,
    updateVideo,
    setCurrentStep,
    setUploadProgress,
    setUploading,
    setMeetingId,
    user,
    session,
    // loadMeetingData, // Exposing loadMeetingData from the hook - Removed as unused for now
  } = useMeetingProcessing();

  // Component-specific UI state that might not belong in the global meeting state
  const [selectedFile, setSelectedFile] = useState<File | null>(null); // For UploadStep
  const audioPlayerRef = useRef<HTMLAudioElement>(null!); // For SpeakerAssignmentStep

  // Define a more specific type for formData in UploadStep if possible, or use 'any' for now
  // This function would be passed to UploadStep and called internally there.
  // const handleStep1Submit = async (formData: any) => {
  //   // Logic for TUS upload and backend call will be inside UploadStep
  //   // This function in the parent might just orchestrate step transition if needed
  //   console.log("Step 1 Submitted with data:", formData);
  //   // Example: setCurrentStep(1.5); // Transition to loading
  // };

  // This function would be passed to SpeakerAssignmentStep
  // const handleStep2Submit = async () => {
  //   // Logic for submitting speaker assignments will be inside SpeakerAssignmentStep
  //   console.log("Step 2 Submitted with assignments:", state.speakerAssignments);
  //   // Example: setCurrentStep(2.5); // Transition to AI loading
  // };

  // Render step based on current state
  const renderStep = () => {
    switch (state.currentStep) {
      case 1:
        return (
          <UploadStep
            // Pass necessary parts of state and dispatchers/callbacks
            meetingState={state} // Pass the whole state or specific parts
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            onAddAssociatedEntity={addAssociatedEntity}
            onRemoveAssociatedEntity={removeAssociatedEntity}
            onSetUploadProgress={setUploadProgress}
            onSetUploading={setUploading}
            onSetMeetingId={setMeetingId}
            onSetCurrentStep={setCurrentStep}
            session={session} // Pass session for TUS and API calls
            user={user} // Pass user for TUS path
            // onSubmitForm={handleStep1Submit} // Or handle submit logic within UploadStep
          />
        );

      case 1.5: // Loading for transcription
        return (
          <StepTransition
            currentStep={1.5}
            status={state.status}
            title="Transcribiendo audio"
            description="El sistema está procesando el archivo de audio y generando la transcripción"
            estimatedTime="Tiempo estimado: 2-5 minutos según duración"
            progress={state.uploadProgress || 0}
          />
        );

      case 2:
        return (
          <SpeakerAssignmentStep
            meetingState={state}
            onUpdateSpeakerAssignment={updateSpeakerAssignment}
            onAddAssociatedEntity={addAssociatedEntity} // If new entities can be added in this step
            onUpdateEntrevista={updateEntrevista} // For updating entrevista field
            onUpdateVideo={updateVideo} // For updating video field
            onSetCurrentStep={setCurrentStep}
            session={session} // For API calls
            audioPlayerRef={audioPlayerRef} // For audio playback
            navigate={navigate} // For navigation if needed
            // onSubmitAssignments={handleStep2Submit} // Or handle submit logic within SpeakerAssignmentStep
          />
        );

      case 2.5: // Loading for AI processing
        return (
          <StepTransition
            currentStep={2.5}
            status={state.status}
            title="Procesando con IA"
            description="Generando resumen automático y extrayendo puntos clave de la reunión"
            estimatedTime="Tiempo estimado: 30-60 segundos"
            progress={75} // Fixed progress for AI processing
          />
        );

      case 3:
        return (
          <ResultsStep
            meetingState={state}
            onSetCurrentStep={setCurrentStep}
            // isWaitingForAI might be derived from state.status === 'procesando_ia'
            // connectionMethod is state.connectionStatus
          />
        );

      default:
        // Fallback or initial loading state if needed
        return <div>Cargando configuración de la reunión...</div>;
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-semibold text-gray-800 mb-6">
        {/* Title can be dynamic based on whether it's a new or existing meeting */}
        {state.id ? 'Procesando Reunión' : 'Procesar Nueva Reunión'}
      </h1>

      {/* Step Indicator with Connection Status */}
      <div className="mb-8 flex justify-between items-center">
        <p className="text-sm text-gray-600">
          Paso {Math.floor(state.currentStep)} de 3
          {state.currentStep % 1 !== 0 && <span className="text-gray-400"> (procesando...)</span>}
        </p>
        {state.connectionStatus !== 'disconnected' && state.id && (
          <ConnectionStatus status={state.connectionStatus} className="text-xs" />
        )}
      </div>

      {/* Render current step */}
      {renderStep()}

      {/* Hidden audio player for speaker preview, managed by SpeakerAssignmentStep */}
      <audio ref={audioPlayerRef} className="hidden" />
    </div>
  );
};

export default MeetingProcessingForm;
