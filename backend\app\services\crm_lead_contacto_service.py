# from sqlalchemy.orm import Session # Removed unused import
from uuid import uuid4, UUID
import datetime

from app.models.crm_lead_contacto import LeadContactoCreate
from app.core.database import get_supabase_client

async def create_lead_contacto(lead_contacto_in: LeadContactoCreate, user_id: UUID) -> dict: # Removed db: Session
    """
    Create a new lead contacto.
    `user_id` is for audit purposes if needed.
    """
    supabase = await get_supabase_client() # Added await
    
    lead_contacto_data = lead_contacto_in.dict(exclude_none=True)
    lead_contacto_data['id'] = str(uuid4())
    # Ensure lead_empresa_id is string for Supabase
    lead_contacto_data['lead_empresa_id'] = str(lead_contacto_in.lead_empresa_id)


    try:
        response = await supabase.table('lead_contactos').insert(lead_contacto_data).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]
        else:
            print(f"Supabase insert response for lead_contacto: {response}") 
            raise Exception("Failed to create lead contacto or retrieve created data.")

    except Exception as e:
        print(f"Error creating lead contacto: {e}")
        raise e

# Placeholder for other service functions

async def get_lead_contactos(
    search: str | None = None,
    lead_empresa_id: UUID | None = None,
    skip: int = 0,
    limit: int = 100
) -> list[dict]:
    """
    Retrieve lead contactos, with optional search and filtering by lead_empresa_id.
    """
    supabase = await get_supabase_client() # Added await
    # To get lead_empresa.nombre for the subLabel, we need a join.
    # Select "*, lead_empresa:leads_empresas (id, nombre)"
    query = supabase.table('lead_contactos').select("*, lead_empresa:leads_empresas (id, nombre)").order('apellidos').order('nombre').offset(skip).limit(limit)

    if lead_empresa_id:
        query = query.eq('lead_empresa_id', str(lead_empresa_id))

    if search:
        search_term = f"%{search}%"
        # Searching in nombre, apellidos, or email.
        query = query.or_(f"nombre.ilike.{search_term},apellidos.ilike.{search_term},email.ilike.{search_term}")
        
    try:
        response = query.execute() # Changed to synchronous
        if response.data:
            from app.models.crm_lead_contacto import LeadContacto # Add import
            from pydantic import ValidationError # Import ValidationError
            import logging # Import logging
            logger = logging.getLogger(__name__) # Get logger instance

            validated_contactos = []
            for contacto_data in response.data:
                try:
                    validated_contactos.append(LeadContacto.model_validate(contacto_data))
                except ValidationError as ve:
                    logger.error(f"Pydantic ValidationError for lead_contacto_data: {contacto_data}")
                    logger.error(f"Details: {ve.errors(include_input=False)}")
                    raise ve
            # Data should include "lead_empresa": {"id": "...", "nombre": "..."}
            # LeadContacto.model_validate should handle the nested LeadEmpresaBasicInfo
            return validated_contactos
        return []
    except ValidationError as ve_outer:
        # logger is already defined above if this block is reached via re-raise
        logger.error(f"Outer Pydantic ValidationError in get_lead_contactos: {ve_outer.errors(include_input=False)}")
        raise ve_outer
    except Exception as e:
        import logging # Ensure logging is imported here too for safety
        logger_fallback = logging.getLogger(__name__)
        logger_fallback.error(f"Generic error fetching or validating lead_contactos: {e}", exc_info=True)
        raise e
