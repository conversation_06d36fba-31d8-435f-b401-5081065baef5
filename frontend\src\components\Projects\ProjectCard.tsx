import React, { memo } from 'react';
import { Proyecto } from '../../types/proyecto';
import { Calendar, User, TrendingUp, MoreVertical, Trash2 } from 'lucide-react';
import { formatDate, getStatusColor, getProgressColor } from '../../utils/projectUtils';

interface ProjectCardProps {
  project: Proyecto;
  onProjectClick: (projectId: string) => void;
  onDeleteProject: (projectId: string, projectName: string) => Promise<void>;
  onDragStart?: (e: React.DragEvent, project: Proyecto) => void;
  isDragging?: boolean;
  showActions?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = memo(({
  project,
  onProjectClick,
  onDeleteProject,
  onDragStart,
  isDragging = false,
  showActions = true
}) => {
  const [showMenu, setShowMenu] = React.useState(false);

  const getDaysUntilDeadlineLocal = (): number | null => {
    if (!project.fecha_fin_estimada) return null;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const endDate = new Date(project.fecha_fin_estimada);
    endDate.setHours(0, 0, 0, 0);
    const diffTime = endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const isNearDeadline = () => {
    const diffDays = getDaysUntilDeadlineLocal();
    if (diffDays === null) return false;
    return diffDays <= 7 && diffDays >= 0;
  };

  const isOverdue = () => {
    const diffDays = getDaysUntilDeadlineLocal();
    return diffDays !== null && diffDays < 0 && project.estado !== 'completado';
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent navigation when clicking on menu or actions
    if ((e.target as HTMLElement).closest('.project-card-menu')) {
      return;
    }
    onProjectClick(project.id);
  };

  const handleDeleteClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    if (window.confirm(`¿Estás seguro de que quieres eliminar el proyecto "${project.nombre}"?`)) {
      await onDeleteProject(project.id, project.nombre);
    }
  };

  return (
    <div
      draggable={!!onDragStart}
      onDragStart={onDragStart ? (e) => onDragStart(e, project) : undefined}
      className={`
        bg-white rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer
        ${isDragging ? 'opacity-50' : ''}
        ${isOverdue() ? 'border-red-200 bg-red-50' : 
          isNearDeadline() ? 'border-yellow-200 bg-yellow-50' : 
          'border-gray-200 hover:border-gray-300'}
      `}
      onClick={handleCardClick}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
              {project.nombre}
            </h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.estado)}`}>
              {project.estado}
            </span>
          </div>
          
          {showActions && (
            <div className="relative project-card-menu">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMenu(!showMenu);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <MoreVertical className="h-4 w-4" />
              </button>
              {showMenu && (
                <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowMenu(false);
                      onProjectClick(project.id);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Ver detalles
                  </button>
                  <button
                    onClick={handleDeleteClick}
                    className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Eliminar
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Description */}
        {project.descripcion && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {project.descripcion}
          </p>
        )}

        {/* Progress */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Progreso</span>
            <span className="text-sm font-medium text-gray-900">{project.progreso}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.progreso)}`}
              style={{ width: `${project.progreso}%` }}
            />
          </div>
        </div>

        {/* Footer Info */}
        <div className="space-y-2">
          {project.responsable_usuario && (
            <div className="flex items-center text-sm text-gray-600">
              <User className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{project.responsable_usuario.nombre}</span>
            </div>
          )}
          
          {project.fecha_fin_estimada && (
            <div className={`flex items-center text-sm ${
              isOverdue() ? 'text-red-600' : 
              isNearDeadline() ? 'text-yellow-600' : 
              'text-gray-600'
            }`}>
              <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>
                {isOverdue() ? 'Vencido: ' : 'Vence: '}
                {formatDate(project.fecha_fin_estimada)}
              </span>
            </div>
          )}

          {/* Task count */}
          <div className="flex items-center text-sm text-gray-600">
            <TrendingUp className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>
              {project.tareas_completadas || 0} / {project.total_tareas || 0} tareas
            </span>
          </div>
        </div>
      </div>

      {/* Click outside handler for menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          role="button"
          tabIndex={-1}
          aria-label="Close menu"
          onClick={() => setShowMenu(false)}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setShowMenu(false);
            }
          }}
        />
      )}
    </div>
  );
});

ProjectCard.displayName = 'ProjectCard';

export default ProjectCard;
