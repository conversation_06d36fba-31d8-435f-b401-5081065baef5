import React, { memo } from 'react';
import { Proyecto } from '../../types/proyecto';
import { EditableField } from '../UI/EditableField';
import { getProgressColor } from '../../utils/projectUtils';

interface ProjectInfoSectionProps {
  project: Proyecto;
  onUpdateProject: (field: keyof Proyecto, value: string | number | boolean | null) => Promise<void>;
}

const ProjectInfoSection: React.FC<ProjectInfoSectionProps> = memo(({
  project,
  onUpdateProject
}) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Información del Proyecto</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <EditableField
            label="Descripción"
            value={project.descripcion || ''}
            type="textarea"
            onSave={(value) => onUpdateProject('descripcion', value)}
            placeholder="Descripción del proyecto"
          />
        </div>
        
        <div>
          <EditableField
            label="Objetivo"
            value={project.objetivo || ''}
            type="textarea"
            onSave={(value) => onUpdateProject('objetivo', value)}
            placeholder="Objetivo del proyecto"
          />
        </div>
        
        <div>
          <EditableField
            label="Fecha de inicio"
            value={project.fecha_inicio || ''}
            type="date"
            onSave={(value) => onUpdateProject('fecha_inicio', value)}
          />
        </div>
        
        <div>
          <EditableField
            label="Fecha fin estimada"
            value={project.fecha_fin_estimada || ''}
            type="date"
            onSave={(value) => onUpdateProject('fecha_fin_estimada', value)}
          />
        </div>
        
        <div>
          <EditableField
            label="Presupuesto"
            value={project.presupuesto || ''}
            type="number"
            onSave={(value) => onUpdateProject('presupuesto', value)}
            placeholder="0.00"
          />
        </div>
        
        <div>
          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700">Progreso</label>
          </div>
          <div className="flex items-center">
            <div className="flex-1 bg-gray-200 rounded-full h-3 mr-4">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(project.progreso)}`}
                style={{ width: `${project.progreso}%` }}
              />
            </div>
            <span className="text-sm font-medium text-gray-900">{project.progreso}%</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">Calculado automáticamente basado en tareas completadas</p>
        </div>
      </div>
    </div>
  );
});

ProjectInfoSection.displayName = 'ProjectInfoSection';

export default ProjectInfoSection;
