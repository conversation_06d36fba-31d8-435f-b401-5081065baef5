import { useEffect, useRef, useCallback } from 'react';
import { RealtimeChannel, REALTIME_SUBSCRIBE_STATES } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { ReunionRecord, ConnectionStatus } from '../types/meeting.types';

interface UseRealtimeMeetingOptions {
  meetingId: string | null;
  onUpdate: (update: ReunionRecord) => void;
  onConnectionStatusChange: (status: ConnectionStatus) => void;
  onStartPolling: () => void;
  currentConnectionState: ConnectionStatus;
}

export function useRealtimeMeeting({
  meetingId,
  onUpdate,
  onConnectionStatusChange,
  onStartPolling,
  currentConnectionState,
}: UseRealtimeMeetingOptions) {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const onUpdateRef = useRef(onUpdate);
  const onConnectionStatusChangeRef = useRef(onConnectionStatusChange);
  const onStartPollingRef = useRef(onStartPolling);
  // isSubscribedRef might still be useful locally if needed
  // const isSubscribedRef = useRef(false);

  // Keep refs current
  useEffect(() => {
    onUpdateRef.current = onUpdate;
    onConnectionStatusChangeRef.current = onConnectionStatusChange;
    onStartPollingRef.current = onStartPolling;
  }, [onUpdate, onConnectionStatusChange, onStartPolling]);

  // No longer using useConnectionStateMachine directly in this hook.
  // Parent (useMeetingProcessing) will manage polling/retry based on raw status.

  // Handle Realtime updates from Broadcast
  const handleBroadcastUpdate = useCallback((broadcastPayload: { event?: string, type?: string, payload: unknown }) => {
    // The payload from realtime.broadcast_changes contains the database trigger data
    // The structure is: { event: 'UPDATE', payload: { id: broadcast_id, record: ReunionRecord, old_record: ReunionRecord, ... } }
    console.log('[useRealtimeMeeting] Broadcast update received:', broadcastPayload);

    try {
      const payload = broadcastPayload.payload;

      // Validate payload
      if (!payload || typeof payload !== 'object') {
        console.warn('[useRealtimeMeeting] Invalid payload received:', payload);
        return;
      }

      // Extract the actual reunion record from the payload
      // The real reunion data is in payload.record (for UPDATE) or payload itself (for direct record)
      let reunionRecord: ReunionRecord;

      // Type guard for database trigger payload
      if (payload && typeof payload === 'object' && 'record' in payload && payload.record && typeof payload.record === 'object') {
        // This is a database trigger payload with record structure
        reunionRecord = payload.record as ReunionRecord;
        console.log('[useRealtimeMeeting] Extracted reunion record from payload.record:', reunionRecord);
      } else if (payload && typeof payload === 'object' && 'id' in payload && 'estado_procesamiento' in payload) {
        // This is a direct reunion record
        reunionRecord = payload as ReunionRecord;
        console.log('[useRealtimeMeeting] Using payload as direct reunion record:', reunionRecord);
      } else {
        console.warn('[useRealtimeMeeting] Could not extract reunion record from payload:', payload);
        return;
      }

      // Validate that the update is for the current meeting
      if (reunionRecord.id !== meetingId) {
        console.log(`[useRealtimeMeeting] Update for different meeting (${reunionRecord.id}), ignoring. Expected: ${meetingId}`);
        return;
      }

      console.log(`[useRealtimeMeeting] Processing update for meeting ${reunionRecord.id}, event: ${broadcastPayload.event}`);

      if (broadcastPayload.event === 'UPDATE' && reunionRecord) {
        onUpdateRef.current(reunionRecord);
      } else if (broadcastPayload.event === 'INSERT' && reunionRecord) {
        // Handle insert if needed, e.g. if subscription starts before first fetch
        onUpdateRef.current(reunionRecord);
      }
      // Handle 'DELETE' if necessary
    } catch (error) {
      console.error('[useRealtimeMeeting] Error handling broadcast update:', error);
    }
  }, [meetingId]);

  // Create and subscribe to channel (will be called directly in main useEffect)
  // This function is now self-contained for a single attempt.
  const attemptSubscription = useCallback(async (id: string) => {
    if (channelRef.current && channelRef.current.topic === `reunion_updates:${id}`) {
        console.log(`[useRealtimeMeeting] attemptSubscription: Already have a channel for ${id}, potentially resubscribing or ensuring state.`);
        // If channel exists, it might be in a weird state. Let's try to resubscribe.
        // Or, the main useEffect's cleanup should handle full removal before calling this.
        // For simplicity now, assume main useEffect cleans up thoroughly.
    }

    // Clean up any existing channel before creating a new one.
    if (channelRef.current) {
      console.log(`[useRealtimeMeeting] attemptSubscription: Removing existing channel ${channelRef.current.topic}`);
      try {
        await supabase.removeChannel(channelRef.current);
      } catch (e) {
        console.warn('[useRealtimeMeeting] attemptSubscription: Error removing previous channel:', e);
      }
      channelRef.current = null;
    }

    const channelName = `reunion_updates:${id}`;
    console.log(`[useRealtimeMeeting] Creating BROADCAST channel: ${channelName}`);

    const channel = supabase.channel(channelName, {
      config: {
        private: true // Required for RLS on realtime.messages
      }
    });

    channelRef.current = channel;

    // Set up broadcast listener
    // The SQL trigger uses TG_OP as the event name.
    channel.on(
      'broadcast',
      { event: 'UPDATE' }, // Listen specifically for 'UPDATE' events from the trigger
      handleBroadcastUpdate
    );
    channel.on(
      'broadcast',
      { event: 'INSERT' }, // Also listen for 'INSERT' if a meeting could be created and we subscribe fast
      handleBroadcastUpdate
    );
    // Add listener for 'DELETE' if needed

    try {
      // Authorize for private channel
      // This should ideally be called once, perhaps when the user session is established.
      // For now, calling it here before each subscription attempt to ensure it's set.
      // Supabase client might be smart enough to not re-auth if token is still valid.
      console.log('[useRealtimeMeeting] Setting Realtime Auth for private channel');
      await supabase.realtime.setAuth();
      console.log('[useRealtimeMeeting] Realtime Auth set successfully.');
    } catch (authError) {
      console.error('[useRealtimeMeeting] Exception during Realtime Auth:', authError);
      onConnectionStatusChangeRef.current('error');
      return;
    }

    channel.subscribe((status, err) => {
      console.log(`[useRealtimeMeeting] Raw subscription status for ${channelName}: ${status}`, err || '');
      onConnectionStatusChangeRef.current(status); // Pass Supabase status directly

      if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        console.warn(`[useRealtimeMeeting] Channel error or timeout for ${channelName}. Suggesting polling.`);
        onStartPollingRef.current(); // Suggest polling on these critical errors
      }
    });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meetingId, handleBroadcastUpdate]); // Dependencies for attemptSubscription - using refs for callbacks


  useEffect(() => {
    // Main useEffect: Simplified to be synchronous and declarative based on meetingId.
    // attemptSubscription is async and handles its own internal logic.

    if (meetingId) {
      console.log(`[useRealtimeMeeting] useEffect: Valid meetingId '${meetingId}'. Calling attemptSubscription.`);
      // Call attemptSubscription directly. It's a useCallback, so its reference is stable if its own deps are stable.
      // It handles its own async nature and channel cleanup/creation.
      attemptSubscription(meetingId);
    } else {
      // No meetingId: ensure everything is cleaned up and state is 'disconnected'.
      console.log('[useRealtimeMeeting] useEffect: No meetingId. Ensuring cleanup and disconnected state.');
      if (channelRef.current) {
        const chanToRem = channelRef.current;
        console.log(`[useRealtimeMeeting] useEffect (no meetingId branch): Removing channel ${chanToRem.topic}`);
        supabase.removeChannel(chanToRem)
          .catch(e => console.warn('[useRealtimeMeeting] Error removing channel (no meetingId branch):', e));
        channelRef.current = null;
      }
      onConnectionStatusChangeRef.current('disconnected');
    }

    return () => {
      // Simplified cleanup - only remove channel on unmount or meetingId change
      console.log(`[useRealtimeMeeting] useEffect CLEANUP. meetingId: ${meetingId}, channel: ${channelRef.current?.topic}`);

      // Only clean up if there's actually a channel
      if (channelRef.current) {
        const chanToRem = channelRef.current;
        console.log(`[useRealtimeMeeting] useEffect CLEANUP: Removing channel ${chanToRem.topic}`);
        supabase.removeChannel(chanToRem)
          .catch(e => console.warn('[useRealtimeMeeting] Error removing channel in cleanup:', e));
        channelRef.current = null;
      }
    };
  }, [meetingId, attemptSubscription]);

  return {
    isConnected: currentConnectionState === REALTIME_SUBSCRIBE_STATES.SUBSCRIBED, // Use enum member
  };
}