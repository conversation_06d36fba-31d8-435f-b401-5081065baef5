import React from 'react';

interface SidebarNavProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
  isMobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
}

const SidebarNav: React.FC<SidebarNavProps> = ({
  isCollapsed,
  toggleSidebar,
  isMobileMenuOpen,
  toggleMobileMenu,
}) => {
  // For mobile view, the sidebar is always "expanded" visually, but occupies full width of its container.
  // The `isCollapsed` prop primarily controls the desktop collapsed state.
  const displayCollapsed = !isMobileMenuOpen && isCollapsed;

  return (
    <aside
      className={`bg-gray-700 text-white ${
        isMobileMenuOpen
          ? 'w-64 z-40 h-full' // Full width for mobile overlay - Lower than chat (z-50)
          : displayCollapsed
          ? 'w-16' // Desktop collapsed
          : 'w-64' // Desktop expanded
      } transition-all duration-300 ease-in-out flex flex-col`}
    >
      <div className="p-4 flex justify-between items-center">
        {!displayCollapsed && <span className="text-xl font-semibold">Navigation</span>}
        {/* Show desktop toggle only if not in mobile menu mode */}
        {!isMobileMenuOpen && (
          <button
            onClick={toggleSidebar}
            className="p-2 rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {displayCollapsed ? '>' : '<'}
          </button>
        )}
        {/* Show close button for mobile menu */}
        {isMobileMenuOpen && (
           <button
            onClick={toggleMobileMenu}
            className="p-2 rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600"
            aria-label="Close navigation menu"
          >
            X {/* Simple close icon */}
          </button>
        )}
      </div>
      <nav className="flex-grow p-4 space-y-2">
        {/* Navigation links with better responsive design */}
        <a
          href="/dashboard"
          onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
          className={`block py-3 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : ''}`}
        >
          {displayCollapsed ? '📊' : '📊 Dashboard'}
        </a>
        <a
          href="/chat"
          onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
          className={`block py-3 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : ''}`}
        >
          {displayCollapsed ? '💬' : '💬 Chat'}
        </a>
        <a
          href="/meetings"
          onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
          className={`block py-3 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : ''}`}
        >
          {displayCollapsed ? '🎥' : '🎥 Reuniones'}
        </a>

        {/* Project Management Links */}
        <div className={`${displayCollapsed ? 'border-t border-gray-600 pt-2 mt-2' : 'mt-4'}`}>
          {!displayCollapsed && <p className="text-xs text-gray-400 uppercase tracking-wider mb-2 px-4">Gestión</p>}
          <a
            href="/proyectos"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '📁' : '📁 Proyectos'}
          </a>
          <a
            href="/procesos"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '⚙️' : '⚙️ Procesos'}
          </a>
          <a
            href="/tareas"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '✅' : '✅ Tareas'}
          </a>
          <a
            href="/plantillas"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '📋' : '📋 Plantillas'}
          </a>
        </div>
        {/* CRM Links */}
        <div className={`${displayCollapsed ? 'border-t border-gray-600 pt-2 mt-2' : 'mt-4'}`}>
          {!displayCollapsed && <p className="text-xs text-gray-400 uppercase tracking-wider mb-2 px-4">CRM</p>}
          <a
            href="/crm/empresas"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '🏢' : '🏢 Empresas'}
          </a>
          <a
            href="/crm/personas"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '👤' : '👤 Personas'}
          </a>
          <a
            href="/crm/leads"
            onClick={isMobileMenuOpen ? toggleMobileMenu : undefined}
            className={`block py-2 px-4 rounded transition-colors hover:bg-gray-600 ${displayCollapsed ? 'text-center' : 'ml-2'}`}
          >
            {displayCollapsed ? '🎯' : '🎯 Leads'}
          </a>
        </div>
      </nav>
    </aside>
  );
};

export default SidebarNav;