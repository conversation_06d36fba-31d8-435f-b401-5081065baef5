import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTasks, useProyectos } from '../../hooks/useTasks';
import { TareaCreate, EstadoTarea, PrioridadTarea, UrgenciaTarea, ESTADOS_TAREA, PRIORIDADES_TAREA, URGENCIAS_TAREA } from '../../types/tarea';
import {
  Save,
  X,
  Calendar,
  FolderOpen,
  AlertTriangle,
  Clock
} from 'lucide-react';

// Validation schema
const tareaSchema = z.object({
  titulo: z.string().min(1, 'El título es requerido').max(255, 'El título es muy largo'),
  descripcion: z.string().optional(),
  proyecto_id: z.string().optional(),
  estado: z.enum(['Pendiente', 'En Progreso', 'En Revisión', 'Bloqueada', 'Completada']).optional(),
  prioridad: z.enum(['Baja', 'Media', 'Alta', 'Urgente']).optional(),
  urgencia: z.enum(['Urgente', 'No Urgente']).optional(),
  fecha_vencimiento: z.string().optional(),
  asignado_a: z.string().optional(),
  tarea_padre_id: z.string().optional(),
  info_adicional: z.string().optional(),
  empresas_asociadas_ids: z.array(z.string()).optional(),
  etiquetas_ids: z.array(z.string()).optional(),
});

type TareaFormValues = z.infer<typeof tareaSchema>;

const TaskCreateForm: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const parentId = searchParams.get('parent');
  
  const { createTask } = useTasks();
  const { proyectos } = useProyectos();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<TareaFormValues>({
    resolver: zodResolver(tareaSchema),
    defaultValues: {
      titulo: '',
      descripcion: '',
      proyecto_id: '',
      estado: 'Pendiente',
      prioridad: 'Media',
      urgencia: 'No Urgente',
      fecha_vencimiento: '',
      asignado_a: '',
      tarea_padre_id: parentId || '',
      info_adicional: '',
      empresas_asociadas_ids: [],
      etiquetas_ids: [],
    }
  });

  const onSubmit = async (data: TareaFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Convert form data to API format
      const tareaData: TareaCreate = {
        titulo: data.titulo,
        descripcion: data.descripcion || undefined,
        proyecto_id: data.proyecto_id || undefined,
        estado: data.estado as EstadoTarea,
        prioridad: data.prioridad as PrioridadTarea,
        urgencia: data.urgencia as UrgenciaTarea,
        fecha_vencimiento: data.fecha_vencimiento || undefined,
        asignado_a: data.asignado_a || undefined,
        tarea_padre_id: data.tarea_padre_id || undefined,
        info_adicional: data.info_adicional || undefined,
        empresas_asociadas_ids: data.empresas_asociadas_ids || [],
        etiquetas_ids: data.etiquetas_ids || [],
      };

      const newTask = await createTask(tareaData);

      // Navigate to the new task details or back to tasks list
      navigate(`/tareas/${newTask.id}`);
    } catch (err: unknown) {
      console.error('Error creating task:', err);
      setError(err instanceof Error ? err.message : 'Error al crear la tarea');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/tareas');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {parentId ? 'Nueva Subtarea' : 'Nueva Tarea'}
            </h1>
            <p className="text-gray-600 mt-1">
              Crea una nueva tarea para organizar tu trabajo
            </p>
          </div>
          
          <button
            onClick={handleCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Información Básica</h2>
            
            <div className="grid grid-cols-1 gap-6">
              {/* Title */}
              <div>
                <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-2">
                  Título *
                </label>
                <input
                  {...register('titulo')}
                  type="text"
                  id="titulo"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ingresa el título de la tarea"
                />
                {errors.titulo && (
                  <p className="mt-1 text-sm text-red-600">{errors.titulo.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción
                </label>
                <textarea
                  {...register('descripcion')}
                  id="descripcion"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe los detalles de la tarea"
                />
                {errors.descripcion && (
                  <p className="mt-1 text-sm text-red-600">{errors.descripcion.message}</p>
                )}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Configuración</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Project */}
              <div>
                <label htmlFor="proyecto_id" className="block text-sm font-medium text-gray-700 mb-2">
                  <FolderOpen className="h-4 w-4 inline mr-1" />
                  Proyecto
                </label>
                <select
                  {...register('proyecto_id')}
                  id="proyecto_id"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Seleccionar proyecto</option>
                  {proyectos.map((proyecto) => (
                    <option key={proyecto.id} value={proyecto.id}>
                      {proyecto.nombre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status */}
              <div>
                <label htmlFor="estado" className="block text-sm font-medium text-gray-700 mb-2">
                  Estado
                </label>
                <select
                  {...register('estado')}
                  id="estado"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {ESTADOS_TAREA.map((estado) => (
                    <option key={estado} value={estado}>
                      {estado}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priority */}
              <div>
                <label htmlFor="prioridad" className="block text-sm font-medium text-gray-700 mb-2">
                  Prioridad
                </label>
                <select
                  {...register('prioridad')}
                  id="prioridad"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {PRIORIDADES_TAREA.map((prioridad) => (
                    <option key={prioridad} value={prioridad}>
                      {prioridad}
                    </option>
                  ))}
                </select>
              </div>

              {/* Urgency */}
              <div>
                <label htmlFor="urgencia" className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Urgencia
                </label>
                <select
                  {...register('urgencia')}
                  id="urgencia"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {URGENCIAS_TAREA.map((urgencia) => (
                    <option key={urgencia} value={urgencia}>
                      {urgencia}
                    </option>
                  ))}
                </select>
              </div>

              {/* Due Date */}
              <div>
                <label htmlFor="fecha_vencimiento" className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Fecha de Vencimiento
                </label>
                <input
                  {...register('fecha_vencimiento')}
                  type="date"
                  id="fecha_vencimiento"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Información Adicional</h2>
            
            <div>
              <label htmlFor="info_adicional" className="block text-sm font-medium text-gray-700 mb-2">
                Notas Adicionales
              </label>
              <textarea
                {...register('info_adicional')}
                id="info_adicional"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Información adicional sobre la tarea"
              />
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Crear Tarea
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskCreateForm;
