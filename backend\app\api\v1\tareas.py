import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
from uuid import UUID

from app.models.tarea import (
    TareaCreate, TareaUpdate, TareaResponse, TareaListResponse,
    TareaMatrix, TareaKanbanBoard, TareaFilters,
    EstadoTarea, PrioridadTarea, UrgenciaTarea
)
from app.core.database import get_supabase_client
from app.core.security import get_current_user_id
from app.services import tarea_service

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=TareaResponse, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=TareaResponse, status_code=status.HTTP_201_CREATED)
async def create_tarea(
    tarea_in: TareaCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new tarea.
    """
    try:
        logger.info(f"Creating tarea: {tarea_in.titulo} by user {current_user_id}")
        tarea = await tarea_service.create_tarea(tarea_in, current_user_id)
        return TareaResponse(**tarea.model_dump())
    except Exception as e:
        logger.error(f"Error creating tarea: {str(e)}")
        raise

@router.get("/", response_model=TareaListResponse)
@router.get("", response_model=TareaListResponse)
async def get_tareas(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    estado: Optional[EstadoTarea] = Query(None, description="Filter by estado"),
    prioridad: Optional[PrioridadTarea] = Query(None, description="Filter by prioridad"),
    urgencia: Optional[UrgenciaTarea] = Query(None, description="Filter by urgencia"),
    proyecto_id: Optional[UUID] = Query(None, description="Filter by proyecto"),
    asignado_a: Optional[UUID] = Query(None, description="Filter by asignado"),
    empresa_id: Optional[UUID] = Query(None, description="Filter by empresa"),
    vencidas: Optional[bool] = Query(None, description="Filter by vencidas"),
    search: Optional[str] = Query(None, description="Search in titulo and descripcion"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get list of tareas with optional filtering.
    """
    try:
        logger.info(f"Fetching tareas for user {current_user_id}")
        
        filters = TareaFilters(
            estado=estado,
            prioridad=prioridad,
            urgencia=urgencia,
            proyecto_id=proyecto_id,
            asignado_a=asignado_a,
            empresa_id=empresa_id,
            vencidas=vencidas,
            search=search
        )
        
        return await tarea_service.get_tareas(
            skip=skip,
            limit=limit,
            filters=filters
        )
    except Exception as e:
        logger.error(f"Error fetching tareas: {str(e)}")
        raise

@router.get("/matrix", response_model=TareaMatrix)
async def get_tareas_matrix(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get tareas organized in importance/urgency matrix for dashboard.
    """
    try:
        logger.info(f"Fetching tareas matrix for user {current_user_id}")
        return await tarea_service.get_tareas_matrix_dashboard()
    except Exception as e:
        logger.error(f"Error fetching tareas matrix: {str(e)}")
        raise

@router.get("/kanban", response_model=TareaKanbanBoard)
async def get_tareas_kanban(
    proyecto_id: Optional[UUID] = Query(None, description="Filter by proyecto"),
    asignado_a: Optional[UUID] = Query(None, description="Filter by asignado"),
    empresa_id: Optional[UUID] = Query(None, description="Filter by empresa"),
    estado: Optional[EstadoTarea] = Query(None, description="Filter by estado"),
    prioridad: Optional[PrioridadTarea] = Query(None, description="Filter by prioridad"),
    urgencia: Optional[UrgenciaTarea] = Query(None, description="Filter by urgencia"),
    vencidas: Optional[bool] = Query(None, description="Filter by vencidas"),
    search: Optional[str] = Query(None, description="Search in titulo and descripcion"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get tareas organized in Kanban board format.
    """
    try:
        logger.info(f"Fetching tareas kanban for user {current_user_id}")

        # Create filters object
        filters = TareaFilters(
            proyecto_id=proyecto_id,
            asignado_a=asignado_a,
            empresa_id=empresa_id,
            estado=estado,
            prioridad=prioridad,
            urgencia=urgencia,
            vencidas=vencidas,
            search=search
        )

        return await tarea_service.get_tareas_kanban(filters)
    except Exception as e:
        logger.error(f"Error fetching tareas kanban: {str(e)}")
        raise

@router.get("/{tarea_id}", response_model=TareaResponse)
async def get_tarea(
    tarea_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a specific tarea by ID.
    """
    try:
        logger.info(f"Fetching tarea {tarea_id} for user {current_user_id}")
        tarea = await tarea_service.get_tarea_by_id(tarea_id)
        return TareaResponse(**tarea.model_dump())
    except Exception as e:
        logger.error(f"Error fetching tarea {tarea_id}: {str(e)}")
        raise

@router.put("/{tarea_id}", response_model=TareaResponse)
async def update_tarea(
    tarea_id: UUID,
    tarea_update: TareaUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update a tarea.
    """
    try:
        logger.info(f"Updating tarea {tarea_id} by user {current_user_id}")
        tarea = await tarea_service.update_tarea(tarea_id, tarea_update)
        return TareaResponse(**tarea.model_dump())
    except Exception as e:
        logger.error(f"Error updating tarea {tarea_id}: {str(e)}")
        raise

@router.delete("/{tarea_id}")
async def delete_tarea(
    tarea_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a tarea.
    """
    try:
        logger.info(f"Deleting tarea {tarea_id} by user {current_user_id}")
        return await tarea_service.delete_tarea(tarea_id)
    except Exception as e:
        logger.error(f"Error deleting tarea {tarea_id}: {str(e)}")
        raise

# Bulk operations
@router.patch("/bulk/estado")
async def bulk_update_estado(
    tarea_ids: List[UUID],
    nuevo_estado: EstadoTarea,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Bulk update estado for multiple tareas.
    """
    try:
        logger.info(f"Bulk updating estado for {len(tarea_ids)} tareas by user {current_user_id}")
        
        results = []
        for tarea_id in tarea_ids:
            try:
                tarea_update = TareaUpdate(estado=nuevo_estado)
                tarea = await tarea_service.update_tarea(tarea_id, tarea_update)
                results.append({"tarea_id": tarea_id, "success": True})
            except Exception as e:
                results.append({"tarea_id": tarea_id, "success": False, "error": str(e)})
        
        return {"results": results}
    except Exception as e:
        logger.error(f"Error in bulk update: {str(e)}")
        raise

# Enums endpoints
@router.get("/estados/available")
async def get_available_estados(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available estados for tareas.
    """
    return {
        "estados": [estado.value for estado in EstadoTarea]
    }

@router.get("/prioridades/available")
async def get_available_prioridades(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available prioridades for tareas.
    """
    return {
        "prioridades": [prioridad.value for prioridad in PrioridadTarea]
    }

@router.get("/urgencias/available")
async def get_available_urgencias(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available urgencias for tareas.
    """
    return {
        "urgencias": [urgencia.value for urgencia in UrgenciaTarea]
    }

# Statistics endpoints
@router.get("/stats/summary")
async def get_tareas_stats(
    proyecto_id: Optional[UUID] = Query(None, description="Filter by proyecto"),
    asignado_a: Optional[UUID] = Query(None, description="Filter by asignado"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get summary statistics for tareas.
    """
    try:
        logger.info(f"Fetching tareas stats for user {current_user_id}")
        
        # Get kanban data which includes counts
        filters = TareaFilters(
            proyecto_id=proyecto_id,
            asignado_a=asignado_a
        )
        kanban_data = await tarea_service.get_tareas_kanban(filters)
        
        # Calculate statistics
        stats = {
            "total_tareas": kanban_data.total_tareas,
            "por_estado": {}
        }
        
        for columna in kanban_data.columnas:
            stats["por_estado"][columna.estado.value] = columna.total
        
        return stats
    except Exception as e:
        logger.error(f"Error fetching tareas stats: {str(e)}")
        raise

@router.get("/proyectos/available")
async def get_available_proyectos(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available proyectos for filtering tareas.
    """
    try:
        supabase = await get_supabase_client()
        result = supabase.table('proyectos').select('id, nombre').order('nombre').execute()
        return [{"id": p["id"], "nombre": p["nombre"]} for p in result.data or []]
    except Exception as e:
        logger.error(f"Error fetching proyectos: {str(e)}")
        return []

@router.get("/empresas/available")
async def get_available_empresas(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get available empresas for filtering tareas.
    """
    try:
        supabase = await get_supabase_client()
        result = supabase.table('empresas').select('id, nombre').order('nombre').execute()
        return [{"id": e["id"], "nombre": e["nombre"]} for e in result.data or []]
    except Exception as e:
        logger.error(f"Error fetching empresas: {str(e)}")
        return []
