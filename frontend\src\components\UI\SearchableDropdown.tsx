import React, { useState, useEffect, useRef } from 'react';

export interface SearchableDropdownOption {
  value: string;
  label: string;
  subLabel?: string;
}

interface SearchableDropdownProps {
  options: SearchableDropdownOption[];
  value: string | null;
  onChange: (selectedValue: string | null) => void;
  placeholder?: string;
  onAddNew?: () => void;
  addNewLabel?: string;
  isLoading?: boolean;
  disabled?: boolean;
  fetchOptions?: (searchTerm: string) => Promise<SearchableDropdownOption[]>; // For async fetching
}

const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  options: initialOptions,
  value,
  onChange,
  placeholder = "Seleccione...",
  onAddNew,
  addNewLabel = "+ Añadir Nuevo",
  isLoading = false,
  disabled = false,
  fetchOptions,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentOptions, setCurrentOptions] = useState<SearchableDropdownOption[]>(initialOptions);
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const selectedOption = currentOptions.find(opt => opt.value === value);

  useEffect(() => {
    setCurrentOptions(initialOptions);
  }, [initialOptions]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [wrapperRef]);

  const handleInputChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = event.target.value;
    setSearchTerm(newSearchTerm);
    setIsOpen(true);
    setHighlightedIndex(-1); // Reset highlight when typing

    if (fetchOptions) {
      // setCurrentOptions([]); // Optionally clear options while fetching
      // setIsLoading(true); // Handled by prop
      try {
        const fetched = await fetchOptions(newSearchTerm);
        setCurrentOptions(fetched);
      } catch (error) {
        console.error("Error fetching options:", error);
        setCurrentOptions([]); // Or handle error appropriately
      } finally {
        // setIsLoading(false); // Handled by prop
      }
    } else {
      setCurrentOptions(
        initialOptions.filter(option =>
          option.label.toLowerCase().includes(newSearchTerm.toLowerCase()) ||
          option.subLabel?.toLowerCase().includes(newSearchTerm.toLowerCase())
        )
      );
    }
  };

  const handleOptionClick = (option: SearchableDropdownOption) => {
    onChange(option.value);
    setSearchTerm(option.label); // Show selected label in input
    setIsOpen(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) setIsOpen(true);
        setHighlightedIndex(prev => (prev + 1) % (currentOptions.length + (onAddNew ? 1 : 0)));
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (!isOpen) setIsOpen(true);
        setHighlightedIndex(prev => (prev - 1 + (currentOptions.length + (onAddNew ? 1 : 0))) % (currentOptions.length + (onAddNew ? 1 : 0)));
        break;
      case 'Enter':
        event.preventDefault();
        if (isOpen && highlightedIndex >= 0) {
          if (onAddNew && highlightedIndex === currentOptions.length) {
            onAddNew();
            setIsOpen(false);
          } else if (currentOptions[highlightedIndex]) {
            handleOptionClick(currentOptions[highlightedIndex]);
          }
        }
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'Tab':
        setIsOpen(false); // Close on tab out
        break;
      default:
        if (!isOpen) setIsOpen(true);
        break;
    }
  };
  
  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent dropdown from opening if it was closed
    onChange(null);
    setSearchTerm('');
    setCurrentOptions(initialOptions); // Reset to initial options or fetch if async
    inputRef.current?.focus();
  };

  useEffect(() => {
    if (!isOpen) {
      setHighlightedIndex(-1);
      // If a value is selected, show its label, otherwise show current search term or empty
      setSearchTerm(selectedOption ? selectedOption.label : '');
    }
  }, [isOpen, selectedOption]);


  return (
    <div className="relative w-full" ref={wrapperRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={() => { if (!disabled) setIsOpen(true); }}
          onKeyDown={handleKeyDown}
          placeholder={selectedOption ? selectedOption.label : placeholder}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
          disabled={disabled || isLoading}
        />
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-8 flex items-center pointer-events-none">
            <svg className="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        )}
        {value && !disabled && !isLoading && (
          <button
            type="button"
            onClick={clearSelection}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            aria-label="Clear selection"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {isOpen && !disabled && (
        <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-96 overflow-y-auto"> {/* Increased max-h-60 to max-h-96 */}
          {currentOptions.length > 0 ? (
            currentOptions.map((option, index) => (
              <li
                key={option.value}
                onClick={() => handleOptionClick(option)}
                onMouseEnter={() => setHighlightedIndex(index)}
                className={`px-3 py-2 cursor-pointer hover:bg-indigo-50 ${highlightedIndex === index ? 'bg-indigo-100' : ''}`}
              >
                <div className="font-medium">{option.label}</div>
                {option.subLabel && <div className="text-xs text-gray-500">{option.subLabel}</div>}
              </li>
            ))
          ) : (
            !isLoading && <li className="px-3 py-2 text-gray-500">No hay opciones</li>
          )}
          {onAddNew && (
            <li
              onClick={() => { onAddNew(); setIsOpen(false); }}
              onMouseEnter={() => setHighlightedIndex(currentOptions.length)}
              className={`px-3 py-2 cursor-pointer text-indigo-600 hover:bg-indigo-50 border-t border-gray-200 ${highlightedIndex === currentOptions.length ? 'bg-indigo-100' : ''}`}
            >
              {addNewLabel}
            </li>
          )}
        </ul>
      )}
    </div>
  );
};

export default SearchableDropdown;