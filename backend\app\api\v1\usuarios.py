from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List
from uuid import UUID

from app.models.user import User as UserResponse
from app.services import usuario_service
from app.core.security import get_current_user_id

router = APIRouter()

@router.get("", response_model=List[UserResponse])
async def list_usuarios(
    search: str | None = Query(None, alias="search"),
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve a list of usuarios, with optional search.
    """
    try:
        usuarios_data = await usuario_service.get_usuarios(
            search=search, skip=skip, limit=limit
        )
        return usuarios_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve usuarios: {str(e)}"
        )

@router.get("/{usuario_id}", response_model=UserResponse)
async def get_usuario_by_id(
    usuario_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve a specific usuario by ID.
    """
    try:
        usuario_data = await usuario_service.get_usuario_by_id(usuario_id)
        if not usuario_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Usuario not found"
            )
        return usuario_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve usuario: {str(e)}"
        )
