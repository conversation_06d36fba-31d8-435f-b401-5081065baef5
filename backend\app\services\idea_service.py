"""
Service layer for Ideas functionality
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from fastapi import HTTPException, status

from app.core.database import get_supabase_client
from app.models.idea import (
    Idea, IdeaCreate, IdeaUpdate, IdeasListResponse, 
    IdeaFilters, EmpresaIdeasDetails, EstadoIdea, PrioridadIdea
)

logger = logging.getLogger(__name__)

async def get_ideas(filters: IdeaFilters) -> IdeasListResponse:
    """
    Get ideas with filtering and pagination
    """
    supabase = await get_supabase_client()
    
    try:
        # Build query
        query = supabase.table('ideas').select('''
            id,
            titulo,
            descripcion,
            empresa_relacionada_id,
            proyecto_relacionado_id,
            estado,
            prioridad,
            created_at,
            updated_at,
            empresa_relacionada:empresas!empresa_relacionada_id(id, nombre),
            proyecto_relacionado:proyectos!proyecto_relacionado_id(id, nombre)
        ''')
        
        # Apply filters
        if filters.empresa_id:
            query = query.eq('empresa_relacionada_id', str(filters.empresa_id))
        
        if filters.proyecto_id:
            query = query.eq('proyecto_relacionado_id', str(filters.proyecto_id))
            
        if filters.estado:
            query = query.eq('estado', filters.estado.value)
            
        if filters.prioridad:
            query = query.eq('prioridad', filters.prioridad.value)
            
        if filters.search:
            # Search in titulo and descripcion (properly escaped)
            escaped_search = filters.search.replace('%', '\\%').replace('_', '\\_')
            search_term = f"%{escaped_search}%"
            query = query.or_(f"titulo.ilike.{search_term},descripcion.ilike.{search_term}")
        
        # Get total count before pagination
        count_response = query.execute()
        total_count = len(count_response.data) if count_response.data else 0

        # Apply pagination
        if filters.offset:
            query = query.range(filters.offset, filters.offset + filters.limit - 1)
        else:
            query = query.range(0, filters.limit - 1)

        # Order by created_at desc by default
        query = query.order('created_at', desc=True)

        # Execute query
        response = query.execute()
        
        if not response.data:
            return IdeasListResponse(ideas=[], total=0, filtered_count=0)
        
        # Transform data
        ideas = []
        for row in response.data:
            idea_data = {
                'id': row['id'],
                'titulo': row['titulo'],
                'descripcion': row['descripcion'],
                'empresa_relacionada_id': row['empresa_relacionada_id'],
                'proyecto_relacionado_id': row['proyecto_relacionado_id'],
                'estado': row['estado'],
                'prioridad': row['prioridad'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            }
            
            # Add related data if available
            if row.get('empresa_relacionada'):
                idea_data['empresa_relacionada'] = row['empresa_relacionada']
            
            if row.get('proyecto_relacionado'):
                idea_data['proyecto_relacionado'] = row['proyecto_relacionado']
            
            ideas.append(Idea(**idea_data))
        
        return IdeasListResponse(
            ideas=ideas,
            total=total_count,
            filtered_count=len(ideas)
        )
        
    except Exception as e:
        logger.error(f"Error getting ideas: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving ideas: {str(e)}"
        )

async def get_idea_by_id(idea_id: UUID) -> Idea:
    """
    Get a single idea by ID
    """
    supabase = await get_supabase_client()
    
    try:
        response = supabase.table('ideas').select('''
            id,
            titulo,
            descripcion,
            empresa_relacionada_id,
            proyecto_relacionado_id,
            estado,
            prioridad,
            created_at,
            updated_at,
            empresa_relacionada:empresas!empresa_relacionada_id(id, nombre),
            proyecto_relacionado:proyectos!proyecto_relacionado_id(id, nombre)
        ''').eq('id', str(idea_id)).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Idea with ID {idea_id} not found"
            )
        
        row = response.data[0]
        idea_data = {
            'id': row['id'],
            'titulo': row['titulo'],
            'descripcion': row['descripcion'],
            'empresa_relacionada_id': row['empresa_relacionada_id'],
            'proyecto_relacionado_id': row['proyecto_relacionado_id'],
            'estado': row['estado'],
            'prioridad': row['prioridad'],
            'created_at': row['created_at'],
            'updated_at': row['updated_at']
        }
        
        # Add related data if available
        if row.get('empresa_relacionada'):
            idea_data['empresa_relacionada'] = row['empresa_relacionada']
        
        if row.get('proyecto_relacionado'):
            idea_data['proyecto_relacionado'] = row['proyecto_relacionado']
        
        return Idea(**idea_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving idea: {str(e)}"
        )

async def create_idea(idea_data: IdeaCreate) -> Idea:
    """
    Create a new idea
    """
    supabase = await get_supabase_client()
    
    try:
        # Prepare data for insertion
        insert_data = {
            'titulo': idea_data.titulo,
            'descripcion': idea_data.descripcion,
            'estado': idea_data.estado.value,
            'prioridad': idea_data.prioridad.value
        }
        
        if idea_data.empresa_relacionada_id:
            insert_data['empresa_relacionada_id'] = str(idea_data.empresa_relacionada_id)
            
        if idea_data.proyecto_relacionado_id:
            insert_data['proyecto_relacionado_id'] = str(idea_data.proyecto_relacionado_id)
        
        # Insert idea
        response = supabase.table('ideas').insert(insert_data).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create idea"
            )
        
        # Return the created idea with full data
        created_idea_id = response.data[0]['id']
        return await get_idea_by_id(UUID(created_idea_id))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating idea: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating idea: {str(e)}"
        )

async def update_idea(idea_id: UUID, idea_data: IdeaUpdate) -> Idea:
    """
    Update an existing idea
    """
    supabase = await get_supabase_client()
    
    try:
        # Prepare update data
        update_data = {}
        
        if idea_data.titulo is not None:
            update_data['titulo'] = idea_data.titulo
            
        if idea_data.descripcion is not None:
            update_data['descripcion'] = idea_data.descripcion
            
        if idea_data.empresa_relacionada_id is not None:
            update_data['empresa_relacionada_id'] = str(idea_data.empresa_relacionada_id) if idea_data.empresa_relacionada_id else None
            
        if idea_data.proyecto_relacionado_id is not None:
            update_data['proyecto_relacionado_id'] = str(idea_data.proyecto_relacionado_id) if idea_data.proyecto_relacionado_id else None
            
        if idea_data.estado is not None:
            update_data['estado'] = idea_data.estado.value
            
        if idea_data.prioridad is not None:
            update_data['prioridad'] = idea_data.prioridad.value
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields to update"
            )
        
        # Update idea
        response = supabase.table('ideas').update(update_data).eq('id', str(idea_id)).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Idea with ID {idea_id} not found"
            )
        
        # Return updated idea with full data
        return await get_idea_by_id(idea_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating idea: {str(e)}"
        )

async def delete_idea(idea_id: UUID) -> None:
    """
    Delete an idea
    """
    supabase = await get_supabase_client()
    
    try:
        response = supabase.table('ideas').delete().eq('id', str(idea_id)).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Idea with ID {idea_id} not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting idea: {str(e)}"
        )

async def get_empresa_ideas_details(empresa_id: UUID) -> EmpresaIdeasDetails:
    """
    Get ideas details for a specific empresa (for company subtab)
    """
    supabase = await get_supabase_client()
    
    try:
        # Get all ideas for the empresa
        response = supabase.table('ideas').select('''
            id,
            titulo,
            descripcion,
            empresa_relacionada_id,
            proyecto_relacionado_id,
            estado,
            prioridad,
            created_at,
            updated_at,
            proyecto_relacionado:proyectos!proyecto_relacionado_id(id, nombre)
        ''').eq('empresa_relacionada_id', str(empresa_id)).order('created_at', desc=True).execute()
        
        ideas_data = response.data or []
        
        # Transform data
        ideas = []
        for row in ideas_data:
            idea_data = {
                'id': row['id'],
                'titulo': row['titulo'],
                'descripcion': row['descripcion'],
                'empresa_relacionada_id': row['empresa_relacionada_id'],
                'proyecto_relacionado_id': row['proyecto_relacionado_id'],
                'estado': row['estado'],
                'prioridad': row['prioridad'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            }
            
            if row.get('proyecto_relacionado'):
                idea_data['proyecto_relacionado'] = row['proyecto_relacionado']
            
            ideas.append(Idea(**idea_data))
        
        # Calculate statistics
        total_ideas = len(ideas)
        
        ideas_por_estado = {}
        ideas_por_prioridad = {}
        
        for idea in ideas:
            # Count by estado
            estado = idea.estado
            ideas_por_estado[estado] = ideas_por_estado.get(estado, 0) + 1
            
            # Count by prioridad
            prioridad = idea.prioridad
            ideas_por_prioridad[prioridad] = ideas_por_prioridad.get(prioridad, 0) + 1
        
        return EmpresaIdeasDetails(
            ideas=ideas,
            total_ideas=total_ideas,
            ideas_por_estado=ideas_por_estado,
            ideas_por_prioridad=ideas_por_prioridad
        )
        
    except Exception as e:
        logger.error(f"Error getting empresa ideas details for {empresa_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving empresa ideas details: {str(e)}"
        )
