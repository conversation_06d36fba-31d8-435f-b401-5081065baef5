import { useRef, useCallback, useEffect, useMemo } from 'react'; // Added useMemo

export type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'error' | 'polling';

interface ConnectionStateMachineOptions {
  maxRetries: number;
  retryDelayMs: number;
  onStateChange: (state: ConnectionState) => void;
  onMaxRetriesExceeded: () => void;
}

export function useConnectionStateMachine(options: ConnectionStateMachineOptions) {
  const stateRef = useRef<ConnectionState>('disconnected');
  const retriesRef = useRef(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSuccessTimestampRef = useRef<number | null>(null); // Added
  const GRACE_PERIOD_MS = 2000; // Added: 2 seconds grace period

  const transitionTo = useCallback((newState: ConnectionState) => {
    console.log(`[ConnectionStateMachine] ${stateRef.current} -> ${newState}`);
    stateRef.current = newState;
    options.onStateChange(newState);
  }, [options]);
  
  const clearRetryTimeout = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);
  
  const connect = useCallback(() => {
    if (stateRef.current === 'connecting' || stateRef.current === 'connected') {
      console.log('[ConnectionStateMachine] connect: Already connecting/connected, ignoring full connect logic.');
      return;
    }
    // This is a fresh connection attempt (state was disconnected, error, or polling)
    console.log('[ConnectionStateMachine] connect: Initiating fresh connection sequence.');
    clearRetryTimeout(); // Ensure no old retry timeouts are pending
    retriesRef.current = 0; // Reset retries for a fresh attempt
    transitionTo('connecting');
  }, [transitionTo, clearRetryTimeout]); // Added clearRetryTimeout
  
  const handleSuccess = useCallback(() => {
    clearRetryTimeout();
    retriesRef.current = 0;
    lastSuccessTimestampRef.current = Date.now(); // Record success time
    transitionTo('connected');
  }, [transitionTo, clearRetryTimeout]);
  
  const handleError = useCallback((error?: Error) => {
    console.error('[ConnectionStateMachine] Connection error:', error);

    if (lastSuccessTimestampRef.current && (Date.now() - lastSuccessTimestampRef.current < GRACE_PERIOD_MS)) {
      // Error occurred very shortly after a success. This indicates an unstable connection.
      // Force polling immediately to break any rapid retry loops.
      console.warn(`[ConnectionStateMachine] Error occurred within ${GRACE_PERIOD_MS}ms of a successful connection. Connection is unstable. Forcing polling.`);
      clearRetryTimeout();
      transitionTo('error'); // Go to error state
      options.onMaxRetriesExceeded(); // Trigger polling
      lastSuccessTimestampRef.current = null; // Reset timestamp, this "success" was fleeting.
      retriesRef.current = 0; // Reset retries as we are aborting this attempt sequence.
      return;
    }
    
    // If not in the grace period, or if the grace period passed, reset the success timestamp
    // as we are now in a normal error/retry flow.
    lastSuccessTimestampRef.current = null;

    if (retriesRef.current >= options.maxRetries) {
      clearRetryTimeout();
      transitionTo('error');
      options.onMaxRetriesExceeded();
      return;
    }
    
    retriesRef.current++;
    const delay = Math.min(
      options.retryDelayMs * Math.pow(2, retriesRef.current - 1),
      30000 // Max 30 seconds
    );
    
    console.log(`[ConnectionStateMachine] Retrying in ${delay}ms (attempt ${retriesRef.current}/${options.maxRetries})`);
    
    retryTimeoutRef.current = setTimeout(() => {
      transitionTo('connecting');
    }, delay);
  }, [transitionTo, clearRetryTimeout, options]);
  
  const disconnect = useCallback(() => {
    clearRetryTimeout();
    retriesRef.current = 0;
    transitionTo('disconnected');
  }, [transitionTo, clearRetryTimeout]);
  
  const startPolling = useCallback(() => {
    clearRetryTimeout();
    transitionTo('polling');
  }, [transitionTo, clearRetryTimeout]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearRetryTimeout();
    };
  }, [clearRetryTimeout]);
  
  // Memoize the returned object so its reference is stable
  return useMemo(() => ({
    // state: stateRef.current, // stateRef.current will give the value at the time of memoization, not reactive.
                               // Instead, the component using this hook should get state via onStateChange callback.
                               // Or, we can make the hook return a getter for the state.
                               // For now, let's assume the parent gets state via onStateChange.
                               // A better approach might be to return stateRef itself or a getter.
                               // Let's return stateRef.current for now, but acknowledge this might not be reactive if not handled carefully by consumer.
                               // The consumer (useRealtimeMeeting) uses connectionStateMachine.state in a useEffect dependency.
                               // This means it needs the actual reactive state, not a memoized stale one.
    
    // The functions (connect, handleSuccess, etc.) are stable due to useCallback.
    // The issue is the object wrapper itself.
    // The `state` property in the returned object needs to be reactive.
    // The `onStateChange` callback handles reactivity for the parent.
    // The `connectionStateMachine.state` used in `useRealtimeMeeting`'s `useEffect` dependency
    // will be the `state` from `useConnectionStateMachine`'s closure when `useRealtimeMeeting` renders.
    // This is complex. Let's simplify: the functions are stable. The object needs to be stable.
    // The `state` value itself is managed by `stateRef` and `transitionTo` calls `options.onStateChange`.
    // The `useRealtimeMeeting` hook uses `connectionStateMachine.state` in a `useEffect` dependency.
    // This means it needs the *actual current state value*, not a memoized one.
    // The functions are stable due to useCallback. The object wrapper is the issue.

    // Let's return the functions, and the parent can manage its own state based on onStateChange.
    // This is what `useRealtimeMeeting` already does with `onConnectionStatusChange`.
    // The problem is `connectionStateMachine` itself being a dependency.

    // The functions are stable. The object reference needs to be stable.
    // The `state` property in the returned object is the tricky part if the object is memoized.
    // `stateRef.current` inside useMemo would capture the value at memoization time.

    // Let's return the functions and the parent can get the state via the callback.
    // The `connectionStateMachine` dependency in `useRealtimeMeeting`'s main useEffect
    // is problematic if the object reference changes.
    // The functions inside are stable.
    // The `state` property of the returned object is the issue for memoization.

    // The functions are stable. The object reference is not.
    // If we memoize the object, `stateRef.current` would be stale if included directly.
    // The `useRealtimeMeeting` hook uses `connectionStateMachine.state` in its second useEffect.
    // This state needs to be the *actual current state*.

    // The functions are stable. The object reference is the problem.
    // Let's memoize the object containing the stable functions.
    // The `state` property will be an issue if the consumer expects it to be reactive from the object.
    // However, `useRealtimeMeeting` gets the reactive state via `onConnectionStatusChange`.
    // The dependency on `connectionStateMachine` in `useRealtimeMeeting`'s main `useEffect`
    // is likely just to ensure the effect re-runs if the hook itself were to be re-instantiated
    // with different options, which is not the case here.
    // The functions within `connectionStateMachine` are stable.

    // The simplest fix for the object reference is to memoize the returned object.
    // The `state` property in the returned object is not used by `useRealtimeMeeting`'s main `useEffect`.
    // The second `useEffect` in `useRealtimeMeeting` uses `connectionStateMachine.state`.
    // This `state` comes from the `useConnectionStateMachine`'s closure at the time `useRealtimeMeeting` renders.
    // This is fine. The functions are stable. The object wrapper needs to be stable.

    connect,
    handleSuccess,
    handleError,
    disconnect,
    startPolling,
    // Expose a getter for the current state if needed, or rely on onStateChange
    // For the dependency array in useRealtimeMeeting, the functions being stable is key.
    // The object itself needs to be stable.
  }), [connect, handleSuccess, handleError, disconnect, startPolling]);
}