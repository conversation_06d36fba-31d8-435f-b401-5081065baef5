"""
Database Operations Tool

This tool allows authorized services (like n8n) to execute database operations
(INSERT, UPDATE, DELETE) using structured JSON input and an API key for authentication.
It uses a read-write database pool with parameterized queries for security.

Features:
- API key authentication
- Read-write database pool usage
- Structured JSON input validation
- SQL injection prevention through parameterized queries
- Identifier validation (table names, column names)
- Mandatory WHERE clauses for UPDATE/DELETE
- RETURNING clause support
- Comprehensive error handling and logging
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body

from ....core.security import verify_api_key
from ....services.db_operations_service import db_operations_service
from ....models.tool import (
    DbOpRequest,
    DbOpResponse,
    ErrorDetail
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post(
    "/execute_db_op",
    response_model=DbOpResponse,
    summary="Execute Database Operation via API Key",
    description="Allows authorized services (like n8n) to execute INSERT, UPDATE, DELETE operations using structured JSON input and an API key for authentication.",
    dependencies=[Depends(verify_api_key)], # Apply API key authentication
    responses={
        400: {"model": ErrorDetail, "description": "Invalid operation data or missing WHERE clause"},
        401: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        403: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        500: {"model": ErrorDetail, "description": "Database Execution Error or Unexpected Error"}
    }
)
async def execute_db_op_endpoint(
    request_body: DbOpRequest = Body(...),
):
    """
    Endpoint to execute database operations (INSERT, UPDATE, DELETE) with structured JSON input.
    Requires `X-API-Key` header for authentication.
    Uses read-write database pool with parameterized queries for security.
    
    Supported Operations:
    - INSERT: Add new records to a table
    - UPDATE: Modify existing records (WHERE clause required)
    - DELETE: Remove records (WHERE clause required)
    
    Security Features:
    - Parameterized queries prevent SQL injection
    - Identifier validation for table and column names
    - Mandatory WHERE clauses for UPDATE/DELETE operations
    - Read-write database pool with proper connection management
    
    Args:
        request_body: Database operation request (discriminated union based on 'operation' field)
        
    Returns:
        DbOpResponse: Operation results including rows affected and optional returned data
        
    Raises:
        HTTPException 400: Invalid operation data or missing WHERE clause
        HTTPException 401/403: Missing or invalid API key
        HTTPException 500: Database execution error or unexpected error
    """
    logger.info(f"Received request to execute {request_body.operation.upper()} operation.")
    
    # Log operation details for debugging (avoid logging sensitive data)
    logger.info(f"DB OPERATION: {request_body.operation.upper()} on table '{request_body.table}'")
    
    try:
        result = await db_operations_service.execute_db_operation(request_body)
        
        logger.info(f"{request_body.operation.upper()} execution successful. Rows affected: {result.rows_affected}")
        return result

    except HTTPException as http_exc:
        # Log the known HTTP exception before re-raising
        logger.warning(f"HTTPException during {request_body.operation.upper()} execution: Status={http_exc.status_code}, Detail={http_exc.detail}")
        raise HTTPException(status_code=http_exc.status_code, detail=http_exc.detail)
    except Exception as e:
        # Catch and log any other unexpected errors during endpoint processing
        logger.error(f"Unexpected error in /execute_db_op endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )
