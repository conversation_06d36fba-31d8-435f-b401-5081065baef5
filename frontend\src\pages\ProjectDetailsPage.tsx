import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProjects } from '../hooks/useProjects';
import { useTasks } from '../hooks/useTasks';
import { useProjectsRealtime, useTasksRealtime } from '../hooks/useRealtime';
import { Proyecto, ProyectoUpdate } from '../types/proyecto';
import { Tarea, EstadoTarea } from '../types/tarea';
import { formatDate } from '../utils/projectUtils';
import ProjectHeader from '../components/Projects/ProjectHeader';
import ProjectInfoSection from '../components/Projects/ProjectInfoSection';
import ProjectTasksSection from '../components/Projects/ProjectTasksSection';
import {
  ArrowLeft,
  Building,
  TrendingUp,
  RefreshCw
} from 'lucide-react';

const ProjectDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Proyecto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tasksFilter, setTasksFilter] = useState('');
  const [tasksSearch, setTasksSearch] = useState('');
  const [groupBy, setGroupBy] = useState<'none' | 'estado' | 'prioridad' | 'urgencia'>('none');

  const { getProject, updateProjectField, deleteProject } = useProjects();
  const { 
    tasks, 
    loading: tasksLoading, 
    fetchTasks, 
    updateTaskField, 
    deleteTask,
    createTask 
  } = useTasks();

  // Setup realtime updates
  useProjectsRealtime(
    () => loadProject(),
    () => loadProject()
  );

  useTasksRealtime(
    () => loadTasks(),
    () => loadTasks(),
    id
  );

  const loadProject = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      const projectData = await getProject(id);
      setProject(projectData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading project');
    } finally {
      setLoading(false);
    }
  }, [id, getProject]);

  const loadTasks = useCallback(async () => {
    if (!id) return;

    // Usar fetchTasks con múltiples páginas para obtener todas las tareas
    // Esta es una solución temporal hasta que se mejore la API para soportar límites personalizados
    await fetchTasks(1, {
      proyecto_id: id,
      search: tasksSearch,
      estado: (tasksFilter as EstadoTarea) || undefined
    });

    // TODO: Implementar paginación completa para obtener todas las tareas
    // Por ahora obtenemos solo la primera página (20 tareas)
    // Se recomienda implementar paginación o virtual scrolling en el futuro
  }, [id, fetchTasks, tasksSearch, tasksFilter]);

  useEffect(() => {
    loadProject();
  }, [id, loadProject]);

  useEffect(() => {
    loadTasks();
  }, [id, tasksFilter, tasksSearch, loadTasks]);

  const handleUpdateProject = async (field: keyof Proyecto, value: string | number | boolean | null) => {
    if (!project) return;

    try {
      await updateProjectField(project.id, field as keyof ProyectoUpdate, value);
      await loadProject(); // Reload to get updated data
    } catch (error) {
      console.error('Error updating project:', error);
      throw error;
    }
  };

  const handleDeleteProject = async () => {
    if (!project) return;
    
    if (window.confirm(`¿Estás seguro de que quieres eliminar el proyecto "${project.nombre}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteProject(project.id);
        navigate('/proyectos');
      } catch (error) {
        console.error('Error deleting project:', error);
        alert('Error al eliminar el proyecto. Por favor, inténtalo de nuevo.');
      }
    }
  };

  const handleCreateTask = async () => {
    if (!project) return;
    
    const taskData = {
      titulo: 'Nueva tarea',
      descripcion: '',
      proyecto_id: project.id,
      estado: 'Pendiente' as const,
      prioridad: 'Media' as const,
      urgencia: 'No Urgente' as const
    };

    try {
      await createTask(taskData);
      await loadTasks();
    } catch (error) {
      console.error('Error creating task:', error);
      alert('Error al crear la tarea. Por favor, inténtalo de nuevo.');
    }
  };

  const groupTasks = (tasks: Tarea[]) => {
    if (groupBy === 'none') return { 'Todas las tareas': tasks };
    
    const grouped: Record<string, Tarea[]> = {};
    
    tasks.forEach(task => {
      let key = '';
      switch (groupBy) {
        case 'estado':
          key = task.estado;
          break;
        case 'prioridad':
          key = task.prioridad;
          break;
        case 'urgencia':
          key = task.urgencia;
          break;
      }
      
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(task);
    });
    
    return grouped;
  };

  // formatDate function is now imported from projectUtils

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center">
          <RefreshCw className="h-6 w-6 mr-3 animate-spin text-blue-600" />
          <span className="text-lg text-gray-600">Cargando proyecto...</span>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar el proyecto
          </h2>
          <p className="text-gray-600 mb-4">{error || 'Proyecto no encontrado'}</p>
          <button
            onClick={() => navigate('/proyectos')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Proyectos
          </button>
        </div>
      </div>
    );
  }

  const groupedTasks = groupTasks(tasks);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <ProjectHeader
          project={project}
          onUpdateProject={handleUpdateProject}
          onDeleteProject={handleDeleteProject}
        />

        {/* Main Content */}
        <div className="space-y-8">
          {/* Project Info */}
          <ProjectInfoSection
            project={project}
            onUpdateProject={handleUpdateProject}
          />

          {/* Tasks Section */}
          <ProjectTasksSection
            tasks={tasks}
            tasksLoading={tasksLoading}
            tasksSearch={tasksSearch}
            tasksFilter={tasksFilter}
            groupBy={groupBy}
            onTasksSearchChange={setTasksSearch}
            onTasksFilterChange={setTasksFilter}
            onGroupByChange={setGroupBy}
            onCreateTask={handleCreateTask}
            onUpdateTaskField={updateTaskField}
            onDeleteTask={deleteTask}
            groupedTasks={groupedTasks}
          />

          {/* Related Entities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Associated Companies */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Empresas Asociadas
              </h3>
              {project.empresas_asociadas && project.empresas_asociadas.length > 0 ? (
                <div className="space-y-2">
                  {project.empresas_asociadas.map((empresa) => (
                    <div key={empresa.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{empresa.nombre}</p>
                        <p className="text-sm text-gray-600">{empresa.tipo_relacion}</p>
                      </div>
                      <button
                        onClick={() => navigate(`/empresas/${empresa.id}`)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Ver detalles
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No hay empresas asociadas</p>
              )}
            </div>

            {/* Project Statistics */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Estadísticas
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total de tareas:</span>
                  <span className="font-medium">{project.total_tareas || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tareas completadas:</span>
                  <span className="font-medium">{project.tareas_completadas || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Progreso:</span>
                  <span className="font-medium">{project.progreso}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Responsable:</span>
                  <span className="font-medium">
                    {project.responsable_usuario?.nombre || 'Sin asignar'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Fecha de creación:</span>
                  <span className="font-medium">{formatDate(project.created_at)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Última actualización:</span>
                  <span className="font-medium">{formatDate(project.updated_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailsPage;
