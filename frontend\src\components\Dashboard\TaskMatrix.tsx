import React from 'react';
import { TareaMatrix, TareaSummary, MATRIX_QUADRANTS } from '../../types/tarea';
import { Clock, AlertTriangle, User, Calendar } from 'lucide-react';

interface TaskMatrixProps {
  matrix: TareaMatrix;
  onTaskClick?: (task: TareaSummary) => void;
  loading?: boolean;
}

const TaskCard: React.FC<{
  task: TareaSummary;
  onClick?: () => void;
}> = ({ task, onClick }) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  };

  const getDaysText = (days?: number) => {
    if (days === undefined || days === null) return null;
    if (days < 0) return `${Math.abs(days)}d vencida`;
    if (days === 0) return 'Vence hoy';
    if (days === 1) return 'Vence mañana';
    return `${days}d restantes`;
  };

  return (
    <div
      className={`
        p-3 bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer
        ${task.es_vencida ? 'border-red-200 bg-red-50' : 'border-gray-200'}
      `}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 flex-1">
          {task.titulo}
        </h4>
        {task.es_vencida && (
          <AlertTriangle className="h-4 w-4 text-red-500 ml-2 flex-shrink-0" />
        )}
      </div>
      
      <div className="space-y-1">
        {task.proyecto_nombre && (
          <div className="flex items-center text-xs text-gray-600">
            <span className="truncate">{task.proyecto_nombre}</span>
          </div>
        )}
        
        {task.asignado_nombre && (
          <div className="flex items-center text-xs text-gray-600">
            <User className="h-3 w-3 mr-1 flex-shrink-0" />
            <span className="truncate">{task.asignado_nombre}</span>
          </div>
        )}
        
        {task.fecha_vencimiento && (
          <div className={`flex items-center text-xs ${
            task.es_vencida ? 'text-red-600' : 'text-gray-600'
          }`}>
            <Calendar className="h-3 w-3 mr-1 flex-shrink-0" />
            <span>{formatDate(task.fecha_vencimiento)}</span>
            {task.dias_vencimiento !== undefined && (
              <span className="ml-1">({getDaysText(task.dias_vencimiento)})</span>
            )}
          </div>
        )}
      </div>
      
      <div className="flex items-center justify-between mt-2">
        <span className={`
          inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
          ${task.prioridad === 'Urgente' ? 'bg-red-100 text-red-800' :
            task.prioridad === 'Alta' ? 'bg-orange-100 text-orange-800' :
            task.prioridad === 'Media' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'}
        `}>
          {task.prioridad}
        </span>
        
        <span className={`
          inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
          ${task.urgencia === 'Urgente' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}
        `}>
          {task.urgencia}
        </span>
      </div>
    </div>
  );
};

const MatrixQuadrant: React.FC<{
  title: string;
  subtitle: string;
  tasks: TareaSummary[];
  color: string;
  headerColor: string;
  onTaskClick?: (task: TareaSummary) => void;
}> = ({ title, subtitle, tasks, color, headerColor, onTaskClick }) => {
  return (
    <div className={`${color} rounded-lg border p-4 h-full`}>
      <div className={`${headerColor} rounded-md px-3 py-2 mb-4`}>
        <h3 className="font-semibold text-sm">{title}</h3>
        <p className="text-xs opacity-75">{subtitle}</p>
        <span className="text-xs font-medium">{tasks.length} tarea(s)</span>
      </div>
      
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No hay tareas en este cuadrante</p>
          </div>
        ) : (
          tasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onClick={() => onTaskClick?.(task)}
            />
          ))
        )}
      </div>
    </div>
  );
};

const TaskMatrix: React.FC<TaskMatrixProps> = ({ 
  matrix, 
  onTaskClick, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-gray-100 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <MatrixQuadrant
        title={MATRIX_QUADRANTS.urgente_importante.title}
        subtitle={MATRIX_QUADRANTS.urgente_importante.subtitle}
        tasks={matrix.urgente_importante}
        color={MATRIX_QUADRANTS.urgente_importante.color}
        headerColor={MATRIX_QUADRANTS.urgente_importante.headerColor}
        onTaskClick={onTaskClick}
      />
      
      <MatrixQuadrant
        title={MATRIX_QUADRANTS.no_urgente_importante.title}
        subtitle={MATRIX_QUADRANTS.no_urgente_importante.subtitle}
        tasks={matrix.no_urgente_importante}
        color={MATRIX_QUADRANTS.no_urgente_importante.color}
        headerColor={MATRIX_QUADRANTS.no_urgente_importante.headerColor}
        onTaskClick={onTaskClick}
      />
      
      <MatrixQuadrant
        title={MATRIX_QUADRANTS.urgente_no_importante.title}
        subtitle={MATRIX_QUADRANTS.urgente_no_importante.subtitle}
        tasks={matrix.urgente_no_importante}
        color={MATRIX_QUADRANTS.urgente_no_importante.color}
        headerColor={MATRIX_QUADRANTS.urgente_no_importante.headerColor}
        onTaskClick={onTaskClick}
      />
      
      <MatrixQuadrant
        title={MATRIX_QUADRANTS.no_urgente_no_importante.title}
        subtitle={MATRIX_QUADRANTS.no_urgente_no_importante.subtitle}
        tasks={matrix.no_urgente_no_importante}
        color={MATRIX_QUADRANTS.no_urgente_no_importante.color}
        headerColor={MATRIX_QUADRANTS.no_urgente_no_importante.headerColor}
        onTaskClick={onTaskClick}
      />
    </div>
  );
};

export default TaskMatrix;
