import React from 'react';
import { ProcesoSummary } from '../../types/proceso';
import { Clock, AlertTriangle, Building, User, Workflow } from 'lucide-react';

interface ProcessListProps {
  processes: ProcesoSummary[];
  onProcessClick?: (process: ProcesoSummary) => void;
  loading?: boolean;
}

const ProcessItem: React.FC<{
  process: ProcesoSummary;
  onClick?: () => void;
}> = ({ process, onClick }) => {
  // Estado removed - procesos table doesn't have estado column
  const getCuelloBotellaColor = (esCuelloBotella: boolean) => {
    return esCuelloBotella
      ? 'bg-red-100 text-red-800'
      : 'bg-green-100 text-green-800';
  };

  const getTipoColor = (tipo: string) => {
    return tipo === 'Interno' 
      ? 'bg-blue-100 text-blue-800' 
      : 'bg-green-100 text-green-800';
  };

  const formatTime = (minutes?: number) => {
    if (!minutes) return null;
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  };

  return (
    <div
      className={`
        bg-white rounded-lg border p-4 hover:shadow-md transition-all duration-200 cursor-pointer
        ${process.es_cuello_botella ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'}
      `}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <h3 className="text-sm font-semibold text-gray-900 mr-2 line-clamp-1">
              {process.nombre}
            </h3>
            {process.es_cuello_botella && (
              <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
            )}
          </div>
          
          <div className="flex items-center space-x-2 mb-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTipoColor(process.tipo_proceso)}`}>
              {process.tipo_proceso}
            </span>
            {process.es_cuello_botella && (
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCuelloBotellaColor(process.es_cuello_botella)}`}>
                Cuello de Botella
              </span>
            )}
          </div>
        </div>
        
        <Workflow className="h-5 w-5 text-gray-400 ml-2 flex-shrink-0" />
      </div>

      <div className="space-y-2">
        {process.empresa_nombre && (
          <div className="flex items-center text-xs text-gray-600">
            <Building className="h-3 w-3 mr-2 flex-shrink-0" />
            <span className="truncate">{process.empresa_nombre}</span>
          </div>
        )}
        
        {process.responsable_nombre && (
          <div className="flex items-center text-xs text-gray-600">
            <User className="h-3 w-3 mr-2 flex-shrink-0" />
            <span className="truncate">{process.responsable_nombre}</span>
          </div>
        )}
        
        {process.tiempo_estimado_manual && (
          <div className="flex items-center text-xs text-gray-600">
            <Clock className="h-3 w-3 mr-2 flex-shrink-0" />
            <span>{formatTime(process.tiempo_estimado_manual)}</span>
          </div>
        )}
      </div>

      {process.es_cuello_botella && (
        <div className="mt-3 p-2 bg-red-100 rounded-md">
          <div className="flex items-center">
            <AlertTriangle className="h-3 w-3 text-red-600 mr-1" />
            <span className="text-xs font-medium text-red-800">
              Cuello de botella
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

const ProcessList: React.FC<ProcessListProps> = ({ 
  processes, 
  onProcessClick, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="bg-gray-100 rounded-lg h-24 animate-pulse" />
        ))}
      </div>
    );
  }

  if (processes.length === 0) {
    return (
      <div className="text-center py-8">
        <Workflow className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay procesos en ejecución
        </h3>
        <p className="text-gray-600">
          Los procesos activos aparecerán aquí cuando estén en progreso.
        </p>
      </div>
    );
  }

  // Separate bottleneck processes for priority display
  const bottleneckProcesses = processes.filter(p => p.es_cuello_botella);
  const regularProcesses = processes.filter(p => !p.es_cuello_botella);

  return (
    <div className="space-y-4">
      {bottleneckProcesses.length > 0 && (
        <div>
          <div className="flex items-center mb-3">
            <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
            <h4 className="text-sm font-medium text-red-800">
              Cuellos de botella ({bottleneckProcesses.length})
            </h4>
          </div>
          <div className="space-y-3">
            {bottleneckProcesses.map((process) => (
              <ProcessItem
                key={process.id}
                process={process}
                onClick={() => onProcessClick?.(process)}
              />
            ))}
          </div>
        </div>
      )}

      {regularProcesses.length > 0 && (
        <div>
          {bottleneckProcesses.length > 0 && (
            <div className="flex items-center mb-3 mt-6">
              <Workflow className="h-4 w-4 text-gray-500 mr-2" />
              <h4 className="text-sm font-medium text-gray-700">
                Otros procesos ({regularProcesses.length})
              </h4>
            </div>
          )}
          <div className="space-y-3">
            {regularProcesses.map((process) => (
              <ProcessItem
                key={process.id}
                process={process}
                onClick={() => onProcessClick?.(process)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessList;
