/**
 * Types and interfaces for Ideas functionality
 */

// Base idea interface matching database schema
export interface IdeaBase {
  titulo: string;
  descripcion?: string;
  empresa_relacionada_id?: string;
  proyecto_relacionado_id?: string;
  estado: EstadoIdea;
  prioridad: PrioridadIdea;
}

// Full idea interface with database fields
export interface Idea extends IdeaBase {
  id: string;
  created_at: string;
  updated_at: string;
  
  // Related data populated by API
  empresa_relacionada?: {
    id: string;
    nombre: string;
  };
  proyecto_relacionado?: {
    id: string;
    nombre: string;
  };
}

// Idea creation interface
export type IdeaCreate = IdeaBase;

// Idea update interface
export interface IdeaUpdate {
  titulo?: string;
  descripcion?: string;
  empresa_relacionada_id?: string;
  proyecto_relacionado_id?: string;
  estado?: EstadoIdea;
  prioridad?: PrioridadIdea;
}

// Estado enum values
export type EstadoIdea = 'pendiente' | 'implementada' | 'descartada';

// Prioridad enum values  
export type PrioridadIdea = 'Baja' | 'Media' | 'Alta' | 'Urgente';

// Constants for dropdowns and UI
export const ESTADOS_IDEA: EstadoIdea[] = ['pendiente', 'implementada', 'descartada'];
export const PRIORIDADES_IDEA: PrioridadIdea[] = ['Baja', 'Media', 'Alta', 'Urgente'];

// Estado colors for UI
export const ESTADO_IDEA_COLORS: Record<EstadoIdea, string> = {
  pendiente: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  implementada: 'bg-green-100 text-green-800 border-green-200',
  descartada: 'bg-gray-100 text-gray-800 border-gray-200',
};

// Prioridad colors for UI
export const PRIORIDAD_IDEA_COLORS: Record<PrioridadIdea, string> = {
  Baja: 'bg-blue-100 text-blue-800 border-blue-200',
  Media: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  Alta: 'bg-orange-100 text-orange-800 border-orange-200',
  Urgente: 'bg-red-100 text-red-800 border-red-200',
};

// Filter interfaces
export interface IdeasFilterState {
  estado?: EstadoIdea | 'all';
  prioridad?: PrioridadIdea | 'all';
  search?: string;
}

// Grouping options
export type IdeasGroupBy = 'estado' | 'prioridad' | 'none';

// API response interfaces
export interface IdeasListResponse {
  ideas: Idea[];
  total: number;
  filtered_count: number;
}

// Grouped ideas for UI display
export interface GroupedIdeas {
  [key: string]: Idea[];
}

// Sort interfaces
export type IdeasSortField = 'titulo' | 'estado' | 'prioridad' | 'created_at' | 'updated_at';
export type IdeasSortDirection = 'asc' | 'desc';

export interface IdeasSortState {
  field: IdeasSortField;
  direction: IdeasSortDirection;
}

// Component props interfaces
export interface PestanaIdeasEmpresaProps {
  empresaId: string;
}

export interface IdeasFiltersProps {
  filters: IdeasFilterState;
  onFiltersChange: (filters: IdeasFilterState) => void;
  onClearFilters: () => void;
}

export interface IdeasGroupingProps {
  groupBy: IdeasGroupBy;
  onGroupByChange: (groupBy: IdeasGroupBy) => void;
}

export interface IdeasListProps {
  ideas: Idea[];
  groupBy: IdeasGroupBy;
  onUpdateIdea: (id: string, updates: IdeaUpdate) => Promise<void>;
  loading?: boolean;
}

export interface IdeaItemProps {
  idea: Idea;
  onUpdateIdea: (id: string, updates: IdeaUpdate) => Promise<void>;
}

// Hook return interface
export interface UseIdeasReturn {
  // Data
  ideas: Idea[];
  loading: boolean;
  error: string | null;
  
  // Filters and grouping
  filters: IdeasFilterState;
  groupBy: IdeasGroupBy;
  sortState: IdeasSortState;
  
  // Actions
  setFilters: (filters: IdeasFilterState) => void;
  setGroupBy: (groupBy: IdeasGroupBy) => void;
  setSortState: (sort: IdeasSortState) => void;
  clearFilters: () => void;
  updateIdea: (id: string, updates: IdeaUpdate) => Promise<Idea>;
  createIdea: (idea: IdeaCreate) => Promise<Idea>;
  deleteIdea: (id: string) => Promise<void>;
  
  // Computed data
  groupedIdeas: GroupedIdeas;
  filteredIdeas: Idea[];
  totalCount: number;
  filteredCount: number;
}

// Utility functions for grouping
export const groupIdeasBy = (ideas: Idea[], groupBy: IdeasGroupBy): GroupedIdeas => {
  if (groupBy === 'none') {
    return { 'Todas las Ideas': ideas };
  }
  
  return ideas.reduce((groups: GroupedIdeas, idea) => {
    const key = groupBy === 'estado' ? idea.estado : idea.prioridad;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(idea);
    return groups;
  }, {});
};

// Utility function to get display label for group keys
export const getGroupDisplayLabel = (key: string, groupBy: IdeasGroupBy): string => {
  if (groupBy === 'estado') {
    const estadoLabels: Record<EstadoIdea, string> = {
      pendiente: 'Pendientes',
      implementada: 'Implementadas',
      descartada: 'Descartadas',
    };
    return estadoLabels[key as EstadoIdea] || key;
  }
  
  if (groupBy === 'prioridad') {
    const prioridadLabels: Record<PrioridadIdea, string> = {
      Baja: 'Prioridad Baja',
      Media: 'Prioridad Media',
      Alta: 'Prioridad Alta',
      Urgente: 'Prioridad Urgente',
    };
    return prioridadLabels[key as PrioridadIdea] || key;
  }
  
  return key;
};

// Utility function to sort ideas
export const sortIdeas = (ideas: Idea[], sortState: IdeasSortState): Idea[] => {
  return [...ideas].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;
    
    switch (sortState.field) {
      case 'titulo':
        aValue = a.titulo.toLowerCase();
        bValue = b.titulo.toLowerCase();
        break;
      case 'estado': {
        // Define deterministic order for estado: pendiente > implementada > descartada
        const estadoOrder = { pendiente: 3, implementada: 2, descartada: 1 };
        aValue = estadoOrder[a.estado];
        bValue = estadoOrder[b.estado];
        break;
      }
      case 'prioridad': {
        // Sort by priority order: Urgente > Alta > Media > Baja
        const prioridadOrder = { Urgente: 4, Alta: 3, Media: 2, Baja: 1 };
        aValue = prioridadOrder[a.prioridad];
        bValue = prioridadOrder[b.prioridad];
        break;
      }
      case 'created_at':
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
        break;
      case 'updated_at':
        aValue = new Date(a.updated_at).getTime();
        bValue = new Date(b.updated_at).getTime();
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) {
      return sortState.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortState.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
};

// Filter function
export const filterIdeas = (ideas: Idea[], filters: IdeasFilterState): Idea[] => {
  return ideas.filter(idea => {
    // Estado filter
    if (filters.estado && filters.estado !== 'all' && idea.estado !== filters.estado) {
      return false;
    }
    
    // Prioridad filter
    if (filters.prioridad && filters.prioridad !== 'all' && idea.prioridad !== filters.prioridad) {
      return false;
    }
    
    // Search filter
    if (filters.search && filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      const searchableText = [
        idea.titulo,
        idea.descripcion || '',
        idea.empresa_relacionada?.nombre || '',
        idea.proyecto_relacionado?.nombre || ''
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }
    
    return true;
  });
};
