import logging
from uuid import UUID
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from supabase import AsyncClient

from app.core.database import get_supabase_client
from app.models.doc_export import DocExport # Import the response model
from app.core.security import get_current_user_id

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=List[DocExport])
async def get_doc_exports_for_thread(
    # Use Query for validation and potential future expansion
    thread_id: Optional[int] = Query(None, description="Filter exports by thread ID."),
    # Add other potential filters if needed later (e.g., by message_id/threads_row_id)
    # threads_row_id: Optional[UUID] = Query(None, description="Filter exports by specific message ID."),
    supabase: AsyncClient = Depends(get_supabase_client),
    current_user_id: str = Depends(get_current_user_id),
):
    """
    Retrieve document export statuses for the current user, optionally filtered by thread_id.
    """
    logger.info(f"User {current_user_id} requesting doc_exports, filter thread_id={thread_id}")
    try:
        user_uuid = UUID(current_user_id)
        query = supabase.table("doc_exports").select("*").eq("user_id", str(user_uuid))

        if thread_id is not None:
            # We need to join or query based on threads_row_id which links to threads table's id,
            # but the threads table uses thread_id (int) for grouping.
            # A direct filter on doc_exports by thread_id (int) isn't possible unless we add it there.
            # Option 1: Fetch all for user and filter in Python (inefficient for many exports)
            # Option 2: Add thread_id column to doc_exports table (requires schema change)
            # Option 3: Query threads table first for all message IDs in the thread, then query doc_exports.

            # Let's implement Option 3 for now, although less efficient than Option 2.
            logger.debug(f"Fetching message IDs for thread_id {thread_id}")
            # Remove 'await' as .execute() seems synchronous here
            messages_response = supabase.table("threads").select("id").eq("thread_id", thread_id).eq("user_id", str(user_uuid)).execute()
 
            if messages_response and messages_response.data: # Check response object exists
                message_ids = [str(msg['id']) for msg in messages_response.data]
                if message_ids:
                    logger.debug(f"Filtering doc_exports by threads_row_id IN {message_ids}")
                    query = query.in_("threads_row_id", message_ids)
                else:
                    # No messages found for this thread for this user, return empty
                    return []
            else:
                 # No messages found for this thread for this user, return empty
                 return []
 
        # Execute the query for doc_exports
        # Remove 'await' as .execute() seems synchronous here
        response = query.execute()
 
        if response and response.data: # Check response object exists
            logger.info(f"Returning {len(response.data)} doc_export records for user {current_user_id}, thread_id={thread_id}")
            # Pydantic validation will happen automatically via response_model
            return response.data
        else:
            logger.info(f"No doc_export records found for user {current_user_id}, thread_id={thread_id}")
            return []

    except ValueError:
        logger.error(f"Invalid user ID format received from token: {current_user_id}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format.")
    except Exception as e:
        logger.exception(f"Error fetching doc_exports for user {current_user_id}, thread_id={thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve document export statuses.")