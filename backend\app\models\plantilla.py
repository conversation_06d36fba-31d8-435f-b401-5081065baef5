from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from decimal import Decimal

# Import enums from tarea model
from .tarea import PrioridadTarea

class PlantillaProcesoBase(BaseModel):
    """Base model for proceso template data."""
    nombre_plantilla: str = Field(..., description="Nombre de la plantilla de proceso")
    descripcion_plantilla: Optional[str] = Field(None, description="Descripción de la plantilla")
    objetivo_plantilla: Optional[str] = Field(None, description="Objetivo de la plantilla")
    info_adicional_plantilla: Optional[str] = Field(None, description="Información adicional")

class PlantillaProcesoCreate(PlantillaProcesoBase):
    """Model for creating a new proceso template."""
    tareas_plantilla_ids: Optional[List[UUID]] = Field(default_factory=list, description="IDs de plantillas de tareas asociadas")

class PlantillaProcesoUpdate(BaseModel):
    """Model for updating proceso template data."""
    nombre_plantilla: Optional[str] = None
    descripcion_plantilla: Optional[str] = None
    objetivo_plantilla: Optional[str] = None
    info_adicional_plantilla: Optional[str] = None

class PlantillaProcesoInDBBase(PlantillaProcesoBase):
    """Base model for proceso template data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PlantillaProceso(PlantillaProcesoInDBBase):
    """Model representing a proceso template as returned from the API."""
    # Related data populated by service layer
    tareas_plantilla: Optional[List['PlantillaTarea']] = Field(default_factory=list)
    total_tareas: int = Field(0, description="Total de tareas en la plantilla")
    
    # Usage statistics
    veces_utilizada: int = Field(0, description="Veces que se ha utilizado esta plantilla")

class PlantillaProcesoInDB(PlantillaProcesoInDBBase):
    """Model representing a proceso template stored in the database."""
    pass

# Tarea Template Models
class PlantillaTareaBase(BaseModel):
    """Base model for tarea template data."""
    titulo_plantilla: str = Field(..., description="Título de la plantilla de tarea")
    descripcion_base: Optional[str] = Field(None, description="Descripción base de la tarea")
    duracion_estimada_horas: Optional[Decimal] = Field(None, description="Duración estimada en horas")
    prioridad_predeterminada: PrioridadTarea = Field(PrioridadTarea.MEDIA, description="Prioridad predeterminada")
    info_adicional_plantilla: Optional[str] = Field(None, description="Información adicional")

class PlantillaTareaCreate(PlantillaTareaBase):
    """Model for creating a new tarea template."""
    pass

class PlantillaTareaUpdate(BaseModel):
    """Model for updating tarea template data."""
    titulo_plantilla: Optional[str] = None
    descripcion_base: Optional[str] = None
    duracion_estimada_horas: Optional[Decimal] = None
    prioridad_predeterminada: Optional[PrioridadTarea] = None
    info_adicional_plantilla: Optional[str] = None

class PlantillaTareaInDBBase(PlantillaTareaBase):
    """Base model for tarea template data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PlantillaTarea(PlantillaTareaInDBBase):
    """Model representing a tarea template as returned from the API."""
    # Usage statistics
    veces_utilizada: int = Field(0, description="Veces que se ha utilizado esta plantilla")
    
    # Related data
    procesos_que_la_usan: Optional[List[str]] = Field(default_factory=list, description="Nombres de procesos que usan esta plantilla")

class PlantillaTareaInDB(PlantillaTareaInDBBase):
    """Model representing a tarea template stored in the database."""
    pass

# Relationship Models
class ProcesoTareaPlantillaBase(BaseModel):
    """Base model for proceso-tarea template relationship."""
    proceso_plantilla_id: UUID = Field(..., description="ID de la plantilla de proceso")
    plantilla_tarea_id: UUID = Field(..., description="ID de la plantilla de tarea")
    orden_en_proceso: int = Field(..., description="Orden de la tarea en el proceso")
    dias_desplazamiento: Optional[int] = Field(0, description="Días de desplazamiento desde el inicio")
    es_obligatoria: bool = Field(True, description="Si la tarea es obligatoria")
    notas_especificas_proceso: Optional[str] = Field(None, description="Notas específicas para este proceso")

class ProcesoTareaPlantillaCreate(ProcesoTareaPlantillaBase):
    """Model for creating proceso-tarea template relationship."""
    pass

class ProcesoTareaPlantillaUpdate(BaseModel):
    """Model for updating proceso-tarea template relationship."""
    orden_en_proceso: Optional[int] = None
    dias_desplazamiento: Optional[int] = None
    es_obligatoria: Optional[bool] = None
    notas_especificas_proceso: Optional[str] = None

class ProcesoTareaPlantillaInDBBase(ProcesoTareaPlantillaBase):
    """Base model for proceso-tarea template relationship stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ProcesoTareaPlantilla(ProcesoTareaPlantillaInDBBase):
    """Model representing proceso-tarea template relationship as returned from the API."""
    # Populated by service layer
    plantilla_tarea: Optional[PlantillaTarea] = None

class ProcesoTareaPlantillaInDB(ProcesoTareaPlantillaInDBBase):
    """Model representing proceso-tarea template relationship stored in the database."""
    pass

# Response Models
class PlantillaProcesoResponse(PlantillaProceso):
    """Response model for proceso template API endpoints."""
    pass

class PlantillaProcesoListResponse(BaseModel):
    """Response model for proceso template list endpoints."""
    plantillas: List[PlantillaProceso]
    total: int
    page: int
    size: int

class PlantillaTareaResponse(PlantillaTarea):
    """Response model for tarea template API endpoints."""
    pass

class PlantillaTareaListResponse(BaseModel):
    """Response model for tarea template list endpoints."""
    plantillas: List[PlantillaTarea]
    total: int
    page: int
    size: int

# Template instantiation models
class InstanciarPlantillaRequest(BaseModel):
    """Request model for instantiating a template."""
    plantilla_proceso_id: UUID
    proyecto_id: Optional[UUID] = None
    empresa_id: Optional[UUID] = None
    nombre_proceso: Optional[str] = None
    fecha_inicio: Optional[datetime] = None
    responsable_usuario_id: Optional[UUID] = None

class InstanciarPlantillaResponse(BaseModel):
    """Response model for template instantiation."""
    proceso_id: UUID
    tareas_creadas: List[UUID]
    mensaje: str
