from uuid import uuid4, UUID # Ensured UUID is imported
import datetime

from app.models.crm_lead_empresa import LeadEmpresaCreate
from app.core.database import get_supabase_client
# Removed redundant from uuid import UUID

async def create_lead_empresa(lead_empresa_in: LeadEmpresaCreate, user_id: UUID) -> dict: # Removed db: Session
    """
    Create a new lead empresa.
    `user_id` is for audit or if 'creado_por' is added to schema.
    If `asignado_usuario_id` is not provided in `lead_empresa_in`,
    it could default to `user_id` (the creator).
    """
    supabase = await get_supabase_client() # Added await
    
    lead_empresa_data = lead_empresa_in.dict(exclude_none=True)
    lead_empresa_data['id'] = str(uuid4())
    
    # Default 'asignado_usuario_id' to current user if not provided
    if 'asignado_usuario_id' not in lead_empresa_data or lead_empresa_data['asignado_usuario_id'] is None:
        lead_empresa_data['asignado_usuario_id'] = str(user_id)

    try:
        response = await supabase.table('leads_empresas').insert(lead_empresa_data).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]
        else:
            print(f"Supabase insert response for lead_empresa: {response}") 
            raise Exception("Failed to create lead empresa or retrieve created data.")

    except Exception as e:
        print(f"Error creating lead empresa: {e}")
        raise e

# Placeholder for other service functions

async def get_leads_empresas(search: str | None = None, skip: int = 0, limit: int = 100) -> list[dict]:
    """
    Retrieve lead empresas, with optional search.
    """
    supabase = await get_supabase_client() # Added await
    # Base query: select all columns, order by nombre
    # If 'asignado_usuario_id' needs to be resolved to a user name for display,
    # a join similar to personas->empresas would be needed: `*, asignado_usuario:usuarios (nombre, apellidos, email)`
    # For now, keeping it simple and returning asignado_usuario_id as UUID.
    query = supabase.table('leads_empresas').select("*").order('nombre').offset(skip).limit(limit)

    if search:
        search_term = f"%{search}%"
        # Searching in nombre or fuente. Adjust if other fields are relevant.
        query = query.or_(f"nombre.ilike.{search_term},fuente.ilike.{search_term}")
        
    try:
        response = query.execute() # Changed to synchronous
        if response.data:
            from app.models.crm_lead_empresa import LeadEmpresa # Add import
            from pydantic import ValidationError # Import ValidationError
            import logging # Import logging
            logger = logging.getLogger(__name__) # Get logger instance

            validated_leads = []
            for lead_data in response.data:
                try:
                    validated_leads.append(LeadEmpresa.model_validate(lead_data))
                except ValidationError as ve:
                    logger.error(f"Pydantic ValidationError for lead_empresa_data: {lead_data}")
                    logger.error(f"Details: {ve.errors(include_input=False)}")
                    raise ve 
            return validated_leads
        return []
    except ValidationError as ve_outer: 
        # logger is already defined above if this block is reached via re-raise
        logger.error(f"Outer Pydantic ValidationError in get_leads_empresas: {ve_outer.errors(include_input=False)}")
        raise ve_outer
    except Exception as e: 
        # logger might not be defined if this is the first exception caught, ensure it is
        import logging # Ensure logging is imported here too for safety
        logger_fallback = logging.getLogger(__name__)
        logger_fallback.error(f"Generic error fetching or validating leads_empresas: {e}", exc_info=True)
        raise e
