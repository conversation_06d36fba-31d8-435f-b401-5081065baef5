import React, { useState, useRef, useEffect } from 'react';
import { IoPencil, IoCheckmark, IoClose } from 'react-icons/io5';

interface TitleEditorProps {
  title: string;
  onSave: (newTitle: string) => Promise<void>;
  className?: string;
  placeholder?: string;
}

const TitleEditor: React.FC<TitleEditorProps> = ({
  title,
  onSave,
  className = '',
  placeholder = 'Título del chat'
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(title);
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update edit value when title prop changes
  useEffect(() => {
    setEditValue(title);
  }, [title]);

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditValue(title);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(title);
  };

  const handleSave = async () => {
    if (editValue.trim() === '') {
      return; // Don't save empty titles
    }

    if (editValue.trim() === title.trim()) {
      setIsEditing(false);
      return; // No changes
    }

    setIsSaving(true);
    try {
      await onSave(editValue.trim());
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving title:', error);
      // Optionally show error message to user
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <input
          ref={inputRef}
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isSaving}
        />
        <button
          onClick={handleSave}
          disabled={isSaving || editValue.trim() === ''}
          className="p-1 text-green-600 hover:text-green-700 disabled:text-gray-400 disabled:cursor-not-allowed"
          title="Guardar"
        >
          <IoCheckmark size={16} />
        </button>
        <button
          onClick={handleCancel}
          disabled={isSaving}
          className="p-1 text-red-600 hover:text-red-700 disabled:text-gray-400 disabled:cursor-not-allowed"
          title="Cancelar"
        >
          <IoClose size={16} />
        </button>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 group ${className}`}>
      <h1 className="flex-1 text-lg font-semibold text-gray-900 truncate">
        {title || placeholder}
      </h1>
      <button
        onClick={handleStartEdit}
        className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
        title="Editar título"
      >
        <IoPencil size={16} />
      </button>
    </div>
  );
};

export default TitleEditor;
