import { TareaMatrix } from './tarea';
import { ProyectoSummary } from './proyecto';
import { ProcesoSummary } from './proceso';

export interface DashboardData {
  tareas_matrix: TareaMatrix;
  proyectos_activos: ProyectoSummary[];
  procesos_importantes: ProcesoSummary[];
  quick_stats: QuickStats;
  last_updated: string;
}

export interface QuickStats {
  total_tareas_activas: number;
  tareas_urgentes: number;
  tareas_vencidas: number;
  total_proyectos_activos: number;
  total_procesos_importantes: number;
  proyectos_en_riesgo: number;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  route: string;
}

export interface QuickActionsResponse {
  actions: QuickAction[];
}

export interface DashboardNotification {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  action?: string;
  route?: string;
}

export interface DashboardNotificationsResponse {
  notifications: DashboardNotification[];
  total_count: number;
}

export interface SummaryWidget {
  id: string;
  title: string;
  value: number;
  icon: string;
  color: string;
  trend: 'up' | 'down' | 'stable';
}

export interface SummaryWidgetsResponse {
  widgets: SummaryWidget[];
}

// Color mappings for widgets
export const WIDGET_COLORS: Record<string, string> = {
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  purple: 'bg-purple-500',
  red: 'bg-red-500',
  orange: 'bg-orange-500',
  yellow: 'bg-yellow-500',
  gray: 'bg-gray-500'
};

export const NOTIFICATION_COLORS: Record<DashboardNotification['type'], string> = {
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
  success: 'bg-green-50 border-green-200 text-green-800'
};
