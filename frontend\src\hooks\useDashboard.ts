import { useState, useEffect, useCallback } from 'react';
import { 
  DashboardData, 
  QuickActionsResponse, 
  DashboardNotificationsResponse,
  SummaryWidgetsResponse
} from '../types/dashboard';
import { apiClient, api } from '../lib/api';

export const useDashboard = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.dashboard.getData() as DashboardData;
      setDashboardData(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching dashboard data');
      console.error('Error fetching dashboard data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshDashboard = useCallback(async () => {
    await fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard,
    setError
  };
};

export const useQuickActions = () => {
  const [quickActions, setQuickActions] = useState<QuickActionsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchQuickActions = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.dashboard.getQuickActions() as QuickActionsResponse;
      setQuickActions(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching quick actions');
      console.error('Error fetching quick actions:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchQuickActions();
  }, [fetchQuickActions]);

  return {
    quickActions,
    loading,
    error,
    fetchQuickActions,
    setError
  };
};

export const useDashboardNotifications = () => {
  const [notifications, setNotifications] = useState<DashboardNotificationsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.dashboard.getNotifications() as DashboardNotificationsResponse;
      setNotifications(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications();
  }, [fetchNotifications]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications,
    loading,
    error,
    refreshNotifications,
    setError
  };
};

export const useSummaryWidgets = () => {
  const [widgets, setWidgets] = useState<SummaryWidgetsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWidgets = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.get<SummaryWidgetsResponse>('/api/v1/dashboard/widgets/summary');
      setWidgets(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching summary widgets');
      console.error('Error fetching summary widgets:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchWidgets();
  }, [fetchWidgets]);

  return {
    widgets,
    loading,
    error,
    fetchWidgets,
    setError
  };
};
