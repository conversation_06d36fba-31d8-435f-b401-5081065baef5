// Basic performance configuration for the chat application
// Simplified version to avoid import issues

export const PERFORMANCE_CONFIG = {
  // Virtual scrolling settings
  VIRTUAL_SCROLL: {
    ITEM_HEIGHT: 200, // Average height per message in pixels
    OVERSCAN: 3, // Number of items to render outside visible area
    CONTAINER_HEIGHT: 600, // Default container height
  },

  // Message pagination settings
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 50, // Number of messages to load per page
    MAX_PAGE_SIZE: 100, // Maximum allowed page size
    INITIAL_LOAD_SIZE: 30, // Initial messages to load for new threads
    LOAD_MORE_THRESHOLD: 200, // Pixels from top to trigger load more
  },

  // Memory management settings
  MEMORY: {
    MAX_CACHED_THREADS: 5, // Maximum number of threads to keep in cache
    MAX_MESSAGES_IN_MEMORY: 100, // Maximum messages to keep in active thread
    CACHE_CLEANUP_INTERVAL: 300000, // 5 minutes in milliseconds
    TOKEN_CACHE_SIZE: 1000, // Maximum number of token calculations to cache
  },

  // Performance monitoring
  MONITORING: {
    ENABLE_PERFORMANCE_TRACKING: true, // Enable performance metrics collection
    RENDER_TIME_THRESHOLD: 100, // Milliseconds - warn if render takes longer
    MEMORY_USAGE_THRESHOLD: 50 * 1024 * 1024, // 50MB - warn if memory usage exceeds
    MAX_RENDER_HISTORY: 10, // Number of render times to keep for averaging
  },

  // Content optimization
  CONTENT: {
    MAX_MESSAGE_LENGTH: 10000, // Truncate messages longer than this
    MAX_INTERMEDIATE_STEPS: 20, // Limit intermediate steps displayed
    LAZY_LOAD_THRESHOLD: 5, // Load content lazily after this many messages
    MARKDOWN_PROCESSING_DELAY: 100, // Debounce markdown processing (ms)
  },

  // Realtime settings
  REALTIME: {
    RECONNECT_DELAY: 2000, // Base delay for reconnection attempts
    MAX_RECONNECT_ATTEMPTS: 5, // Maximum reconnection attempts
    HEARTBEAT_INTERVAL: 20000, // Heartbeat interval in milliseconds
    MESSAGE_BUFFER_SIZE: 10, // Buffer realtime messages to prevent flooding
  },

  // UI responsiveness
  UI: {
    DEBOUNCE_SCROLL: 16, // Debounce scroll events (60fps)
    DEBOUNCE_RESIZE: 250, // Debounce window resize events
    ANIMATION_DURATION: 200, // Default animation duration
    LOADING_DELAY: 500, // Show loading indicator after this delay
  },

  // Development settings
  DEV: {
    ENABLE_CONSOLE_LOGS: process.env.NODE_ENV === 'development',
    ENABLE_PERFORMANCE_LOGS: process.env.NODE_ENV === 'development',
    MOCK_SLOW_NETWORK: false, // Simulate slow network for testing
    MOCK_LARGE_DATASET: false, // Use large mock dataset for testing
  },
} as const;

// Performance thresholds for different scenarios
export const PERFORMANCE_THRESHOLDS = {
  // Message count thresholds
  SMALL_CONVERSATION: 50,
  MEDIUM_CONVERSATION: 200,
  LARGE_CONVERSATION: 500,
  VERY_LARGE_CONVERSATION: 1000,

  // Token count thresholds
  LOW_TOKEN_COUNT: 1000,
  MEDIUM_TOKEN_COUNT: 10000,
  HIGH_TOKEN_COUNT: 50000,
  VERY_HIGH_TOKEN_COUNT: 90000,

  // Performance impact levels
  PERFORMANCE_IMPACT: {
    LOW: {
      maxMessages: 100,
      maxTokens: 10000,
      enableVirtualScroll: false,
      enableLazyLoading: false,
    },
    MEDIUM: {
      maxMessages: 500,
      maxTokens: 50000,
      enableVirtualScroll: true,
      enableLazyLoading: true,
    },
    HIGH: {
      maxMessages: 1000,
      maxTokens: 90000,
      enableVirtualScroll: true,
      enableLazyLoading: true,
      enableContentTruncation: true,
    },
    CRITICAL: {
      maxMessages: Infinity,
      maxTokens: Infinity,
      enableVirtualScroll: true,
      enableLazyLoading: true,
      enableContentTruncation: true,
      enableAggressiveCaching: true,
    },
  },
} as const;

// Helper function to determine performance level based on conversation size
export const getPerformanceLevel = (messageCount: number, tokenCount: number) => {
  if (messageCount <= PERFORMANCE_THRESHOLDS.SMALL_CONVERSATION && 
      tokenCount <= PERFORMANCE_THRESHOLDS.LOW_TOKEN_COUNT) {
    return 'LOW';
  }
  
  if (messageCount <= PERFORMANCE_THRESHOLDS.MEDIUM_CONVERSATION && 
      tokenCount <= PERFORMANCE_THRESHOLDS.MEDIUM_TOKEN_COUNT) {
    return 'MEDIUM';
  }
  
  if (messageCount <= PERFORMANCE_THRESHOLDS.LARGE_CONVERSATION && 
      tokenCount <= PERFORMANCE_THRESHOLDS.HIGH_TOKEN_COUNT) {
    return 'HIGH';
  }
  
  return 'CRITICAL';
};

// Helper function to get optimized settings based on performance level
export const getOptimizedSettings = (messageCount: number, tokenCount: number) => {
  const level = getPerformanceLevel(messageCount, tokenCount);
  const settings = PERFORMANCE_THRESHOLDS.PERFORMANCE_IMPACT[level];

  return {
    ...PERFORMANCE_CONFIG,
    level,
    optimizations: settings,
    shouldUseVirtualScroll: settings.enableVirtualScroll,
    shouldUseLazyLoading: settings.enableLazyLoading,
    shouldTruncateContent: 'enableContentTruncation' in settings ? settings.enableContentTruncation : false,
    shouldUseAggressiveCaching: 'enableAggressiveCaching' in settings ? settings.enableAggressiveCaching : false,
  };
};

// Performance monitoring utilities
export const performanceUtils = {
  // Measure function execution time
  measureTime: <T>(fn: () => T, label?: string): T => {
    if (!PERFORMANCE_CONFIG.DEV.ENABLE_PERFORMANCE_LOGS) {
      return fn();
    }
    
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;
    
    if (label && duration > PERFORMANCE_CONFIG.MONITORING.RENDER_TIME_THRESHOLD) {
      console.warn(`Performance warning: ${label} took ${duration.toFixed(2)}ms`);
    }
    
    return result;
  },

  // Check memory usage (if available)
  checkMemoryUsage: (): number | null => {
    if ('memory' in performance) {
      const memory = (performance as Record<string, unknown>).memory as { usedJSHeapSize: number };
      return memory.usedJSHeapSize;
    }
    return null;
  },

  // Log performance metrics
  logPerformanceMetrics: (metrics: Record<string, unknown>) => {
    if (PERFORMANCE_CONFIG.DEV.ENABLE_PERFORMANCE_LOGS) {
      console.group('Performance Metrics');
      Object.entries(metrics).forEach(([key, value]) => {
        console.log(`${key}:`, value);
      });
      console.groupEnd();
    }
  },
};

export default PERFORMANCE_CONFIG;
