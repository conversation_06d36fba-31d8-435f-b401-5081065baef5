import React, { useState } from 'react'; // Import useState
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChatMessage } from '../../contexts/ChatContext';
import { IoSend, IoLink, IoCopy, IoCheckmark, IoTrash } from 'react-icons/io5'; // Import icons including copy and delete
import { useAuth } from '../../hooks/useAuth'; // Import useAuth for token

// Types for ReactMarkdown components - using React.ComponentProps for proper typing

// Removed unused IntermediateStep type alias

// --- CodeBlock Component with Copy Functionality ---
interface CodeBlockProps {
  children: string;
  className?: string;
  language?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ children, className, language }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // Extract language from className (e.g., "language-javascript" -> "javascript")
  const detectedLanguage = language || className?.replace('language-', '') || 'code';

  return (
    <div className="my-4 overflow-hidden rounded-lg border border-gray-200 bg-gray-50 relative group shadow-sm">
      {/* Header with language and copy button */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-100 border-b border-gray-200">
        <span className="text-sm font-medium text-gray-600 uppercase tracking-wide">
          {detectedLanguage}
        </span>
        <button
          onClick={handleCopy}
          className={`flex items-center space-x-2 px-3 py-1.5 rounded text-sm font-medium transition-all duration-200 ${
            copied
              ? 'bg-green-100 text-green-700 border border-green-200'
              : 'bg-white text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-300 hover:border-gray-400'
          }`}
          title={copied ? 'Copiado!' : 'Copiar código'}
        >
          {copied ? (
            <>
              <IoCheckmark size={14} />
              <span>Copiado</span>
            </>
          ) : (
            <>
              <IoCopy size={14} />
              <span>Copiar</span>
            </>
          )}
        </button>
      </div>

      {/* Code content */}
      <pre className="overflow-x-auto p-6 text-base leading-relaxed">
        <code className={`${className || ''} font-mono text-gray-800 break-all whitespace-pre-wrap`}>
          {children}
        </code>
      </pre>
    </div>
  );
};

// --- Simple Code Block with Copy for Tool Steps ---
interface SimpleCodeBlockProps {
  children: string;
  title?: string;
}

const SimpleCodeBlock: React.FC<SimpleCodeBlockProps> = ({ children, title = "Código" }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  return (
    <div className="overflow-hidden rounded border border-gray-100 mt-0.5 relative group">
      <div className="flex items-center justify-between px-2 py-1 bg-gray-50 border-b border-gray-100">
        <span className="text-[0.6rem] font-medium text-gray-500 uppercase tracking-wide">
          {title}
        </span>
        <button
          onClick={handleCopy}
          className={`flex items-center space-x-1 px-1.5 py-0.5 rounded text-[0.6rem] font-medium transition-all duration-200 ${
            copied
              ? 'bg-green-100 text-green-600'
              : 'bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100'
          }`}
          title={copied ? 'Copiado!' : 'Copiar'}
        >
          {copied ? (
            <>
              <IoCheckmark size={10} />
              <span>✓</span>
            </>
          ) : (
            <>
              <IoCopy size={10} />
              <span>Copiar</span>
            </>
          )}
        </button>
      </div>
      <pre className="whitespace-pre-wrap overflow-x-auto font-mono text-[0.7rem] leading-snug text-gray-600 bg-white p-1.5 break-all">
        {children}
      </pre>
    </div>
  );
};

// --- ToolStepDisplay Component ---
interface ToolStepDisplayProps {
  stepNumber: number;
  toolName: string | null;
  input: ChatMessage;
  output: ChatMessage | null;
}

const ToolStepDisplay: React.FC<ToolStepDisplayProps> = ({ stepNumber, toolName, input, output }) => {
  const [isOpen, setIsOpen] = useState(false); // State for this specific tool step

  return (
    <div className="p-2 bg-blue-50 rounded border border-blue-200">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1.5 mb-1 w-full text-left focus:outline-none"
        aria-expanded={isOpen}
      >
        <span className="text-sm">🛠️</span>
        <span className="font-medium text-blue-800 flex-1">Step {stepNumber}: Tool: {toolName || 'tools'}</span>
         {/* Chevron Icon */}
         <svg xmlns="http://www.w3.org/2000/svg" className={`h-3 w-3 transition-transform text-blue-600 ${isOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
         </svg>
      </button>

      {/* Collapsible Input/Output */}
      {isOpen && (
        <div className="pl-5 mt-1 space-y-1.5"> {/* Indent content */}
          {/* Input - Parse content and display tool_params */}
          <div>
            <span className="font-semibold text-gray-700 text-[0.65rem]">Input Params:</span>
            <SimpleCodeBlock title="Input Params">
              {(() => {
                 try {
                   const contentJson = JSON.parse(input.content || '{}');
                   // Attempt to parse tool_params if it's a stringified JSON
                   let toolParams = contentJson?.toolInput?.tool_params;
                   if (typeof toolParams === 'string') {
                       try {
                           toolParams = JSON.parse(toolParams);
                       } catch { /* Ignore inner parse error, display original string */ }
                   }
                   // Pretty print the params object or the original string if parsing failed
                   return JSON.stringify(toolParams || contentJson?.toolInput || '(No Input Params)', null, 2);
                 } catch (e) {
                   console.error("Failed to parse input content JSON:", e);
                   return input.content || '(Invalid Input Format)'; // Fallback to raw content on error
                 }
              })()}
            </SimpleCodeBlock>
          </div>
          {/* Output */}
          {output && (
            <div>
              <span className="font-semibold text-gray-700 text-[0.65rem]">Output:</span>
              <SimpleCodeBlock title="Output">
                {output.content || '(No Output content)'}
              </SimpleCodeBlock>
            </div>
          )}
          {!output && (
            <p className="text-gray-500 italic text-[0.65rem]">(Tool output missing or not yet received)</p>
          )}
        </div>
      )}
    </div>
  );
};


// --- MessageBubble Component ---
interface MessageBubbleProps {
  message: ChatMessage;
  // Add props for intermediate steps later if needed
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const [isStepsOpen, setIsStepsOpen] = useState(false); // State for dropdown visibility
  const { session } = useAuth(); // Get session for API calls
  // console.log(`MessageBubble rendering: ${message.id}, from: ${message.from_sender}, type: ${message.type}, steps:`, message.intermediate_steps, `doc_status: ${message.doc_export_status}`);
  const isUser = message.from_sender === 'User';
  const hasIntermediateSteps = message.intermediate_steps && message.intermediate_steps.length > 0;

  // Helper to group tool_use/tool_output pairs and number steps
  const groupAndNumberSteps = (steps: ChatMessage[] | undefined) => {
    if (!steps) return [];

    const grouped: Array<{ type: 'observation' | 'tool'; stepNumber: number; data?: ChatMessage; toolName?: string | null; input?: ChatMessage; output?: ChatMessage | null }> = [];
    let stepCounter = 0;
    let i = 0;

    while (i < steps.length) {
      const currentStep = steps[i];
      stepCounter++;

      if (currentStep.type === 'tool_use') {
        const toolName = currentStep.tool_name;
        const nextStep = steps[i + 1];
        // Assume tool_output immediately follows tool_use for the same execution
        if (nextStep && nextStep.type === 'tool_output') {
          grouped.push({
            type: 'tool',
            stepNumber: stepCounter,
            toolName: toolName,
            input: currentStep,
            output: nextStep
          });
          i += 2; // Skip the next step as it's grouped
        } else {
          // Tool use without an immediately following output (or end of list)
          grouped.push({
            type: 'tool',
            stepNumber: stepCounter,
            toolName: toolName,
            input: currentStep,
            output: null // Mark output as missing/null
          });
          i++;
        }
      } else if (currentStep.type === 'observation') {
        grouped.push({ type: 'observation', stepNumber: stepCounter, data: currentStep });
        i++;
      } else if (currentStep.type === 'tool_output') {
         // Orphaned tool_output? Log warning and maybe display it standalone.
         console.warn("Orphaned tool_output step found:", currentStep);
         // Decide how to display orphaned outputs, e.g., treat as observation?
         grouped.push({ type: 'observation', stepNumber: stepCounter, data: { ...currentStep, content: `Orphaned Tool Output: ${currentStep.content}` } });
         i++;
      }
       else {
        // Handle other unexpected step types if necessary
        console.warn("Unknown intermediate step type:", currentStep);
         grouped.push({ type: 'observation', stepNumber: stepCounter, data: { ...currentStep, content: `Unknown Step Type (${currentStep.type}): ${currentStep.content}` } });
        i++;
      }
    }
    return grouped;
  };

  const groupedAndNumberedSteps = groupAndNumberSteps(message.intermediate_steps);

  const handleSendToDocs = async () => {
    if (!message.id || !session?.access_token) {
      console.error("Message ID or session token is missing for sendToDocs.");
      // Optionally show an error to the user
      return;
    }

    // Optimistically update UI or rely on ChatProvider to handle Realtime update
    // For now, the button will be disabled based on doc_export_status which ChatProvider updates

    const apiUrl = `${import.meta.env.VITE_API_BASE_URL}/chat/messages/${message.id}/send_to_docs`;

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json', // Though no body is sent for this POST
        },
      });

      if (response.status === 202) {
        console.log(`Successfully initiated send_to_docs for message ${message.id}`);
        // Backend will set status to 'pending', Realtime will update ChatProvider state
        // which in turn updates this component's message prop.
      } else {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        console.error(`Failed to initiate send_to_docs for message ${message.id}:`, errorData.detail || response.statusText);
        // Optionally, directly set an error state here if Realtime update is too slow or fails
        // For now, relying on backend to set 'error' status which Realtime picks up.
      }
    } catch (error) {
      console.error(`Error calling send_to_docs API for message ${message.id}:`, error);
      // Handle network errors, etc.
    }
  };

  const handleDeleteMessage = async () => {
    if (!message.id || !session?.access_token) {
      console.error("Message ID or session token is missing for delete.");
      return;
    }

    // Confirm deletion
    if (!confirm('¿Estás seguro de que quieres eliminar este mensaje? Esta acción no se puede deshacer.')) {
      return;
    }

    const apiUrl = `${import.meta.env.VITE_API_BASE_URL}/chat/messages/${message.id}`;

    try {
      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (response.ok) {
        console.log(`Successfully deleted message ${message.id}`);
        // The message will be removed from UI via Realtime updates
      } else {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        console.error(`Failed to delete message ${message.id}:`, errorData.detail || response.statusText);
        alert('Error al eliminar el mensaje. Por favor, inténtalo de nuevo.');
      }
    } catch (error) {
      console.error(`Error calling delete message API for message ${message.id}:`, error);
      alert('Error de conexión al eliminar el mensaje.');
    }
  };


  return (
    <div className={`w-full flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}> {/* Increased margin bottom */}
      <div
        className={`relative w-full ${isUser ? 'max-w-4xl' : 'max-w-6xl'} px-6 py-5 rounded-2xl shadow-lg overflow-hidden ${ /* Much larger max-width for agent, increased padding */
          isUser
            ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white' /* User: Gradient blue with white text */
            : 'bg-white text-gray-800 border border-gray-100' /* Agent: White background, lighter border */
        }`}
      >
        {/* Action buttons - top right */}
        <div className="absolute top-2 right-2 flex items-center space-x-2">
          {/* Delete button - always visible for user messages, and for agent messages */}
          <button
            onClick={handleDeleteMessage}
            className={`p-1 hover:text-red-600 transition-colors ${
              isUser ? 'text-blue-200 hover:text-white' : 'text-gray-400'
            }`}
            title="Eliminar mensaje"
          >
            <IoTrash size={14} />
          </button>

          {/* "Send to Docs" UI elements - only for agent answers */}
          {!isUser && message.type === 'answer' && (
            <>
              {message.doc_export_status === 'pending' && (
                <>
                  <button className="p-1 text-gray-400 cursor-not-allowed" disabled>
                    <IoSend size={16} />
                  </button>
                  <span className="text-xs text-gray-500 italic">Processing...</span>
                </>
              )}
              {message.doc_export_status === 'success' && message.doc_export_url && (
                <>
                  <button className="p-1 text-gray-400 cursor-not-allowed" disabled>
                    <IoSend size={16} />
                  </button>
                  <a href={message.doc_export_url} target="_blank" rel="noopener noreferrer" title="Open Document">
                    <button className="p-1 text-green-500 hover:text-green-600">
                      <IoLink size={18} />
                    </button>
                  </a>
                </>
              )}
              {message.doc_export_status === 'error' && (
                <>
                  <button className="p-1 text-gray-400 cursor-not-allowed" disabled>
                    <IoSend size={16} />
                  </button>
                  <span className="text-xs text-red-500 italic" title="Failed to create document">Error</span>
                </>
              )}
              {(!message.doc_export_status || message.doc_export_status === null) && (
                <button
                  onClick={handleSendToDocs}
                  className="p-1 text-gray-500 hover:text-blue-600"
                  title="Send to Google Docs"
                >
                  <IoSend size={16} />
                </button>
              )}
            </>
          )}
        </div>

        {/* Render message content using ReactMarkdown */}
        <div className={`prose prose-lg max-w-none break-words overflow-hidden ${!isUser && message.type === 'answer' ? 'pt-6' : ''}`}> {/* Increased prose size for better readability */}
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              // Custom code block component with copy functionality
              code: ({ className, children, ...props }: React.ComponentProps<'code'>) => {
                const isInline = !className?.includes('language-');
                if (isInline) {
                  return (
                    <code
                      className={`px-2 py-1 rounded text-sm font-mono break-all ${isUser ? 'bg-blue-400 bg-opacity-30 text-white' : 'bg-gray-100 text-gray-800'}`}
                      {...props}
                    >
                      {children}
                    </code>
                  );
                }
                // Use our custom CodeBlock component for block code
                return (
                  <CodeBlock className={className} language={className?.replace('language-', '')}>
                    {String(children).replace(/\n$/, '')}
                  </CodeBlock>
                );
              },
              // Custom pre component - fallback for pre tags without code
              pre: ({ children, ...props }: React.ComponentProps<'pre'>) => {
                // Check if children contains a code element
                if (React.isValidElement(children) && children.type === 'code') {
                  return children; // Let the code component handle it
                }
                return (
                  <div className="my-4 overflow-hidden rounded-lg border border-gray-200">
                    <pre className="overflow-x-auto bg-gray-50 p-4 text-sm font-mono text-gray-800 whitespace-pre-wrap break-all" {...props}>
                      {children}
                    </pre>
                  </div>
                );
              },
              // Custom heading components for better styling
              h1: ({ children, ...props }: React.ComponentProps<'h1'>) => (
                <h1 className={`text-2xl font-bold mt-6 mb-4 pb-2 ${isUser ? 'text-white border-b border-blue-300' : 'text-gray-900 border-b border-gray-200'}`} {...props}>
                  {children}
                </h1>
              ),
              h2: ({ children, ...props }: React.ComponentProps<'h2'>) => (
                <h2 className={`text-xl font-semibold mt-5 mb-3 ${isUser ? 'text-white' : 'text-gray-800'}`} {...props}>
                  {children}
                </h2>
              ),
              h3: ({ children, ...props }: React.ComponentProps<'h3'>) => (
                <h3 className={`text-lg font-medium mt-4 mb-2 ${isUser ? 'text-white' : 'text-gray-800'}`} {...props}>
                  {children}
                </h3>
              ),
              // Custom paragraph component
              p: ({ children, ...props }: React.ComponentProps<'p'>) => (
                <p className={`leading-relaxed mb-4 ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
                  {children}
                </p>
              ),
              // Custom list components
              ul: ({ children, ...props }: React.ComponentProps<'ul'>) => (
                <ul className={`list-disc list-inside space-y-2 mb-4 ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
                  {children}
                </ul>
              ),
              ol: ({ children, ...props }: React.ComponentProps<'ol'>) => (
                <ol className={`list-decimal list-inside space-y-2 mb-4 ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
                  {children}
                </ol>
              ),
              li: ({ children, ...props }: React.ComponentProps<'li'>) => (
                <li className="leading-relaxed" {...props}>
                  {children}
                </li>
              ),
              // Custom blockquote component
              blockquote: ({ children, ...props }: React.ComponentProps<'blockquote'>) => (
                <blockquote className={`border-l-4 pl-4 py-2 my-4 italic ${isUser ? 'border-blue-300 bg-blue-400 bg-opacity-20 text-white' : 'border-indigo-500 bg-indigo-50 text-gray-700'}`} {...props}>
                  {children}
                </blockquote>
              ),
              // Custom table components
              table: ({ children, ...props }: React.ComponentProps<'table'>) => (
                <div className="overflow-x-auto my-4">
                  <table className="min-w-full divide-y divide-gray-200 border border-gray-300 rounded-lg" {...props}>
                    {children}
                  </table>
                </div>
              ),
              thead: ({ children, ...props }: React.ComponentProps<'thead'>) => (
                <thead className="bg-gray-50" {...props}>
                  {children}
                </thead>
              ),
              tbody: ({ children, ...props }: React.ComponentProps<'tbody'>) => (
                <tbody className="bg-white divide-y divide-gray-200" {...props}>
                  {children}
                </tbody>
              ),
              th: ({ children, ...props }: React.ComponentProps<'th'>) => (
                <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isUser ? 'text-blue-100' : 'text-gray-500'}`} {...props}>
                  {children}
                </th>
              ),
              td: ({ children, ...props }: React.ComponentProps<'td'>) => (
                <td className={`px-4 py-3 text-sm ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
                  {children}
                </td>
              ),
              // Custom link component
              a: ({ children, href, ...props }: React.ComponentProps<'a'>) => (
                <a
                  href={href}
                  className={`underline ${isUser ? 'text-blue-100 hover:text-white' : 'text-indigo-600 hover:text-indigo-800'}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  {...props}
                >
                  {children}
                </a>
              ),
              // Custom strong/bold component
              strong: ({ children, ...props }: React.ComponentProps<'strong'>) => (
                <strong className={`font-semibold ${isUser ? 'text-white' : 'text-gray-900'}`} {...props}>
                  {children}
                </strong>
              ),
              // Custom emphasis/italic component
              em: ({ children, ...props }: React.ComponentProps<'em'>) => (
                <em className={`italic ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
                  {children}
                </em>
              ),
            }}
          >
            {message.content || ''}
          </ReactMarkdown>
        </div>
 
        {/* Timestamp - Smaller and lighter */}
        <p className={`text-[0.65rem] mt-1.5 ${isUser ? 'text-blue-200' : 'text-gray-400'} text-right`}>
          {new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </p>
 
        {/* Intermediate steps dropdown - Render only for agent answers with steps */}
        {!isUser && message.type === 'answer' && hasIntermediateSteps && (
          <div className="mt-2 pt-2 border-t border-gray-200"> {/* Separator line */}
            <button
              onClick={() => setIsStepsOpen(!isStepsOpen)}
              className="text-[0.7rem] font-medium text-indigo-600 hover:text-indigo-800 focus:outline-none flex items-center"
              aria-expanded={isStepsOpen}
            >
              {isStepsOpen ? 'Hide steps' : 'Show steps...'}
              {/* Simple Chevron Icon */}
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-3 w-3 ml-1 transition-transform ${isStepsOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
                 <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Collapsible content - Render grouped steps */}
            {isStepsOpen && (
              <div className="mt-2 space-y-2 text-xs">
                {groupedAndNumberedSteps.map((groupedStep, index) => {
                  // --- Render Observation Step ---
                  if (groupedStep.type === 'observation' && groupedStep.data) {
                    const step = groupedStep.data;
                    return (
                      <div key={`obs-${step.created_at}-${index}`} className="p-2 bg-gray-50 rounded border border-gray-200">
                        <div className="flex items-center space-x-1.5 mb-1">
                          <span className="text-sm">🤔</span>
                          <span className="font-medium text-gray-700">Step {groupedStep.stepNumber}: Observation</span>
                        </div>
                        <SimpleCodeBlock title="Observation">
                          {step.content || '(No content)'}
                        </SimpleCodeBlock>
                      </div>
                    );
                  }
                  // --- Render Tool Step using ToolStepDisplay component ---
                  else if (groupedStep.type === 'tool' && groupedStep.input) {
                    return (
                      <ToolStepDisplay
                        key={`tool-${groupedStep.input.created_at}-${index}`}
                        stepNumber={groupedStep.stepNumber}
                        toolName={groupedStep.toolName || null} // Pass null if undefined
                        input={groupedStep.input}
                        output={groupedStep.output || null} // Pass null if undefined
                      />
                    );
                  }
                  return null;
                })}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;