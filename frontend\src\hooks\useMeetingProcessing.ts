import { useReducer, useCallback, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from './useAuth'; // Assuming this path is correct
import { useUserProfile } from './useUserProfile';
import { useRealtimeMeeting } from './useRealtimeMeeting';
import { useSmartPolling } from './useSmartPolling';
import { meetingReducer, initialMeetingState } from '../reducers/meetingReducer';
import {
  cleanTranscriptForDisplay,
  extractSpeakerIds,
  extractSpeakerLongestSegments,
} from '../utils/transcriptParser';
import {
  MeetingState,
  BackendReunion,
  ReunionRecord,
  AssociatedEntity,
  SpeakerAssignable,
} from '../types/meeting.types';

export function useMeetingProcessing() {
  const { reunionId: routeReunionId } = useParams<{ reunionId?: string }>();
  const { user, session } = useAuth();
  const { userProfile } = useUserProfile(user);
  const [state, dispatch] = useReducer(meetingReducer, initialMeetingState);
  const initialSetupDoneRef = useRef(false); // Ref to track initial setup

  // Refs for stable callbacks
  const sessionRef = useRef(session);
  const stateRef = useRef(state);

  useEffect(() => {
    sessionRef.current = session;
    stateRef.current = state;
  }, [session, state]);

  // Handle Realtime updates
  const handleRealtimeUpdate = useCallback((broadcastPayload: ReunionRecord | { record: ReunionRecord } | unknown) => {
    console.log('[useMeetingProcessing] Processing Realtime broadcast payload:', broadcastPayload);

    try {
      // Handle both direct ReunionRecord and broadcast payload formats
      let meetingData: ReunionRecord;

      if (broadcastPayload && typeof broadcastPayload === 'object' && 'record' in broadcastPayload) {
        // It's a broadcast payload
        meetingData = broadcastPayload.record as ReunionRecord;
      } else {
        // It's a direct ReunionRecord (from polling)
        meetingData = broadcastPayload as ReunionRecord;
      }

      // Validate meeting data
      if (!meetingData || !meetingData.id) {
        console.warn('[useMeetingProcessing] Received update without meeting data or id:', broadcastPayload);
        return;
      }

      // Validate that this update is for the current meeting
      if (stateRef.current.id && meetingData.id !== stateRef.current.id) {
        console.log(`[useMeetingProcessing] Update for different meeting (${meetingData.id}), ignoring`);
        return;
      }

      console.log('[useMeetingProcessing] Extracted meeting data from broadcast:', meetingData);

      if (meetingData.estado_procesamiento) {
        dispatch({ type: 'UPDATE_STATUS', status: meetingData.estado_procesamiento });

        if (meetingData.estado_procesamiento === 'pending_asignacion_speakers' && meetingData.transcripcion_raw) {
          console.log(`[useMeetingProcessing] 'pending_asignacion_speakers' received. Current step: ${stateRef.current.currentStep}. Meeting Data:`, meetingData);

          const displayTranscript = cleanTranscriptForDisplay(meetingData.transcripcion_raw);
          const speakers = extractSpeakerIds(meetingData.transcripcion_raw);

          dispatch({ type: 'SET_TRANSCRIPT', rawTranscript: meetingData.transcripcion_raw, displayTranscript: displayTranscript || '' });
          dispatch({ type: 'SET_SPEAKERS', speakers });

          if (meetingData.url_grabacion_publica && typeof meetingData.url_grabacion_publica === 'string') {
            const segments = extractSpeakerLongestSegments(meetingData.transcripcion_raw, meetingData.url_grabacion_publica);
            dispatch({ type: 'SET_AUDIO_SEGMENTS', segments });
          }

          // Ensure current user and associated entities are available as speakers
          const currentPotentialSpeakers = [...stateRef.current.potentialSpeakers];

          // Add current user if not already present
          if (user && !currentPotentialSpeakers.find(ps => ps.id === user.id && ps.tipo === 'usuario')) {
            currentPotentialSpeakers.push({
              id: user.id,
              nombre: userProfile?.nombre || user.email || 'Usuario actual', // Use full name from profile
              tipo: 'usuario',
            });
          }

          // Add associated entities as potential speakers if they're not already present
          stateRef.current.associatedEntities.forEach(entity => {
            if (entity.tipo === 'persona' || entity.tipo === 'leadContacto') {
              if (!currentPotentialSpeakers.find(ps => ps.id === entity.id && ps.tipo === entity.tipo)) {
                currentPotentialSpeakers.push({
                  id: entity.id,
                  nombre: entity.nombre,
                  tipo: entity.tipo as 'persona' | 'leadContacto',
                });
              }
            }
          });

          // Update potential speakers if we added any
          if (currentPotentialSpeakers.length !== stateRef.current.potentialSpeakers.length) {
            console.log('[useMeetingProcessing] Updated potential speakers:', currentPotentialSpeakers);
            dispatch({ type: 'SET_POTENTIAL_SPEAKERS', speakers: currentPotentialSpeakers });
          }

          if (stateRef.current.currentStep === 1.5) {
            console.log('[useMeetingProcessing] Transitioning to step 2.');
            dispatch({ type: 'SET_STEP', step: 2 });
          } else {
            console.log(`[useMeetingProcessing] Condition for step 2 not met. Current step: ${stateRef.current.currentStep}`);
          }
        }

        // Handle AI processing status
        if (meetingData.estado_procesamiento === 'procesando_ia') {
          console.log(`[useMeetingProcessing] 'procesando_ia' received. Current step: ${stateRef.current.currentStep}`);

          // Update status to reflect AI processing
          dispatch({ type: 'UPDATE_STATUS', status: 'procesando_ia' });

          // If we're in step 2, transition to step 2.5 (loading)
          if (stateRef.current.currentStep === 2) {
            console.log('[useMeetingProcessing] Transitioning to step 2.5 (AI processing).');
            dispatch({ type: 'SET_STEP', step: 2.5 });
          }
        }

        // Handle completion status
        if (meetingData.estado_procesamiento === 'completado') {
          console.log(`[useMeetingProcessing] 'completado' received. Current step: ${stateRef.current.currentStep}`);

          // Update status to reflect completion
          dispatch({ type: 'UPDATE_STATUS', status: 'completado' });

          // Update meeting data with final results
          if (meetingData.transcripcion_final && typeof meetingData.transcripcion_final === 'string') {
            dispatch({ type: 'SET_TRANSCRIPT', rawTranscript: meetingData.transcripcion_raw || '', displayTranscript: meetingData.transcripcion_final });
          }

          // If we're in step 2.5, transition to step 3
          if (stateRef.current.currentStep === 2.5) {
            console.log('[useMeetingProcessing] Transitioning to step 3 (results).');
            dispatch({ type: 'SET_STEP', step: 3 });
          }
        }

        // Handle error status
        if (meetingData.estado_procesamiento && meetingData.estado_procesamiento.startsWith('error')) {
          console.log(`[useMeetingProcessing] Error status received: ${meetingData.estado_procesamiento}`);

          // Update status to reflect error
          dispatch({ type: 'UPDATE_STATUS', status: meetingData.estado_procesamiento });
          dispatch({ type: 'SET_CONNECTION_ERROR', error: `Error en el procesamiento: ${meetingData.estado_procesamiento}` });
        }
      }
    } catch (error) {
      console.error('[useMeetingProcessing] Error handling realtime update:', error);
      dispatch({ type: 'SET_CONNECTION_ERROR', error: error instanceof Error ? error.message : 'Error processing update' });
    }
  }, [dispatch, user, userProfile?.nombre]); // Using refs for state access, user and userProfile needed for speaker assignment

  // Connection status handler
  const handleConnectionStatusChange = useCallback((status: MeetingState['connectionStatus']) => {
    dispatch({ type: 'SET_CONNECTION_STATUS', status });
  }, []);

  // Start polling handler
  const handleStartPolling = useCallback(() => {
    dispatch({ type: 'SET_CONNECTION_STATUS', status: 'polling' });
  }, []);

  // Use Realtime hook
  const { isConnected } = useRealtimeMeeting({
    meetingId: state.id,
    onUpdate: handleRealtimeUpdate,
    onConnectionStatusChange: handleConnectionStatusChange,
    onStartPolling: handleStartPolling,
    currentConnectionState: state.connectionStatus, // Pass the reactive state here
  });

  // Polling for transcription
  const {
    isPolling: isPollingTranscription,
  } = useSmartPolling({
    enabled: state.connectionStatus === 'polling' && state.status === 'pending_transcripcion' && !!state.id,
    url: `${import.meta.env.VITE_API_BASE_URL}/reuniones/${state.id}`,
    expectedValue: 'pending_asignacion_speakers',
    checkValue: (data: unknown) => (data as ReunionRecord).estado_procesamiento,
    onSuccess: (data) => {
      handleRealtimeUpdate(data as ReunionRecord);
    },
    onError: (error) => {
        dispatch({ type: 'SET_CONNECTION_ERROR', error: error.message });
    },
    headers: {
      'Authorization': `Bearer ${sessionRef.current?.access_token || ''}`,
    },
  });

  // Polling for AI processing
  const {
    isPolling: isPollingAI,
  } = useSmartPolling({
    enabled: state.connectionStatus === 'polling' && state.status === 'procesando_ia' && !!state.id,
    url: `${import.meta.env.VITE_API_BASE_URL}/reuniones/${state.id}`,
    expectedValue: 'completado',
    checkValue: (data: unknown) => (data as ReunionRecord).estado_procesamiento,
    onSuccess: (data) => {
      handleRealtimeUpdate(data as ReunionRecord);
    },
    onError: (error) => {
        dispatch({ type: 'SET_CONNECTION_ERROR', error: error.message });
    },
    headers: {
      'Authorization': `Bearer ${sessionRef.current?.access_token || ''}`,
    },
  });

  // Load meeting data
  const loadMeetingData = useCallback(async (id: string) => {
    // Guard to prevent re-fetching if data is already loaded/loading and not in an error state
    if (state.id === id && state.status && !state.status.startsWith('error_')) {
      console.log(`[useMeetingProcessing] Meeting ${id} data already present or loading (status: ${state.status}). Skipping redundant fetch.`);
      return;
    }
    console.log(`[useMeetingProcessing] Loading meeting data for id: ${id}`);

    if (!sessionRef.current?.access_token) {
      const authError = 'No authentication token available';
      dispatch({ type: 'SET_CONNECTION_ERROR', error: authError });
      throw new Error(authError);
    }

    dispatch({ type: 'SET_UPLOADING', isUploading: true }); // Using this to indicate loading data

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${id}`, {
        headers: { 'Authorization': `Bearer ${sessionRef.current.access_token}` },
      });

      if (!response.ok) {
        const errorBody = await response.json().catch(() => ({ detail: `Error: ${response.statusText}` }));
        const fetchError = errorBody.detail || `Error: ${response.statusText}`;
        dispatch({ type: 'SET_CONNECTION_ERROR', error: fetchError });
        throw new Error(fetchError);
      }

      const meetingData: BackendReunion = await response.json();

      // Process associated entities
      const associatedEntities: AssociatedEntity[] = [];
      const potentialSpeakers: SpeakerAssignable[] = [];

      // Add uploader as potential speaker
      if (meetingData.uploader_info) {
        // Use userProfile name if this is the current user, otherwise use uploader_info
        const uploaderName = (user && user.id === meetingData.uploader_info.id && userProfile?.nombre)
          ? userProfile.nombre
          : (meetingData.uploader_info.nombre || meetingData.uploader_info.email);

        potentialSpeakers.push({
          id: meetingData.uploader_info.id,
          nombre: uploaderName,
          tipo: 'usuario',
        });
      }

      // Process empresas
      meetingData.empresas_asociadas?.forEach(e => {
        associatedEntities.push({ id: e.id, nombre: e.nombre, tipo: 'empresa' });
      });

      // Process personas
      meetingData.personas_asociadas?.forEach(p => {
        const nombre = `${p.nombre} ${p.apellidos || ''}`.trim();
        associatedEntities.push({ id: p.id, nombre, tipo: 'persona' });
        potentialSpeakers.push({ id: p.id, nombre, tipo: 'persona' });
      });

      // Process leads empresas
      meetingData.leads_empresas_asociadas?.forEach(le => {
        associatedEntities.push({ id: le.id, nombre: le.nombre, tipo: 'leadEmpresa' });
      });

      // Process lead contactos
      meetingData.lead_contactos_asociados?.forEach(lc => {
        const nombre = `${lc.nombre} ${lc.apellidos || ''}`.trim();
        associatedEntities.push({ id: lc.id, nombre, tipo: 'leadContacto' });
        potentialSpeakers.push({ id: lc.id, nombre, tipo: 'leadContacto' });
      });

      // Process transcript if available
      let displayTranscript = null;
      let speakers: string[] = [];
      let audioSegments = new Map();

      if (meetingData.transcripcion_raw) {
        displayTranscript = cleanTranscriptForDisplay(meetingData.transcripcion_raw);
        speakers = extractSpeakerIds(meetingData.transcripcion_raw);

        if (meetingData.url_grabacion_publica) {
          audioSegments = extractSpeakerLongestSegments(
            meetingData.transcripcion_raw,
            meetingData.url_grabacion_publica as string
          );
        }
      }

      // Determine current step based on status
      let currentStep = 1;
      if (meetingData.estado_procesamiento === 'pending_asignacion_speakers') {
        currentStep = 2;
      } else if (meetingData.estado_procesamiento === 'completado' || meetingData.estado_procesamiento === 'procesando_ia') {
        currentStep = 3;
      }

      // Update state with all meeting data
      dispatch({
        type: 'LOAD_MEETING',
        payload: {
          id: meetingData.id,
          status: meetingData.estado_procesamiento || null,
          titulo: meetingData.titulo || null,
          observacionesIniciales: meetingData.observaciones_iniciales || null,
          fechaReunion: meetingData.fecha_reunion || null,
          entrevista: (meetingData as BackendReunion).entrevista || null,
          video: (meetingData as BackendReunion).video || null,
          rawTranscript: meetingData.transcripcion_raw || null,
          displayTranscript,
          identifiedSpeakers: speakers,
          audioSegments,
          associatedEntities,
          potentialSpeakers,
          currentStep,
        },
      });

    } catch (error) {
      console.error('[useMeetingProcessing] Error loading meeting:', error);
      // Error is already dispatched in the catch blocks above or thrown
      if (error instanceof Error && !state.connectionError) { // Dispatch if not already set
        dispatch({ type: 'SET_CONNECTION_ERROR', error: error.message });
      }
      throw error; // Re-throw for the caller to handle if needed
    } finally {
      dispatch({ type: 'SET_UPLOADING', isUploading: false });
    }
  }, [dispatch, state.id, state.status, state.connectionError, user, userProfile?.nombre]); // Added user and userProfile dependencies

  // Load meeting on mount if routeReunionId is present
  useEffect(() => {
    if (routeReunionId) {
      // Loading an existing meeting
      initialSetupDoneRef.current = true; // Mark setup as done because we are loading
      loadMeetingData(routeReunionId).catch(error => {
        alert(`Error al cargar la reunión: ${error.message}`);
        // Potentially navigate away or show a more persistent error message in UI
      });
    } else if (!state.id && !initialSetupDoneRef.current) {
      // New meeting path, no existing state.id, and initial setup/reset hasn't run
      console.log('[useMeetingProcessing] Initializing for new meeting, dispatching RESET.');
      dispatch({ type: 'RESET' });

      // Add current user as potential speaker for new meetings
      if (user) {
        const currentUserSpeaker = {
          id: user.id,
          nombre: userProfile?.nombre || user.email || 'Usuario actual',
          tipo: 'usuario' as const,
        };
        console.log('[useMeetingProcessing] Adding current user as potential speaker:', currentUserSpeaker);
        dispatch({ type: 'SET_POTENTIAL_SPEAKERS', speakers: [currentUserSpeaker] });
      }

      initialSetupDoneRef.current = true; // Mark initial setup/reset as done
    }
    // This effect should run when routeReunionId changes, or for the very initial setup.
    // loadMeetingData is a dependency for the routeReunionId case.
    // state.id is now included because it's used in the condition.
  }, [routeReunionId, loadMeetingData, state.id, user, userProfile?.nombre]);

  // Entity management functions
  const addAssociatedEntity = useCallback((entity: AssociatedEntity) => {
    dispatch({ type: 'ADD_ASSOCIATED_ENTITY', entity });

    // If it's a speakable entity, also add to potential speakers
    if (entity.tipo === 'persona' || entity.tipo === 'leadContacto') {
      const speakerEntity: SpeakerAssignable = {
        id: entity.id,
        nombre: entity.nombre,
        tipo: entity.tipo as 'persona' | 'leadContacto', // Type assertion
      };
      // Avoid adding duplicates to potentialSpeakers
      if (!state.potentialSpeakers.find(ps => ps.id === speakerEntity.id && ps.tipo === speakerEntity.tipo)) {
        dispatch({ type: 'SET_POTENTIAL_SPEAKERS', speakers: [...state.potentialSpeakers, speakerEntity] });
      }
    }
  }, [state.potentialSpeakers]);

  const removeAssociatedEntity = useCallback((id: string, tipo: AssociatedEntity['tipo']) => {
    dispatch({ type: 'REMOVE_ASSOCIATED_ENTITY', id, tipo });
    // Also remove from potential speakers if it was there
    if (tipo === 'persona' || tipo === 'leadContacto') {
        dispatch({
            type: 'SET_POTENTIAL_SPEAKERS',
            speakers: state.potentialSpeakers.filter(ps => !(ps.id === id && ps.tipo === tipo))
        });
    }
  }, [state.potentialSpeakers]);

  const updateSpeakerAssignment = useCallback((speakerTag: string, assignment: SpeakerAssignable) => {
    dispatch({ type: 'UPDATE_ASSIGNMENT', speakerTag, assignment });
  }, []);

  const setCurrentStep = useCallback((step: number) => {
    dispatch({ type: 'SET_STEP', step });
  }, []);

  const setUploadProgress = useCallback((progress: number) => {
    dispatch({ type: 'SET_UPLOAD_PROGRESS', progress });
  }, []);

  const setUploading = useCallback((isUploading: boolean) => {
    dispatch({ type: 'SET_UPLOADING', isUploading });
  }, []);

  const setMeetingId = useCallback((id: string) => {
    dispatch({ type: 'SET_ID', id });
  }, []);

  const updateEntrevista = useCallback(async (entrevista: boolean) => {
    if (!state.id || !sessionRef.current?.access_token) {
      throw new Error('No meeting ID or authentication token available');
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${state.id}/entrevista`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionRef.current.access_token}`,
        },
        body: JSON.stringify({ entrevista }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Error updating entrevista field');
      }

      // Update local state
      dispatch({ type: 'SET_ENTREVISTA', entrevista });

      console.log(`[useMeetingProcessing] Successfully updated entrevista to ${entrevista} for meeting ${state.id}`);
    } catch (error) {
      console.error('[useMeetingProcessing] Error updating entrevista:', error);
      throw error;
    }
  }, [state.id]);

  const updateVideo = useCallback(async (video: boolean) => {
    if (!state.id || !sessionRef.current?.access_token) {
      throw new Error('No meeting ID or authentication token available');
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${state.id}/video`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionRef.current.access_token}`,
        },
        body: JSON.stringify({ video }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Error updating video field');
      }

      // Update local state
      dispatch({ type: 'SET_VIDEO', video });

      console.log(`[useMeetingProcessing] Successfully updated video to ${video} for meeting ${state.id}`);
    } catch (error) {
      console.error('[useMeetingProcessing] Error updating video:', error);
      throw error;
    }
  }, [state.id]);

  return {
    state,
    isConnected, // From useRealtimeMeeting
    isPolling: isPollingTranscription || isPollingAI, // Combined polling state

    // Actions/dispatchers for UI components to use
    addAssociatedEntity,
    removeAssociatedEntity,
    updateSpeakerAssignment,
    updateEntrevista,
    updateVideo,
    setCurrentStep,
    setUploadProgress,
    setUploading,
    setMeetingId,
    loadMeetingData, // Expose if direct loading is needed from UI

    // Auth data for convenience if needed by UI components directly
    user,
    session,
  };
}