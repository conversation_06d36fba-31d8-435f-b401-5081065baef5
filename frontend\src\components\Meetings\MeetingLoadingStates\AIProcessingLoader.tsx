import React from 'react';
import LoadingSpinner from '../../UI/LoadingSpinner'; // Assuming this path is correct
import { MeetingState } from '../../../types/meeting.types'; // For connectionMethod type

interface AIProcessingLoaderProps {
  status: string | null; // e.g., 'procesando_ia'
  connectionMethod: MeetingState['connectionStatus'];
}

const AIProcessingLoader: React.FC<AIProcessingLoaderProps> = ({ status, connectionMethod }) => {
  const getStatusMessage = (currentStatus: string | null) => {
    if (currentStatus === 'procesando_ia') {
      return {
        title: 'Procesando con IA...',
        description: 'Generando resumen automático y extrayendo puntos clave de la reunión.',
        estimated: 'Tiempo estimado: 30-60 segundos'
      };
    }
    return {
      title: 'Procesando Reunión...',
      description: 'Esperando la finalización del procesamiento con IA.',
      estimated: 'Esto puede tardar unos segundos.'
    };
  };

  const messageDetails = getStatusMessage(status);

  return (
    <div className="bg-white p-6 shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-700 mb-4">{messageDetails.title}</h2>
      <div className="my-6">
        <LoadingSpinner
          message={messageDetails.title}
          subMessage={messageDetails.description}
          connectionStatus={connectionMethod} // Pass the connection status
          estimatedTime={messageDetails.estimated}
        />
      </div>
      <p className="text-sm text-gray-600 text-center">
        Por favor, espera mientras se completa el análisis. La página se actualizará automáticamente.
      </p>
    </div>
  );
};

export default AIProcessingLoader;