import logging
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, BackgroundTasks
from typing import Annotated # Use Annotated for Form fields with File
from uuid import UUID
 
from supabase import AsyncClient # Correct import for the async client
from app.core.database import get_supabase_client # Import Supabase client dependency
from app.models import chat as chat_models
from app.models.doc_export import DocExport # Import DocExport model
from app.core.security import get_current_user_id # Import auth dependency
from app.services import webhook_service, transcription_service, doc_export_service, thread_service, thread_metadata_service
from app.services.doc_export_service import process_send_to_docs # Import the background task function
 
logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/messages", status_code=status.HTTP_202_ACCEPTED)
async def send_chat_message(
    payload: chat_models.ChatMessageCreate,
    supabase: AsyncClient = Depends(get_supabase_client), # Correct type hint
    current_user_id: str = Depends(get_current_user_id) # Protect endpoint and get user ID
):
    """
    Receives a chat message from the user, stores it, and triggers the n8n webhook.
    Requires authentication.
    """
    logger.info(f"Received message for thread {payload.thread_id} from user {current_user_id}")

    # 1. Validate input further if needed (Pydantic already does basic validation)

    # 2. Ensure the user_id from the token matches the payload or use the token's user_id
    # For security, always trust the user_id from the verified token.
    # Convert UUID from payload to string for comparison if necessary
    if str(payload.user_id) != current_user_id:
         logger.warning(f"Payload user_id {payload.user_id} does not match authenticated user_id {current_user_id}. Using authenticated user_id.")
         # Depending on policy, you might raise an error or just use the authenticated ID
         # If you need to update the payload object:
         # payload.user_id = UUID(current_user_id) # Ensure type match if payload is used later

    # 3. Prepare data for insertion into 'threads' table
    message_data_to_insert = {
        "thread_id": payload.thread_id,
        "content": payload.content,
        "agent_id": str(payload.agent_id), # Ensure UUID is converted to string if DB expects text
        "user_id": current_user_id, # Use authenticated user ID (already a string from JWT 'sub')
        "type": "user", # Mark this as a user message
        "from": "User" # Mark sender as User
        # 'message_id' and 'created_at' will likely be handled by DB defaults/triggers
    }

    # 4. Store the user message in the database
    try:
        # supabase-py client's execute() is synchronous, remove await
        insert_response = supabase.table("threads").insert(message_data_to_insert).execute()
        # The execute() call above will raise an exception on error, caught by the except block.
        # No need for explicit error checking on the response object itself.
        # We can log the response data if needed for debugging RLS issues etc.
        logger.debug(f"Supabase insert response data for thread {payload.thread_id}: {insert_response.data}")

        logger.info(f"Stored user message for thread {payload.thread_id}")

    except Exception as e:
        logger.exception(f"Failed to store user message for thread {payload.thread_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to store message")

    # 4.5. Ensure thread metadata exists (create if this is the first message)
    try:
        await thread_metadata_service.ensure_thread_metadata_exists(payload.thread_id)
        logger.info(f"Thread metadata ensured for thread {payload.thread_id}")
    except Exception as e:
        # Log the error but don't fail the request, as the message is already stored
        logger.warning(f"Failed to ensure thread metadata for thread {payload.thread_id}: {e}")
        # Continue with the rest of the flow

    # 5. Prepare payload for webhook
    # Ensure user_id in webhook_data is the correct type (UUID) if the model expects it
    webhook_data = chat_models.WebhookPayload(
        thread_id=payload.thread_id,
        agent_id=payload.agent_id,
        message=payload.content,
        user_id=UUID(current_user_id) # Convert authenticated user_id string back to UUID for the model
    )

    # 6. Trigger the n8n webhook (using the stubbed service)
    try:
        # Consider running this in a background task for production
        await webhook_service.trigger_n8n_webhook(webhook_data.model_dump()) # Pass dict representation
        logger.info(f"Triggered n8n webhook for thread {payload.thread_id}")
    except HTTPException as http_exc:
        # Log the error but don't fail the request, as the message is stored.
        # Re-raise if webhook failure should be reported to client? For now, just log.
        logger.error(f"Webhook trigger failed for thread {payload.thread_id}. Status: {http_exc.status_code}, Detail: {http_exc.detail}")
    except Exception as e:
        # Log other unexpected errors
        logger.exception(f"Failed to trigger n8n webhook for thread {payload.thread_id}: {e}")
        # If webhook trigger is critical, re-raise or raise a specific HTTPException

    return {"message": "Message received and processing started"}


@router.post("/transcribe", response_model=chat_models.TranscriptionResponse)
async def transcribe_audio(
    # Option 1: Receive file directly
    audio_file: UploadFile = File(...),
    # Option 2: Receive base64 data (less ideal for large files)
    # request: chat_models.TranscriptionRequest,
    current_user_id: str = Depends(get_current_user_id) # Protect endpoint
):
    """
    Receives an audio file, sends it to OpenAI for transcription, and returns the text.
    Requires authentication.
    """
    logger.info(f"Received audio file for transcription from user {current_user_id}: {audio_file.filename}")

    # Validate file type/size if needed
    if not audio_file.content_type.startswith("audio/"):
         raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid file type, expected audio.")

    try:
        # Call the transcription service directly with the UploadFile object
        transcribed_text = await transcription_service.transcribe_audio(audio_file)
        logger.info(f"Audio transcribed successfully for user {current_user_id}, file: {audio_file.filename}")
        return chat_models.TranscriptionResponse(text=transcribed_text)
    except HTTPException as http_exc:
        # Re-raise HTTPExceptions from the service (e.g., API key missing, API error)
        logger.error(f"Transcription service failed for user {current_user_id}, file: {audio_file.filename}. Status: {http_exc.status_code}, Detail: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors
        logger.exception(f"Unexpected error during transcription for user {current_user_id}, file: {audio_file.filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during transcription."
        )
    finally:
        # Ensure the file is closed even if errors occur
        await audio_file.close()

@router.post("/messages/{message_id}/send_to_docs", status_code=status.HTTP_202_ACCEPTED)
async def send_message_to_google_docs(
    message_id: UUID, # This is the ID of the specific message row in 'threads' table
    background_tasks: BackgroundTasks,
    supabase: AsyncClient = Depends(get_supabase_client), # Correct type hint
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Initiates the process of sending a specific agent message's content to Google Docs via n8n.
    """
    logger.info(f"User {current_user_id} initiated 'send_to_docs' for message {message_id}")

    # 1. Fetch the message content and verify ownership/type
    # Assuming message_id is the primary key of the 'threads' table row
    try:
        # Remove 'await' as .execute() after .maybe_single() might be synchronous
        message_response = supabase.table("threads").select("content, type, user_id").eq("id", str(message_id)).maybe_single().execute()
        if not message_response.data:
            logger.warning(f"Message {message_id} not found for send_to_docs request by user {current_user_id}.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found.")
        
        message_data = message_response.data
        # Ensure the message belongs to an interaction involving the current user
        # This check might need refinement based on how user_id is set for agent messages
        # For now, we assume the thread is associated with the user.
        # A more robust check would be to verify thread ownership via thread_service.check_thread_authorization if applicable.

        if message_data["type"] != "answer": # Only allow 'answer' type messages from agent
            logger.warning(f"Message {message_id} is not an agent answer. Type: {message_data['type']}. Request by {current_user_id}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Only agent answers can be sent to docs.")
        
        message_content = message_data["content"]
        if not message_content:
            logger.warning(f"Message {message_id} has no content. Request by {current_user_id}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Message has no content to send.")

    except HTTPException:
        raise # Re-raise HTTPException
    except Exception as e:
        logger.exception(f"Error fetching message {message_id} for send_to_docs: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error retrieving message details.")

    # 2. Check if an export request already exists for this message_id (threads_row_id) and user_id
    existing_export = await doc_export_service.get_doc_export_by_threads_row_id(
        db=supabase, user_id=UUID(current_user_id), threads_row_id=message_id
    )
    if existing_export:
        if existing_export.status == "pending":
            logger.info(f"Send_to_docs for message {message_id} by user {current_user_id} is already pending.")
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Request is already being processed.")
        if existing_export.status == "success":
            logger.info(f"Send_to_docs for message {message_id} by user {current_user_id} has already succeeded.")
            # Optionally return the existing URL or just indicate completion
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Document has already been created for this message.")
        # If 'error', allow retry by creating a new request.

    # 3. Create a 'pending' doc_exports record
    doc_export_request = await doc_export_service.create_doc_export_request(
        db=supabase, user_id=UUID(current_user_id), threads_row_id=message_id
    )

    if not doc_export_request or not doc_export_request.id:
        logger.error(f"Failed to create doc_export_request for message {message_id}, user {current_user_id}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to initiate document export process.")

    # 4. Add the background task
    # Pass the necessary arguments to the background task
    # Note: We cannot pass the 'supabase' client directly as it might not be pickleable
    # or suitable for background tasks depending on implementation details.
    # Instead, the background task function itself should acquire the client if needed,
    # or we pass necessary primitives like IDs and content.
    # Re-checking process_send_to_docs signature in doc_export_service.py...
    # It expects db_client, export_id, message_content.
    # Passing the client might work if BackgroundTasks handles it correctly, but let's be cautious.
    # For now, assuming it works as implemented earlier. If issues arise, refactor needed.
    background_tasks.add_task(
        process_send_to_docs,
        db_client=supabase, # Pass the Supabase client instance
        export_id=doc_export_request.id,
        message_content=message_content
    )
    logger.info(f"Background task added for doc_export_request {doc_export_request.id} (message {message_id})")

    return {"message": "Document export process initiated."}


@router.delete("/messages/{message_id}", status_code=status.HTTP_200_OK)
async def delete_chat_message(
    message_id: UUID,
    supabase: AsyncClient = Depends(get_supabase_client),
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Delete a specific chat message and all related messages with the same message_id.
    Users can delete messages that belong to them (both user messages and agent responses
    in their conversations, as agent messages share the same user_id as the requesting user).
    """
    logger.info(f"User {current_user_id} requested deletion of message {message_id}")

    try:
        # 1. First, get the message to verify ownership and get message_id for related messages
        message_response = supabase.table("threads").select("message_id, user_id, thread_id").eq("id", str(message_id)).maybe_single().execute()

        if not message_response.data:
            logger.warning(f"Message {message_id} not found for deletion request by user {current_user_id}")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")

        message_data = message_response.data

        # 2. Verify ownership - both user messages and agent responses have the same user_id
        if message_data["user_id"] != current_user_id:
            logger.warning(f"User {current_user_id} attempted to delete message {message_id} owned by {message_data['user_id']}")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You can only delete your own messages")

        # 3. Delete all messages with the same message_id (includes intermediate steps)
        if message_data["message_id"]:
            delete_response = supabase.table("threads").delete().eq("message_id", message_data["message_id"]).eq("user_id", current_user_id).execute()
            deleted_count = len(delete_response.data) if delete_response.data else 0
            logger.info(f"Deleted {deleted_count} messages with message_id {message_data['message_id']} for user {current_user_id}")
        else:
            # If no message_id, delete just this specific message
            delete_response = supabase.table("threads").delete().eq("id", str(message_id)).eq("user_id", current_user_id).execute()
            deleted_count = len(delete_response.data) if delete_response.data else 0
            logger.info(f"Deleted single message {message_id} for user {current_user_id}")

        if deleted_count == 0:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No messages were deleted")

        return {"message": f"Successfully deleted {deleted_count} message(s)", "deleted_count": deleted_count}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting message {message_id} for user {current_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while deleting the message: {str(e)}"
        )