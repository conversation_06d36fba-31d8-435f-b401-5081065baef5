import React, { useState, useEffect } from 'react';
import { useChat } from '../../hooks/useChat'; // Corrected import path
import { useAuth } from '../../hooks/useAuth'; // Corrected import path

// Define a type for the Agent data expected from the backend API
// Based on backend/app/models/agent.py::Agent
interface Agent {
  id: string; // UUID as string
  nombre: string;
  descripcion?: string | null;
  activo: boolean;
  // Add other fields if needed for display
}

// Define types for Tool and Companion based on backend models
interface Tool {
    id: string;
    tool_name: string;
    tool_description?: string | null;
    // Add other fields if needed
}

// Use the base Agent type for Companions
type Companion = Agent;

// Define the structure for AgentDetails response
interface AgentDetails extends Agent {
    tools: Tool[];
    companeros: Companion[];
}


const AgentSelector: React.FC = () => {
  const { selectedAgentId, setSelectedAgentId } = useChat();
  const { session } = useAuth(); // Get session for token
  const [agents, setAgents] = useState<Agent[]>([]);
  // Rename state for clarity
  const [isLoadingAgents, setIsLoadingAgents] = useState<boolean>(false);
  const [agentsError, setAgentsError] = useState<string | null>(null);

  // State for agent details dropdown
  const [agentDetails, setAgentDetails] = useState<AgentDetails | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState<boolean>(false);
  const [detailsError, setDetailsError] = useState<string | null>(null);
  const [isInfoOpen, setIsInfoOpen] = useState<boolean>(false); // State for dropdown visibility


  // Fetch active agents when the component mounts or session changes
  useEffect(() => {
    const fetchAgents = async () => {
      // Clear state before fetching
      setAgents([]);
      setAgentsError(null);
      setIsLoadingAgents(true);

      if (!session?.access_token) {
        setAgentsError("Not authenticated.");
        setIsLoadingAgents(false);
        setSelectedAgentId(null); // Clear selection if not authenticated
        return;
      }

      try {
        // Call the backend API endpoint GET /api/v1/agents?activo=true
        // Construct full URL using environment variable
        const apiUrl = `${import.meta.env.VITE_API_BASE_URL}/agents?activo=true`;
        console.log("Fetching agents from:", apiUrl); // Log the URL being fetched
        const response = await fetch(apiUrl, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || 'Failed to fetch agents');
        }

        const data: Agent[] = await response.json();
        setAgents(data);

        // Selection validation is handled in the render logic to preserve user choice

      } catch (err) {
        console.error("Error fetching agents:", err);
        setAgentsError(err instanceof Error ? err.message : 'An unknown error occurred');
        setAgents([]); // Clear agents on error
        setSelectedAgentId(null); // Clear selection on error
      } finally {
        setIsLoadingAgents(false);
      }
    };

    fetchAgents();
    // Reset details when session changes
    setAgentDetails(null);
    setIsInfoOpen(false);
  }, [session, setSelectedAgentId]); // setSelectedAgentId is stable from useChat

  // Fetch agent details when selectedAgentId changes
  useEffect(() => {
    const fetchAgentDetails = async () => {
        // Clear previous state
        setAgentDetails(null);
        setDetailsError(null);
        setIsLoadingDetails(false); // Reset loading state initially
        setIsInfoOpen(false); // Close dropdown when agent changes

        if (!selectedAgentId || !session?.access_token) {
            return; // Exit if no agent selected or not logged in
        }

        setIsLoadingDetails(true); // Set loading true before fetch

        try {
            console.log(`Fetching details for agent ${selectedAgentId}`);
            // Construct full URL using environment variable
            const detailApiUrl = `${import.meta.env.VITE_API_BASE_URL}/agents/${selectedAgentId}`;
            console.log(`Fetching details from: ${detailApiUrl}`); // Log the URL being fetched
            const response = await fetch(detailApiUrl, {
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
                throw new Error(errorData.detail || `Failed to fetch details for agent ${selectedAgentId}`);
            }

            const data: AgentDetails = await response.json();
            setAgentDetails(data);
            console.log("Agent details fetched:", data);

        } catch (err) {
            console.error(`Error fetching details for agent ${selectedAgentId}:`, err);
            setDetailsError(err instanceof Error ? err.message : 'An unknown error occurred');
            setAgentDetails(null); // Clear details on error
        } finally {
            setIsLoadingDetails(false);
        }
    };

    fetchAgentDetails();
  }, [selectedAgentId, session]); // Re-fetch when selected agent or session changes


  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newAgentId = event.target.value || null;
    console.log("Agent selected via dropdown:", newAgentId);
    setSelectedAgentId(newAgentId);
    // Let useEffect handle fetching details and closing dropdown
  };

  // Check if current selection is valid
  const isSelectionValid = !selectedAgentId || agents.some(agent => agent.id === selectedAgentId);

  const toggleInfoDropdown = () => {
      setIsInfoOpen(prev => !prev);
  }

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="agent-select" className="block text-sm font-medium text-gray-700 mb-2">
          Seleccionar Agente
        </label>
        {isLoadingAgents && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
            <p className="text-sm text-gray-500">Cargando agentes...</p>
          </div>
        )}
        {agentsError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">Error: {agentsError}</p>
          </div>
        )}
        {!isLoadingAgents && !agentsError && (
          <>
            <select
              id="agent-select"
              value={isSelectionValid ? (selectedAgentId || '') : ''}
              onChange={handleSelectChange}
              disabled={isLoadingAgents}
              className="block w-full pl-3 pr-10 py-4 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 rounded-lg shadow-sm disabled:bg-gray-100 transition-colors min-h-[44px]"
            >
              <option value="" disabled={agents.length > 0}>
                {agents.length === 0 ? 'No hay agentes activos' : '-- Selecciona un Agente --'}
              </option>
              {agents.map((agent) => (
                <option key={agent.id} value={agent.id}>
                  {agent.nombre}
                </option>
              ))}
            </select>
            {!isSelectionValid && (
              <div className="mt-2 bg-yellow-50 border border-yellow-200 rounded-md p-2">
                <p className="text-sm text-yellow-600">El agente seleccionado ya no está disponible. Por favor, selecciona otro agente.</p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Agent Info Dropdown */}
      {selectedAgentId && !isLoadingAgents && !agentsError && (
        <div>
          <button
            onClick={toggleInfoDropdown}
            disabled={isLoadingDetails}
            className="w-full flex items-center justify-between p-4 text-sm font-medium text-indigo-600 hover:text-indigo-800 bg-indigo-50 hover:bg-indigo-100 rounded-lg border border-indigo-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] active:scale-95"
            aria-expanded={isInfoOpen}
            aria-controls="agent-info-content"
          >
            <span>Información del Agente</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 transition-transform ${isInfoOpen ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isInfoOpen && (
            <div id="agent-info-content" className="mt-3 p-4 border border-gray-200 rounded-lg bg-white shadow-sm space-y-3">
              {isLoadingDetails && (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                  <p className="text-sm text-gray-500">Cargando detalles...</p>
                </div>
              )}
              {detailsError && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-sm text-red-600">Error: {detailsError}</p>
                </div>
              )}
              {agentDetails && !isLoadingDetails && !detailsError && (
                <div className="space-y-3">
                  <div>
                    <p className="text-sm"><span className="font-semibold text-gray-700">Descripción:</span></p>
                    <p className="text-sm text-gray-600 mt-1">{agentDetails.descripcion || 'Sin descripción disponible'}</p>
                  </div>

                  <div>
                    <p className="font-semibold text-gray-700 text-sm mb-2">Herramientas:</p>
                    {agentDetails.tools.length > 0 ? (
                      <div className="space-y-1">
                        {agentDetails.tools.map(tool => (
                          <div key={tool.id} className="bg-gray-50 rounded-md p-2">
                            <p className="text-sm font-medium text-gray-800">{tool.tool_name}</p>
                            {tool.tool_description && (
                              <p className="text-xs text-gray-600 mt-1">{tool.tool_description}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">Sin herramientas asociadas</p>
                    )}
                  </div>

                  <div>
                    <p className="font-semibold text-gray-700 text-sm mb-2">Compañeros:</p>
                    {agentDetails.companeros.length > 0 ? (
                      <div className="space-y-1">
                        {agentDetails.companeros.map(comp => (
                          <div key={comp.id} className="bg-gray-50 rounded-md p-2">
                            <p className="text-sm font-medium text-gray-800">{comp.nombre}</p>
                            {comp.descripcion && (
                              <p className="text-xs text-gray-600 mt-1">{comp.descripcion}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">Sin compañeros asociados</p>
                    )}
                  </div>
                </div>
              )}
              {!agentDetails && !isLoadingDetails && !detailsError && (
                <p className="text-sm text-gray-500 italic">No hay detalles disponibles para este agente</p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AgentSelector;