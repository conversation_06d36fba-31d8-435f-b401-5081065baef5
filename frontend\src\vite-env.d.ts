/// <reference types="vite/client" />

declare global {
  interface ImportMetaEnv {
    readonly VITE_SUPABASE_URL: string
    readonly VITE_SUPABASE_ANON_KEY: string
    readonly VITE_API_URL: string
    readonly VITE_ENVIRONMENT: string
    readonly MODE: string
    readonly DEV: boolean
    readonly PROD: boolean
    readonly BASE_URL: string
    readonly SSR: boolean
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}
