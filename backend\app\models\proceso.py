from pydantic import BaseModel, Field
from typing import Optional, List, Union, Dict, Any
from uuid import UUID
from datetime import datetime
from enum import Enum

# Import related models
from .crm_empresa import Empresa
from .user import User

class TipoProceso(str, Enum):
    """Enum for proceso types."""
    INTERNO = "Interno"
    EXTERNO = "Externo"

class HerramientaUtilizada(BaseModel):
    """Model for herramientas utilizadas."""
    nombre_herramienta: str
    uso_principal: str

class ProcesoBase(BaseModel):
    """Base model for proceso data."""
    nombre: str = Field(..., description="Nombre del proceso")
    descripcion: Optional[str] = Field(None, description="Descripción del proceso")
    tipo_proceso: TipoProceso = Field(TipoProceso.INTERNO, description="Tipo de proceso")
    empresa_id: Optional[UUID] = Field(None, description="Empresa asociada al proceso")
    departamento_id: Optional[UUID] = Field(None, description="Departamento asociado")
    persona_id: Optional[UUID] = Field(None, description="Persona responsable")
    es_repetitivo: Optional[bool] = Field(False, description="Si el proceso es repetitivo")
    es_cuello_botella: Optional[bool] = Field(False, description="Si es un cuello de botella")
    es_manual: Optional[bool] = Field(True, description="Si el proceso es manual")
    valor_negocio: Optional[str] = Field(None, description="Valor de negocio del proceso")
    complejidad_automatizacion: Optional[str] = Field(None, description="Complejidad para automatizar")
    prioridad_automatizacion: Optional[str] = Field(None, description="Prioridad de automatización")
    tiempo_estimado_manual: Optional[int] = Field(None, description="Tiempo estimado en minutos")
    frecuencia: Optional[str] = Field(None, description="Frecuencia de ejecución")
    proceso_plantilla_origen_id: Optional[UUID] = Field(None, description="Plantilla de origen si aplica")
    herramientas_utilizadas: Optional[Union[List[HerramientaUtilizada], Dict[str, Any]]] = Field(None, description="Herramientas utilizadas")
    info_adicional: Optional[str] = Field(None, description="Información adicional")

class ProcesoCreate(ProcesoBase):
    """Model for creating a new proceso."""
    pass

class ProcesoUpdate(BaseModel):
    """Model for updating proceso data."""
    nombre: Optional[str] = None
    descripcion: Optional[str] = None
    tipo_proceso: Optional[TipoProceso] = None
    empresa_id: Optional[UUID] = None
    departamento_id: Optional[UUID] = None
    persona_id: Optional[UUID] = None
    es_repetitivo: Optional[bool] = None
    es_cuello_botella: Optional[bool] = None
    es_manual: Optional[bool] = None
    valor_negocio: Optional[str] = None
    complejidad_automatizacion: Optional[str] = None
    prioridad_automatizacion: Optional[str] = None
    tiempo_estimado_manual: Optional[int] = None
    frecuencia: Optional[str] = None
    proceso_plantilla_origen_id: Optional[UUID] = None
    herramientas_utilizadas: Optional[Union[List[HerramientaUtilizada], Dict[str, Any]]] = None
    info_adicional: Optional[str] = None

class ProcesoInDBBase(ProcesoBase):
    """Base model for proceso data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Proceso(ProcesoInDBBase):
    """Model representing a proceso as returned from the API."""
    # Populated by service layer
    empresa: Optional[Empresa] = None
    responsable_usuario: Optional[User] = None
    plantilla_origen: Optional[str] = Field(None, description="Nombre de la plantilla origen")
    
    # Related data
    proyectos_asociados: Optional[List[dict]] = Field(default_factory=list, description="Proyectos asociados")
    total_tareas: Optional[int] = Field(0, description="Total de tareas relacionadas")

class ProcesoInDB(ProcesoInDBBase):
    """Model representing a proceso stored in the database."""
    pass

# Response models
class ProcesoResponse(Proceso):
    """Response model for proceso API endpoints."""
    pass

class ProcesoListResponse(BaseModel):
    """Response model for proceso list endpoints."""
    procesos: List[Proceso]
    total: int
    page: int
    size: int

# Dashboard specific models
class ProcesoSummary(BaseModel):
    """Simplified proceso model for dashboard."""
    id: UUID
    nombre: str
    tipo_proceso: TipoProceso
    empresa_nombre: Optional[str] = None
    responsable_nombre: Optional[str] = None
    tiempo_estimado_manual: Optional[int] = None
    es_cuello_botella: bool = False

# Filter models
class ProcesoFilters(BaseModel):
    """Model for proceso filtering."""
    tipo_proceso: Optional[TipoProceso] = None
    empresa_id: Optional[UUID] = None
    es_cuello_botella: Optional[bool] = None
    search: Optional[str] = None
