import React from 'react';

interface LoadingSpinnerProps {
  message?: string; // Optional for mini version
  subMessage?: string;
  connectionStatus?: 'SUBSCRIBED' | 'TIMED_OUT' | 'CLOSED' | 'CHANNEL_ERROR' | 'disconnected' | 'error' | 'polling';
  estimatedTime?: string;
  size?: 'default' | 'small';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message,
  subMessage,
  connectionStatus,
  estimatedTime,
  size = 'default' // Default to 'default' size
}) => {
  const getConnectionStatusInfo = () => {
    if (!connectionStatus) return null;
    
    switch (connectionStatus) {
      case 'SUBSCRIBED':
        return { color: 'text-green-600', text: 'Conectado (Realtime)', icon: '🟢', bgColor: 'bg-green-50' };
      case 'polling':
        return { color: 'text-yellow-600', text: 'Consultando (Polling)', icon: '🟡', bgColor: 'bg-yellow-50' };
      case 'disconnected':
      case 'CLOSED':
        return { color: 'text-gray-500', text: 'Desconectado', icon: '⚪', bgColor: 'bg-gray-100' };
      // 'connecting' is not a direct status we pass anymore with the simplified hook
      case 'error':
      case 'TIMED_OUT':
      case 'CHANNEL_ERROR':
        return { color: 'text-red-700', text: 'Error de Conexión', icon: '🔴', bgColor: 'bg-red-100' };
      default:
        return null; // Or a default unknown state
    }
  };

  const statusInfo = getConnectionStatusInfo();

  return (
    <div className="flex flex-col items-center justify-center py-8 px-4">
      {/* Main Spinner */}
      <div className="relative">
        <div
          className={`animate-spin rounded-full border-gray-200 ${
            size === 'small' ? 'h-4 w-4 border-2' : 'h-16 w-16 border-4'
          }`}
        ></div>
        <div
          className={`animate-spin rounded-full border-indigo-600 border-t-transparent absolute top-0 left-0 ${
            size === 'small' ? 'h-4 w-4 border-2' : 'h-16 w-16 border-4'
          }`}
        ></div>
      </div>
      
      {size === 'default' && (
        <>
          {/* Main Message */}
          {message && <h3 className="text-lg font-medium text-gray-700 mb-2 mt-4 text-center">{message}</h3>}
          
          {/* Sub Message */}
          {subMessage && (
            <p className="text-sm text-gray-500 text-center mb-3 max-w-md">{subMessage}</p>
          )}
          
          {/* Estimated Time */}
          {estimatedTime && (
            <p className="text-xs text-gray-400 text-center mb-3">{estimatedTime}</p>
          )}
          
          {/* Connection Status */}
          {statusInfo && (
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusInfo.bgColor}`}>
              <span className="text-sm">{statusInfo.icon}</span>
              <span className={`text-xs font-medium ${statusInfo.color}`}>
                {statusInfo.text}
              </span>
            </div>
          )}
          
          {/* Loading Animation Dots */}
          <div className="flex space-x-1 mt-4">
            <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </>
      )}
    </div>
  );
};

export default LoadingSpinner;