from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal

# Import related models for relationships
from .crm_empresa import Empresa
from .user import User

class ProyectoBase(BaseModel):
    """Base model for proyecto data."""
    nombre: str = Field(..., description="Nombre del proyecto")
    descripcion: Optional[str] = Field(None, description="Descripción detallada del proyecto")
    objetivo: Optional[str] = Field(None, description="Objetivo principal del proyecto")
    estado: Optional[str] = Field("Planificado", description="Estado actual del proyecto")
    fecha_inicio: Optional[date] = Field(None, description="Fecha de inicio del proyecto")
    fecha_fin_estimada: Optional[date] = Field(None, description="Fecha estimada de finalización")
    fecha_fin_real: Optional[date] = Field(None, description="Fecha real de finalización")
    presupuesto: Optional[Decimal] = Field(None, description="Presupuesto asignado")
    prioridad: Optional[str] = Field("Media", description="Prioridad del proyecto")
    progreso: Optional[Decimal] = Field(0, description="Porcentaje de progreso (0-100)")
    responsable_usuario_id: Optional[UUID] = Field(None, description="Usuario responsable del proyecto")
    responsable_persona_id: Optional[UUID] = Field(None, description="Persona responsable del proyecto")
    info_adicional: Optional[str] = Field(None, description="Información adicional")

class ProyectoCreate(ProyectoBase):
    """Model for creating a new proyecto."""
    # Additional fields for creation
    empresas_asociadas_ids: Optional[List[UUID]] = Field(default_factory=list, description="IDs de empresas asociadas")

class ProyectoUpdate(BaseModel):
    """Model for updating proyecto data."""
    nombre: Optional[str] = None
    descripcion: Optional[str] = None
    objetivo: Optional[str] = None
    estado: Optional[str] = None
    fecha_inicio: Optional[date] = None
    fecha_fin_estimada: Optional[date] = None
    fecha_fin_real: Optional[date] = None
    presupuesto: Optional[Decimal] = None
    prioridad: Optional[str] = None
    progreso: Optional[Decimal] = None
    responsable_usuario_id: Optional[UUID] = None
    responsable_persona_id: Optional[UUID] = None
    info_adicional: Optional[str] = None

class ProyectoInDBBase(ProyectoBase):
    """Base model for proyecto data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Proyecto(ProyectoInDBBase):
    """Model representing a proyecto as returned from the API."""
    # Populated by service layer when fetching detailed proyecto info
    responsable_usuario: Optional[User] = None
    empresas_asociadas: Optional[List[Empresa]] = Field(default_factory=list)
    
    # Calculated fields
    total_tareas: Optional[int] = Field(0, description="Total de tareas del proyecto")
    tareas_completadas: Optional[int] = Field(0, description="Tareas completadas")
    porcentaje_completado: Optional[Decimal] = Field(0, description="Porcentaje calculado de completado")

class ProyectoInDB(ProyectoInDBBase):
    """Model representing a proyecto stored in the database."""
    pass

# Models for relationship management
class ProyectoEmpresaLink(BaseModel):
    """Model for proyecto-empresa relationship."""
    proyecto_id: UUID
    empresa_id: UUID

class ProyectoProcesoLink(BaseModel):
    """Model for proyecto-proceso relationship."""
    id: UUID
    proyecto_id: UUID
    proceso_id: UUID
    created_at: datetime

# Response models for API
class ProyectoResponse(Proyecto):
    """Response model for proyecto API endpoints."""
    pass

class ProyectoListResponse(BaseModel):
    """Response model for proyecto list endpoints."""
    proyectos: List[Proyecto]
    total: int
    page: int
    size: int

# Dashboard specific models
class ProyectoSummary(BaseModel):
    """Simplified proyecto model for dashboard cards."""
    id: UUID
    nombre: str
    estado: str
    progreso: Decimal
    fecha_fin_estimada: Optional[date]
    responsable_usuario_id: Optional[UUID]
    responsable_nombre: Optional[str] = None
    empresa_principal: Optional[str] = None
    total_tareas: int = 0
    tareas_completadas: int = 0
