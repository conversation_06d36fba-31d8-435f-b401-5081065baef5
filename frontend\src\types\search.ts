export interface SearchResult {
  id: string;
  type: 'proyecto' | 'proceso' | 'tarea';
  title: string;
  description: string;
  url: string;
  
  // Common fields
  estado?: string;
  
  // Proyecto specific
  prioridad?: string;
  progreso?: number;
  responsable?: string;
  fecha_fin_estimada?: string;
  
  // Proceso specific
  tipo_proceso?: string;
  es_cuello_botella?: boolean;
  empresa?: string;
  tiempo_estimado?: number;
  
  // Tarea specific
  urgencia?: string;
  fecha_vencimiento?: string;
  es_vencida?: boolean;
  asignado?: string;
  proyecto?: string;
}

export interface GlobalSearchResponse {
  query: string;
  proyectos: SearchResult[];
  procesos: SearchResult[];
  tareas: SearchResult[];
  total_results: number;
}

export interface QuickSearchResponse {
  query: string;
  proyectos: SearchResult[];
  procesos: SearchResult[];
  tareas: SearchResult[];
  total_results: number;
  has_more: {
    proyectos: boolean;
    procesos: boolean;
    tareas: boolean;
  };
}

export interface SearchFilters {
  types?: Array<'proyectos' | 'procesos' | 'tareas'>;
  estados_proyecto?: string[];
  estados_proceso?: string[];
  estados_tarea?: string[];
  prioridades?: string[];
  urgencias?: string[];
}

export interface SearchFiltersResponse {
  types: Array<{
    value: string;
    label: string;
  }>;
  estados_proyecto: string[];
  estados_proceso: string[];
  estados_tarea: string[];
  prioridades: string[];
  urgencias: string[];
}

// Search type mappings
export const SEARCH_TYPE_LABELS: Record<SearchResult['type'], string> = {
  proyecto: 'Proyecto',
  proceso: 'Proceso',
  tarea: 'Tarea'
};

export const SEARCH_TYPE_COLORS: Record<SearchResult['type'], string> = {
  proyecto: 'bg-blue-100 text-blue-800',
  proceso: 'bg-purple-100 text-purple-800',
  tarea: 'bg-green-100 text-green-800'
};

export const SEARCH_TYPE_ICONS: Record<SearchResult['type'], string> = {
  proyecto: 'folder',
  proceso: 'workflow',
  tarea: 'task'
};
