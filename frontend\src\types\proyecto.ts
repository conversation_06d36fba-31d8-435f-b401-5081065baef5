export interface Proyecto {
  id: string;
  nombre: string;
  descripcion?: string;
  objetivo?: string;
  estado: string;
  fecha_inicio?: string;
  fecha_fin_estimada?: string;
  fecha_fin_real?: string;
  presupuesto?: number;
  prioridad: string;
  progreso: number;
  responsable_usuario_id?: string;
  responsable_persona_id?: string;
  info_adicional?: string;
  created_at: string;
  updated_at: string;
  
  // Related data
  responsable_usuario?: {
    id: string;
    nombre: string;
    email: string;
  };
  empresas_asociadas?: Array<{
    id: string;
    nombre: string;
    tipo_relacion: string;
  }>;
  
  // Calculated fields
  total_tareas?: number;
  tareas_completadas?: number;
  porcentaje_completado?: number;
}

export interface ProyectoCreate {
  nombre: string;
  descripcion?: string;
  objetivo?: string;
  estado?: string;
  fecha_inicio?: string;
  fecha_fin_estimada?: string;
  fecha_fin_real?: string;
  presupuesto?: number;
  prioridad?: string;
  progreso?: number;
  responsable_usuario_id?: string;
  responsable_persona_id?: string;
  info_adicional?: string;
  empresas_asociadas_ids?: string[];
}

export interface ProyectoUpdate {
  nombre?: string;
  descripcion?: string;
  objetivo?: string;
  estado?: string;
  fecha_inicio?: string;
  fecha_fin_estimada?: string;
  fecha_fin_real?: string;
  presupuesto?: number;
  prioridad?: string;
  progreso?: number;
  responsable_usuario_id?: string;
  responsable_persona_id?: string;
  info_adicional?: string;
}

export interface ProyectoSummary {
  id: string;
  nombre: string;
  estado: string;
  progreso: number;
  fecha_fin_estimada?: string;
  responsable_usuario_id?: string;
  responsable_nombre?: string;
  empresa_principal?: string;
  total_tareas: number;
  tareas_completadas: number;
}

export interface ProyectoListResponse {
  proyectos: Proyecto[];
  total: number;
  page: number;
  size: number;
}

export interface ProyectoFilters {
  estado?: string;
  prioridad?: string;
  responsable_id?: string;
  search?: string;
}

// Constants - Estados según PIE de gestión de entidades
export const ESTADOS_PROYECTO = [
  'propuesta',
  'planificacion',
  'desarrollo',
  'implementacion',
  'mantenimiento',
  'finalizado',
  'pendiente',
  'en_progreso',
  'completado',
  'cancelado',
  'pausado'
] as const;

export const PRIORIDADES_PROYECTO = [
  'Baja',
  'Media',
  'Alta',
  'Urgente'
] as const;

export type EstadoProyecto = typeof ESTADOS_PROYECTO[number];
export type PrioridadProyecto = typeof PRIORIDADES_PROYECTO[number];

// Color mappings for UI
export const ESTADO_COLORS: Record<EstadoProyecto, string> = {
  'propuesta': 'bg-gray-100 text-gray-800',
  'planificacion': 'bg-blue-100 text-blue-800',
  'desarrollo': 'bg-yellow-100 text-yellow-800',
  'implementacion': 'bg-orange-100 text-orange-800',
  'mantenimiento': 'bg-purple-100 text-purple-800',
  'finalizado': 'bg-green-100 text-green-800',
  'pendiente': 'bg-gray-100 text-gray-800',
  'en_progreso': 'bg-blue-100 text-blue-800',
  'completado': 'bg-green-100 text-green-800',
  'cancelado': 'bg-red-100 text-red-800',
  'pausado': 'bg-orange-100 text-orange-800'
};

export const PRIORIDAD_COLORS: Record<PrioridadProyecto, string> = {
  'Baja': 'bg-gray-100 text-gray-800',
  'Media': 'bg-blue-100 text-blue-800',
  'Alta': 'bg-orange-100 text-orange-800',
  'Urgente': 'bg-red-100 text-red-800'
};
