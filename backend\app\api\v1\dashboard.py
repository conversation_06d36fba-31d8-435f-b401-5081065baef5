import logging
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any
from uuid import UUID

from app.models.proyecto import ProyectoSummary
from app.models.proceso import ProcesoSummary
from app.models.tarea import TareaMatrix
from app.core.security import get_current_user_id
from app.services import proyecto_service, proceso_service, tarea_service

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=Dict[str, Any])
@router.get("", response_model=Dict[str, Any])
async def get_dashboard_data(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get all dashboard data in a single request.
    Returns:
    - tareas_matrix: Matrix of tasks organized by importance/urgency
    - proyectos_activos: Active projects summary
    - procesos_importantes: Important processes in execution
    - quick_stats: Quick statistics
    """
    try:
        logger.info(f"Fetching dashboard data for user {current_user_id}")
        
        # Fetch all dashboard data concurrently
        tareas_matrix = await tarea_service.get_tareas_matrix_dashboard()
        proyectos_activos = await proyecto_service.get_proyectos_activos_dashboard()
        procesos_importantes = await proceso_service.get_procesos_dashboard()
        
        # Calculate quick stats
        total_tareas_activas = (
            len(tareas_matrix.urgente_importante) +
            len(tareas_matrix.no_urgente_importante) +
            len(tareas_matrix.urgente_no_importante) +
            len(tareas_matrix.no_urgente_no_importante)
        )
        
        total_proyectos_activos = len(proyectos_activos)
        total_procesos_importantes = len(procesos_importantes)
        
        # Count urgent tasks
        tareas_urgentes = len(tareas_matrix.urgente_importante) + len(tareas_matrix.urgente_no_importante)
        
        # Count overdue tasks
        tareas_vencidas = sum(1 for tarea in tareas_matrix.urgente_importante + tareas_matrix.urgente_no_importante 
                             if tarea.es_vencida)
        
        quick_stats = {
            "total_tareas_activas": total_tareas_activas,
            "tareas_urgentes": tareas_urgentes,
            "tareas_vencidas": tareas_vencidas,
            "total_proyectos_activos": total_proyectos_activos,
            "total_procesos_importantes": total_procesos_importantes,
            "proyectos_en_riesgo": sum(1 for p in proyectos_activos 
                                     if p.fecha_fin_estimada and 
                                     (p.fecha_fin_estimada - __import__('datetime').date.today()).days < 7)
        }
        
        return {
            "tareas_matrix": tareas_matrix,
            "proyectos_activos": proyectos_activos,
            "procesos_importantes": procesos_importantes,
            "quick_stats": quick_stats,
            "last_updated": __import__('datetime').datetime.now(__import__('datetime').timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error fetching dashboard data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard data: {str(e)}"
        )

@router.get("/quick-actions")
async def get_quick_actions(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get quick action buttons configuration for dashboard.
    """
    try:
        logger.info(f"Fetching quick actions for user {current_user_id}")
        
        return {
            "actions": [
                {
                    "id": "create_proyecto",
                    "title": "Nuevo Proyecto",
                    "description": "Crear un nuevo proyecto",
                    "icon": "plus",
                    "color": "blue",
                    "route": "/proyectos/nuevo"
                },
                {
                    "id": "create_tarea",
                    "title": "Nueva Tarea",
                    "description": "Crear una nueva tarea",
                    "icon": "task",
                    "color": "green",
                    "route": "/tareas/nueva"
                },
                {
                    "id": "create_proceso",
                    "title": "Nuevo Proceso",
                    "description": "Crear un nuevo proceso",
                    "icon": "workflow",
                    "color": "purple",
                    "route": "/procesos/nuevo"
                },
                {
                    "id": "view_kanban",
                    "title": "Vista Kanban",
                    "description": "Ver tareas en formato Kanban",
                    "icon": "kanban",
                    "color": "orange",
                    "route": "/tareas/kanban"
                }
            ]
        }
        
    except Exception as e:
        logger.error(f"Error fetching quick actions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching quick actions: {str(e)}"
        )

@router.get("/notifications")
async def get_dashboard_notifications(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get dashboard notifications (overdue tasks, approaching deadlines, etc.).
    """
    try:
        logger.info(f"Fetching dashboard notifications for user {current_user_id}")
        
        # Get tasks matrix to analyze notifications
        tareas_matrix = await tarea_service.get_tareas_matrix_dashboard()
        
        notifications = []
        
        # Check for overdue tasks
        all_tasks = (tareas_matrix.urgente_importante + 
                    tareas_matrix.no_urgente_importante + 
                    tareas_matrix.urgente_no_importante + 
                    tareas_matrix.no_urgente_no_importante)
        
        overdue_tasks = [t for t in all_tasks if t.es_vencida]
        if overdue_tasks:
            notifications.append({
                "id": "overdue_tasks",
                "type": "error",
                "title": f"{len(overdue_tasks)} tarea(s) vencida(s)",
                "message": f"Tienes {len(overdue_tasks)} tarea(s) que han pasado su fecha límite",
                "action": "Ver tareas vencidas",
                "route": "/tareas?vencidas=true"
            })
        
        # Check for tasks due soon (next 3 days)
        import datetime
        today = datetime.date.today()
        soon_due = [t for t in all_tasks 
                   if t.fecha_vencimiento and 
                   0 <= (t.fecha_vencimiento - today).days <= 3 and 
                   not t.es_vencida]
        
        if soon_due:
            notifications.append({
                "id": "due_soon",
                "type": "warning",
                "title": f"{len(soon_due)} tarea(s) vencen pronto",
                "message": f"Tienes {len(soon_due)} tarea(s) que vencen en los próximos 3 días",
                "action": "Ver tareas próximas",
                "route": "/tareas?due_soon=true"
            })
        
        # Check for urgent important tasks
        urgent_important = tareas_matrix.urgente_importante
        if urgent_important:
            notifications.append({
                "id": "urgent_important",
                "type": "info",
                "title": f"{len(urgent_important)} tarea(s) urgente(s) e importante(s)",
                "message": "Tienes tareas que requieren atención inmediata",
                "action": "Ver matriz de tareas",
                "route": "/dashboard#tareas-matrix"
            })
        
        return {
            "notifications": notifications,
            "total_count": len(notifications)
        }
        
    except Exception as e:
        logger.error(f"Error fetching dashboard notifications: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard notifications: {str(e)}"
        )

@router.get("/widgets/summary")
async def get_summary_widgets(
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get summary widgets data for dashboard.
    """
    try:
        logger.info(f"Fetching summary widgets for user {current_user_id}")
        
        # Get basic data
        tareas_matrix = await tarea_service.get_tareas_matrix_dashboard()
        proyectos_activos = await proyecto_service.get_proyectos_activos_dashboard()
        procesos_importantes = await proceso_service.get_procesos_dashboard()
        
        # Calculate widget data
        widgets = [
            {
                "id": "total_tareas",
                "title": "Tareas Activas",
                "value": (len(tareas_matrix.urgente_importante) + 
                         len(tareas_matrix.no_urgente_importante) + 
                         len(tareas_matrix.urgente_no_importante) + 
                         len(tareas_matrix.no_urgente_no_importante)),
                "icon": "task",
                "color": "blue",
                "trend": "stable"  # Could be calculated based on historical data
            },
            {
                "id": "proyectos_activos",
                "title": "Proyectos Activos",
                "value": len(proyectos_activos),
                "icon": "project",
                "color": "green",
                "trend": "up"
            },
            {
                "id": "procesos_importantes",
                "title": "Procesos en Ejecución",
                "value": len(procesos_importantes),
                "icon": "workflow",
                "color": "purple",
                "trend": "stable"
            },
            {
                "id": "tareas_urgentes",
                "title": "Tareas Urgentes",
                "value": len(tareas_matrix.urgente_importante) + len(tareas_matrix.urgente_no_importante),
                "icon": "alert",
                "color": "red",
                "trend": "down"
            }
        ]
        
        return {"widgets": widgets}
        
    except Exception as e:
        logger.error(f"Error fetching summary widgets: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching summary widgets: {str(e)}"
        )
