import { supabase } from './supabaseClient';

export interface UserProfile {
  id: string;
  email: string;
  nombre: string;
  rol: string;
  empresa_id?: string;
  avatar_url?: string;
  info_adicional?: string;
  ultimo_acceso?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get the current user's profile information from the usuarios table
 */
export const getCurrentUserProfile = async (): Promise<UserProfile | null> => {
  try {
    // Get the current authenticated user from Supabase Auth
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('[UserService] Error getting authenticated user:', authError);
      return null;
    }

    // Fetch the user profile from the usuarios table
    const { data, error } = await supabase
      .from('usuarios')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('[UserService] Error fetching user profile:', error);
      return null;
    }

    if (!data) {
      console.warn('[UserService] User profile not found in usuarios table for user:', user.id);
      return null;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('[UserService] Exception getting user profile:', error);
    return null;
  }
};

/**
 * Update the current user's profile information
 */
export const updateUserProfile = async (updates: Partial<UserProfile>): Promise<UserProfile | null> => {
  try {
    // Get the current authenticated user from Supabase Auth
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('[UserService] Error getting authenticated user:', authError);
      return null;
    }

    // Update the user profile in the usuarios table
    const { data, error } = await supabase
      .from('usuarios')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('[UserService] Error updating user profile:', error);
      return null;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('[UserService] Exception updating user profile:', error);
    return null;
  }
};

/**
 * Create a user profile in the usuarios table (typically called after signup)
 */
export const createUserProfile = async (userProfile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile | null> => {
  try {
    // Get the current authenticated user from Supabase Auth
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('[UserService] Error getting authenticated user:', authError);
      return null;
    }

    // Create the user profile in the usuarios table
    const { data, error } = await supabase
      .from('usuarios')
      .insert({
        id: user.id,
        ...userProfile,
      })
      .select()
      .single();

    if (error) {
      console.error('[UserService] Error creating user profile:', error);
      return null;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('[UserService] Exception creating user profile:', error);
    return null;
  }
};
