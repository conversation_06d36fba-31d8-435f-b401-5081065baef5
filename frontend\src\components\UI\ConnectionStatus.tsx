import React from 'react';

interface ConnectionStatusProps {
  status: 'SUBSCRIBED' | 'TIMED_OUT' | 'CLOSED' | 'CHANNEL_ERROR' | 'disconnected' | 'error' | 'polling';
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status, className = '' }) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'SUBSCRIBED':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          text: 'Conectado (Realtime)',
          icon: '🟢',
          description: 'Recibiendo actualizaciones en vivo.'
        };
      case 'polling':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          text: 'Consultando estado (Polling)',
          icon: '🟡',
          description: 'Verificando periódicamente.'
        };
      case 'disconnected': // This is our logical state
      case 'CLOSED': // This is from Supabase
        return {
          color: 'text-gray-500', // Changed to gray for neutral disconnected
          bgColor: 'bg-gray-100',
          text: 'Desconectado',
          icon: '⚪',
          description: 'No hay conexión activa.'
        };
      // 'connecting' is no longer a direct status from this simplified hook.
      // Parent might infer 'connecting' or we simplify UI.
      // For now, 'TIMED_OUT' and 'CHANNEL_ERROR' will be treated as errors.
      case 'error': // This is our logical state
      case 'TIMED_OUT': // From Supabase
      case 'CHANNEL_ERROR': // From Supabase
        return {
          color: 'text-red-700', // Darker red for error
          bgColor: 'bg-red-200', // Darker bg for error
          text: 'Error de Conexión',
          icon: '🔴',
          description: 'No se pudo conectar. Reintentando...'
        };
      default: // Fallback for any unexpected status
        return {
            color: 'text-gray-500',
            bgColor: 'bg-gray-100',
            text: 'Estado Desconocido',
            icon: '❓',
            description: 'El estado de la conexión es desconocido.'
        };
    }
  };

  const statusInfo = getStatusInfo();
  if (!statusInfo) { // Should not happen with a default case, but good practice
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${statusInfo.bgColor} ${className}`}>
      <span className="text-sm">{statusInfo.icon}</span>
      <div className="flex flex-col">
        <span className={`text-xs font-medium ${statusInfo.color}`}>
          {statusInfo.text}
        </span>
        <span className="text-xs text-gray-500">
          {statusInfo.description}
        </span>
      </div>
    </div>
  );
};

export default ConnectionStatus;