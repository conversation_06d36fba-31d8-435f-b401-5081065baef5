# 🧭 Sistema de Navegación Centralizada

## 📋 Descripción General

Este sistema proporciona una **configuración centralizada** para toda la navegación de la aplicación, eliminando la necesidad de actualizar múltiples archivos cuando se añaden nuevas pestañas.

## 🎯 Beneficios

- ✅ **Actualización automática**: Añade una ruta → Aparece en toda la app
- ✅ **Configuración única**: Un solo archivo para mantener
- ✅ **Consistencia**: Mismos iconos y labels en toda la app
- ✅ **Escalabilidad**: Preparado para permisos y roles
- ✅ **Validación**: Detecta rutas rotas automáticamente

## 📁 Estructura de Archivos

```
frontend/src/
├── config/
│   └── navigation.ts          # ⭐ Configuración centralizada
├── hooks/
│   └── useNavigation.ts       # Hook para usar la navegación
└── components/
    ├── Chat/ContextPanel.tsx  # ✅ Ya actualizado
    └── AppShell/SidebarNav.tsx # 🔄 Pendiente de actualizar
```

## 🔧 Cómo Añadir Nuevas Pestañas

### 1. Pestaña Principal (Top-level)

```typescript
// En frontend/src/config/navigation.ts
const mainNavigation: NavigationItem[] = [
  // ... pestañas existentes
  {
    path: '/nueva-seccion',
    icon: '🆕',
    label: 'Nueva Sección',
    description: 'Descripción de la nueva sección',
    requiresAuth: true
  }
];
```

### 2. Pestaña en Grupo Existente

```typescript
// En frontend/src/config/navigation.ts
{
  id: 'project-management',
  label: 'Gestión de Proyectos',
  icon: '📁',
  items: [
    // ... items existentes
    {
      path: '/nueva-herramienta',
      icon: '🔧',
      label: 'Nueva Herramienta',
      description: 'Nueva herramienta de gestión',
      requiresAuth: true
    }
  ]
}
```

### 3. Nuevo Grupo Completo

```typescript
// En frontend/src/config/navigation.ts
const groupedNavigation: NavigationGroup[] = [
  // ... grupos existentes
  {
    id: 'nuevo-modulo',
    label: 'Nuevo Módulo',
    icon: '🚀',
    items: [
      {
        path: '/nuevo-modulo/feature1',
        icon: '⭐',
        label: 'Feature 1',
        requiresAuth: true
      },
      {
        path: '/nuevo-modulo/feature2',
        icon: '💫',
        label: 'Feature 2',
        requiresAuth: true
      }
    ]
  }
];
```

## 🎨 Uso en Componentes

### Chat Panel (Móvil)
```typescript
import { useMobileNavigation } from '../../hooks/useNavigation';

const { mainSections, groupedSections, isCurrentPath } = useMobileNavigation();

// Renderizado automático de todas las secciones
{mainSections.map((item) => (
  <Link to={item.path} className={isCurrentPath(item.path) ? 'active' : ''}>
    {item.icon} {item.label}
  </Link>
))}
```

### Sidebar Desktop
```typescript
import { useNavigation } from '../../hooks/useNavigation';

const { navigation, isCurrentPath } = useNavigation();
// Similar implementación para desktop
```

## 🔮 Funcionalidades Futuras

### Permisos por Rol
```typescript
// Futuro: Filtrado automático por permisos de usuario
{
  path: '/admin-panel',
  icon: '⚙️',
  label: 'Panel Admin',
  roles: ['admin', 'super-admin'] // Solo visible para estos roles
}
```

### Validación Automática
```typescript
// El sistema puede detectar rutas rotas automáticamente
const { valid, invalid } = validateNavigationRoutes(availableRoutes);
console.warn('Rutas rotas detectadas:', invalid);
```

## 📊 Estado Actual

- ✅ **Chat Panel**: Completamente migrado al sistema centralizado
- ✅ **Configuración**: Sistema base implementado
- ✅ **Hooks**: useNavigation y useMobileNavigation listos
- 🔄 **Sidebar Desktop**: Pendiente de migración
- 🔄 **Validación**: Sistema preparado, pendiente de implementar

## 🚀 Próximos Pasos

1. **Migrar SidebarNav.tsx** al sistema centralizado
2. **Implementar validación** de rutas en desarrollo
3. **Añadir sistema de permisos** por rol de usuario
4. **Crear tests** para la configuración de navegación

## 💡 Ejemplo de Uso Completo

```typescript
// 1. Añadir en navigation.ts
{
  path: '/analytics',
  icon: '📈',
  label: 'Analytics',
  description: 'Métricas y análisis',
  requiresAuth: true
}

// 2. Crear la ruta en App.tsx
<Route path="/analytics" element={<AnalyticsPage />} />

// 3. ¡Listo! Aparece automáticamente en:
//    - Chat panel móvil
//    - Sidebar desktop (cuando se migre)
//    - Cualquier componente que use useNavigation()
```

---

**¡El sistema está diseñado para crecer con la aplicación sin requerir cambios en múltiples archivos!** 🎉
