import React, { useMemo } from 'react'; // Added useMemo for token counting
import { Link } from 'react-router-dom';
import { IoMenu, IoTimeOutline, IoAddCircleOutline } from 'react-icons/io5';
// Removed Supabase imports for now
import ChatInput from '../components/Chat/ChatInput';
import ChatHistory from '../components/Chat/ChatHistory';
import ContextPanel from '../components/Chat/ContextPanel';
import TitleEditor from '../components/Chat/TitleEditor';
import { useChat } from '../hooks/useChat'; // Corrected import path
// Import icons later
// import { IoMenu, IoTimeOutline } from 'react-icons/io5';

// Removed placeholder types (DisplayMessage, ThreadRow) and data (initialMessages)

const ChatPage: React.FC = () => {
  // Get state and functions from ChatContext
  const {
    isContextPanelOpen,
    toggleContextPanel,
    currentThreadId,
    startNewConversation,
    isAgentSelectionRequired, // Get the new state
    messages, // Get messages for token counting
    currentThreadTitle,
    updateThreadTitle,
  } = useChat();

  // Function to count tokens (approximate word count)
  const countTokens = (text: string): number => {
    if (!text || typeof text !== 'string') return 0;
    // Simple approximation: split by whitespace and count words
    // This is a basic estimation - for more accuracy you could use a proper tokenizer
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  // Calculate total tokens for current thread
  const totalTokens = useMemo(() => {
    if (!currentThreadId || !messages.length) return 0;

    let total = 0;

    // Count tokens from all messages in the current thread
    messages.forEach(message => {
      // Count tokens from main message content
      if (message.content) {
        total += countTokens(message.content);
      }

      // Count tokens from intermediate steps if they exist
      if (message.intermediate_steps && message.intermediate_steps.length > 0) {
        message.intermediate_steps.forEach(step => {
          if (step.content) {
            total += countTokens(step.content);
          }
        });
      }
    });

    return total;
  }, [messages, currentThreadId]);

  // Removed local state management
  // Removed Supabase Realtime useEffect

  // Determine if the panel should be open based on user action OR requirement
  const isPanelEffectivelyOpen = isContextPanelOpen || isAgentSelectionRequired;

  return (
    <div className="flex h-full w-full overflow-hidden bg-white">
      {/* Context Panel */}
      <ContextPanel
        isOpen={isPanelEffectivelyOpen}
        onClose={toggleContextPanel}
        totalTokens={totalTokens}
      />

      {/* Mobile Header - Fixed at top */}
      <header className="md:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between p-3">
          {/* Left: Menu Button */}
          <button
            onClick={toggleContextPanel}
            className={`p-2 rounded-full transition-all duration-300 ${
              isPanelEffectivelyOpen
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            aria-label={isPanelEffectivelyOpen ? "Cerrar menú" : "Abrir menú"}
          >
            <IoMenu size={20} />
          </button>

          {/* Center: Title */}
          <div className="flex-1 px-3 min-w-0">
            {currentThreadId ? (
              <TitleEditor
                title={currentThreadTitle || 'Chat sin título'}
                onSave={updateThreadTitle}
                className="text-center"
                placeholder="Chat sin título"
              />
            ) : (
              <h1 className="text-base font-semibold text-gray-800 truncate text-center">
                Nuevo Chat
              </h1>
            )}
          </div>

          {/* Right: Action Buttons */}
          <div className="flex items-center space-x-1">
            <button
              onClick={startNewConversation}
              className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
              aria-label="Nuevo chat"
            >
              <IoAddCircleOutline size={20} />
            </button>
            <Link
              to="/history"
              className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
              aria-label="Historial"
            >
              <IoTimeOutline size={20} />
            </Link>
          </div>
        </div>
      </header>

      {/* Main Chat Area */}
      <div className={`flex flex-col flex-1 h-full min-w-0 transition-all duration-300 ease-in-out ${
        isPanelEffectivelyOpen ? 'md:ml-1/2' : 'ml-0'
      }`}>
        {/* Desktop Header */}
        <header className="hidden md:flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-2 min-w-0">
            {/* Context Panel Toggle */}
            <button
              onClick={toggleContextPanel}
              className={`p-2 rounded-full flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors ${
                isPanelEffectivelyOpen
                  ? 'text-indigo-600 bg-indigo-100 hover:bg-indigo-200'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
              }`}
              aria-label={isPanelEffectivelyOpen ? "Cerrar panel de contexto" : "Abrir panel de contexto"}
            >
              <IoMenu size={24} />
            </button>
            {/* Title - Editable when thread exists */}
            <div className="min-w-0 flex-1">
              {currentThreadId ? (
                <TitleEditor
                  title={currentThreadTitle || 'Chat sin título'}
                  onSave={updateThreadTitle}
                  className="mb-1"
                  placeholder="Chat sin título"
                />
              ) : (
                <h1 className="text-lg md:text-xl font-semibold text-gray-800 truncate">
                  Nuevo Chat
                </h1>
              )}
              {isPanelEffectivelyOpen && (
                <p className="text-xs text-indigo-600 hidden md:block">
                  Panel de contexto abierto
                </p>
              )}
            </div>
          </div>

          {/* Token Counter - Center section */}
          {currentThreadId && (
            <div className="hidden sm:flex items-center justify-center flex-1 max-w-xs mx-4">
              <div
                className="bg-white border border-gray-300 rounded-full px-3 py-1 shadow-sm hover:shadow-md transition-shadow cursor-help"
                title="Contador de tokens: Número total de palabras en todos los mensajes de esta conversación (incluye mensajes de usuario, respuestas del agente y pasos intermedios)"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-gray-700">
                    {totalTokens.toLocaleString()} tokens
                  </span>
                </div>
              </div>
            </div>
          )}



          <div className="flex items-center space-x-2 flex-shrink-0">
            {/* New Conversation Button */}
            <button
              onClick={startNewConversation}
              className="flex items-center space-x-1 p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              aria-label="Start new conversation"
            >
              <IoAddCircleOutline size={24} />
              <span className="hidden sm:inline text-sm">New</span>
            </button>
            {/* History Link */}
            <Link
              to="/history"
              className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              aria-label="View conversation history"
            >
              <IoTimeOutline size={24} />
            </Link>
          </div>
        </header>

        {/* Chat History - Scrollable area */}
        <div className="flex-1 min-h-0 overflow-y-auto mobile-scroll pt-14 md:pt-0">
          <ChatHistory />
        </div>

        {/* Input area - Fixed at bottom */}
        <div className="flex-shrink-0">
          <ChatInput />
        </div>
      </div>

    </div>
  );
};

export default ChatPage;