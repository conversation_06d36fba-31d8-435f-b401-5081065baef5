import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import SearchableDropdown, { SearchableDropdownOption } from '../UI/SearchableDropdown';
import { useAuth } from '../../hooks/useAuth';

// Define Zod schema for validation based on Pydantic model PersonaCreate
// and form fields from docs/FEATURES/CRM_ENTITY_QUICK_ADD_FORMS.md
const personaSchema = z.object({
  nombre: z.string().min(1, "Nombre es requerido"),
  apellidos: z.string().min(1, "Apellidos son requeridos"),
  email: z.string().email("Debe ser un email válido").optional().or(z.literal('')),
  telefono: z.string().optional(),
  cargo: z.string().optional(),
  empresa_id: z.string().uuid({ message: "ID de Empresa debe ser un UUID válido" }).optional().or(z.literal('')),
  departamento_id: z.string().uuid({ message: "ID de Departamento debe ser un UUID válido" }).optional().or(z.literal('')),
  tipo: z.array(z.string()).optional(),
  es_decision_maker: z.boolean(), // Rely on RHF defaultValues
  responsable_departamento: z.boolean(), // Rely on RHF defaultValues
  linkedin_url: z.string().url("Debe ser una URL válida para LinkedIn").optional().or(z.literal('')),
  activo: z.boolean(), // Rely on RHF defaultValues
  info_adicional: z.string().optional(),
});

type PersonaFormValues = z.infer<typeof personaSchema>;

export interface PersonaApiResponse extends PersonaFormValues {
  id: string; // UUID
  fecha_alta?: string;
  fecha_baja?: string;
  created_at: string;
  updated_at: string;
}

interface PersonaCreateFormProps {
  onSubmitSuccess: (data: PersonaApiResponse) => void;
  onCancel: () => void;
  // Props for handling Empresa selection/creation if needed later
  openEmpresaModal?: () => void;
  initialEmpresaId?: string;
}

const PersonaCreateForm: React.FC<PersonaCreateFormProps> = ({
  onSubmitSuccess,
  onCancel,
  openEmpresaModal,
  initialEmpresaId
}) => {
  const { control, register, handleSubmit, formState: { errors, isSubmitting }, setValue, watch } = useForm<PersonaFormValues>({
    resolver: zodResolver(personaSchema),
    defaultValues: {
      nombre: '',
      apellidos: '',
      email: '',
      telefono: '',
      cargo: '',
      empresa_id: initialEmpresaId || '',
      departamento_id: '',
      tipo: [],
      es_decision_maker: false,
      responsable_departamento: false,
      linkedin_url: '',
      activo: true,
      info_adicional: '',
    }
  });
  const { session } = useAuth();
  const [empresaOptions, setEmpresaOptions] = useState<SearchableDropdownOption[]>([]);
  const [isLoadingEmpresas, setIsLoadingEmpresas] = useState(false);
  const [departamentoOptions, setDepartamentoOptions] = useState<SearchableDropdownOption[]>([]);
  const [isLoadingDepartamentos, setIsLoadingDepartamentos] = useState(false);

  const watchedEmpresaId = watch('empresa_id');

  useEffect(() => {
    if (initialEmpresaId) {
      setValue('empresa_id', initialEmpresaId);
    }
  }, [initialEmpresaId, setValue]);

  const fetchEmpresas = async (searchTerm: string): Promise<SearchableDropdownOption[]> => {
    setIsLoadingEmpresas(true);
    console.log('Fetching empresas with term:', searchTerm);
    // Simulate API call - replace with actual
    await new Promise(resolve => setTimeout(resolve, 700));
    const mockData: { id: string, nombre: string, nif_cif?: string }[] = [
      { id: 'e1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6', nombre: 'Empresa Global Corp', nif_cif: 'A12345678' },
      { id: 'f2g3h4i5-j6k7-l8m9-n0o1-p2q3r4s5t6u7', nombre: 'Soluciones Innovadoras S.L.', nif_cif: 'B87654321' },
      { id: 'a0b1c2d3-e4f5-a6b7-c8d9-e0f1a2b3c4d5', nombre: 'Tech Avanzada Ltda.', nif_cif: 'C11223344' },
    ];
    const filtered = mockData
      .filter(emp =>
        emp.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (emp.nif_cif && emp.nif_cif.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .map(emp => ({
        value: emp.id,
        label: emp.nombre,
        subLabel: emp.nif_cif ? `NIF/CIF: ${emp.nif_cif}` : undefined
      }));
    setEmpresaOptions(filtered);
    setIsLoadingEmpresas(false);
    return filtered;
  };
  
  useEffect(() => {
    fetchEmpresas(''); // Initial fetch for empresas
  }, []);

  const fetchDepartamentos = async (searchTerm: string, currentEmpresaId?: string | null): Promise<SearchableDropdownOption[]> => {
    if (!currentEmpresaId) {
      setDepartamentoOptions([]);
      return [];
    }
    setIsLoadingDepartamentos(true);
    console.log('Fetching departamentos for empresa:', currentEmpresaId, 'with term:', searchTerm);
    // Simulate API call - replace with actual: /api/v1/empresas/{currentEmpresaId}/departamentos?search=searchTerm
    // Or a general /api/v1/departamentos?empresa_id={currentEmpresaId}&search=searchTerm
    await new Promise(resolve => setTimeout(resolve, 600));
    const mockData: { id: string, nombre: string, empresa_id: string }[] = [
      { id: 'dep1-e1', nombre: 'Ventas Global Corp', empresa_id: 'e1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6' },
      { id: 'dep2-e1', nombre: 'Marketing Global Corp', empresa_id: 'e1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6' },
      { id: 'dep1-f2', nombre: 'Desarrollo Innovadoras', empresa_id: 'f2g3h4i5-j6k7-l8m9-n0o1-p2q3r4s5t6u7' },
      { id: 'dep2-f2', nombre: 'Soporte Innovadoras', empresa_id: 'f2g3h4i5-j6k7-l8m9-n0o1-p2q3r4s5t6u7' },
      { id: 'dep1-a0', nombre: 'I+D Tech Avanzada', empresa_id: 'a0b1c2d3-e4f5-a6b7-c8d9-e0f1a2b3c4d5' },
    ];

    const filtered = mockData
      .filter(dep => dep.empresa_id === currentEmpresaId && dep.nombre.toLowerCase().includes(searchTerm.toLowerCase()))
      .map(dep => ({
        value: dep.id,
        label: dep.nombre,
      }));
    setDepartamentoOptions(filtered);
    setIsLoadingDepartamentos(false);
    return filtered;
  };

  useEffect(() => {
    if (watchedEmpresaId) {
      fetchDepartamentos('', watchedEmpresaId);
    } else {
      setDepartamentoOptions([]); // Clear department options if no empresa is selected
      setValue('departamento_id', ''); // Clear selected department value
    }
  }, [watchedEmpresaId, setValue]);


  const onSubmit: SubmitHandler<PersonaFormValues> = async (data) => {
    const payload = {
      ...data,
      empresa_id: data.empresa_id === '' ? undefined : data.empresa_id,
      departamento_id: data.departamento_id === '' ? undefined : data.departamento_id,
    };
    try {
      console.log('Submitting Persona Data:', payload);
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/personas`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Error al crear persona');
      }
      const result = await response.json();
      onSubmitSuccess(result as PersonaApiResponse);
    } catch (error) {
      console.error('Error submitting persona form:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formFieldClass = "mb-4";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm";
  const errorClass = "mt-1 text-xs text-red-600";
  const checkboxLabelClass = "ml-2 block text-sm text-gray-900";
  const checkboxClass = "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500";


  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 bg-white shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Crear Nueva Persona</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="nombre" className={labelClass}>Nombre*</label>
          <input id="nombre" {...register("nombre")} className={inputClass} />
          {errors.nombre && <p className={errorClass}>{errors.nombre.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="apellidos" className={labelClass}>Apellidos*</label>
          <input id="apellidos" {...register("apellidos")} className={inputClass} />
          {errors.apellidos && <p className={errorClass}>{errors.apellidos.message}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="email" className={labelClass}>Email</label>
          <input id="email" type="email" {...register("email")} className={inputClass} />
          {errors.email && <p className={errorClass}>{errors.email.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="telefono" className={labelClass}>Teléfono</label>
          <input id="telefono" {...register("telefono")} className={inputClass} />
          {errors.telefono && <p className={errorClass}>{errors.telefono.message}</p>}
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="cargo" className={labelClass}>Cargo</label>
        <input id="cargo" {...register("cargo")} className={inputClass} />
        {errors.cargo && <p className={errorClass}>{errors.cargo.message}</p>}
      </div>
      
      {/* Placeholder for Empresa and Departamento selection/creation */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="empresa_id" className={labelClass}>Empresa</label>
          <Controller
            name="empresa_id"
            control={control}
            render={({ field }) => (
              <SearchableDropdown
                options={empresaOptions}
                value={field.value || null}
                onChange={(val) => {
                  field.onChange(val);
                  setValue('departamento_id', ''); // Reset department when company changes
                  // fetchDepartamentos will be triggered by the useEffect watching watchedEmpresaId
                }}
                placeholder="Buscar o seleccionar Empresa..."
                fetchOptions={fetchEmpresas}
                isLoading={isLoadingEmpresas}
                onAddNew={openEmpresaModal}
                addNewLabel="+ Nueva Empresa"
                disabled={isSubmitting}
              />
            )}
          />
          {errors.empresa_id && <p className={errorClass}>{errors.empresa_id.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="departamento_id" className={labelClass}>Departamento</label>
          <Controller
            name="departamento_id"
            control={control}
            key={watchedEmpresaId || 'dep-dropdown-empty'} // Add key here
            render={({ field }) => (
              <SearchableDropdown
                options={departamentoOptions}
                value={field.value || null}
                onChange={(val) => field.onChange(val)}
                placeholder="Buscar o seleccionar Departamento..."
                fetchOptions={(searchTerm) => fetchDepartamentos(searchTerm, watchedEmpresaId)}
                isLoading={isLoadingDepartamentos}
                // onAddNew={() => { /* TODO: Implement openDepartamentoModal if needed */ }}
                // addNewLabel="+ Nuevo Departamento"
                disabled={isSubmitting || !watchedEmpresaId || isLoadingDepartamentos}
              />
            )}
          />
          {errors.departamento_id && <p className={errorClass}>{errors.departamento_id.message}</p>}
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="linkedin_url" className={labelClass}>LinkedIn URL</label>
        <input id="linkedin_url" type="url" {...register("linkedin_url")} className={inputClass} />
        {errors.linkedin_url && <p className={errorClass}>{errors.linkedin_url.message}</p>}
      </div>

      {/* Tipo (Multiselect/Checkboxes) - Simple text input for now, can be enhanced */}
      <div className={formFieldClass}>
        <label htmlFor="tipo" className={labelClass}>Tipo (ej: Contacto Principal, Técnico - separado por comas)</label>
        <input
          id="tipo"
          {...register("tipo", {
            setValueAs: (value: string | undefined) => {
              if (!value || typeof value !== 'string') return [];
              return value.split(',').map(s => s.trim()).filter(s => s);
            }
          })}
          className={inputClass}
          placeholder="Contacto Principal, Técnico"
        />
        {errors.tipo && <p className={errorClass}>{Array.isArray(errors.tipo) ? errors.tipo.map(e => e?.message).join(', ') : errors.tipo.message}</p>}
      </div>
      
      <div className="space-y-3 mt-4">
        <div className="flex items-center">
          <input id="es_decision_maker" type="checkbox" {...register("es_decision_maker")} className={checkboxClass} />
          <label htmlFor="es_decision_maker" className={checkboxLabelClass}>Es Decision Maker</label>
        </div>
        <div className="flex items-center">
          <input id="responsable_departamento" type="checkbox" {...register("responsable_departamento")} className={checkboxClass} />
          <label htmlFor="responsable_departamento" className={checkboxLabelClass}>Responsable Departamento</label>
        </div>
        <div className="flex items-center">
          <input id="activo" type="checkbox" {...register("activo")} className={checkboxClass} />
          <label htmlFor="activo" className={checkboxLabelClass}>Activo</label>
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="info_adicional" className={labelClass}>Info Adicional</label>
        <textarea id="info_adicional" {...register("info_adicional")} rows={3} className={inputClass}></textarea>
        {errors.info_adicional && <p className={errorClass}>{errors.info_adicional.message}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button type="button" onClick={onCancel} disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          Cancelar
        </button>
        <button type="submit" disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          {isSubmitting ? 'Creando...' : 'Crear Persona'}
        </button>
      </div>
    </form>
  );
};

export default PersonaCreateForm;