"""
API endpoints for Ideas functionality
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from uuid import UUID

from app.models.idea import (
    IdeaCreate, IdeaUpdate, Idea as IdeaResponse, IdeasListResponse,
    IdeaFilters, EstadoIdea, PrioridadIdea
)
from app.services import idea_service
from app.core.security import get_current_user_id

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=IdeasListResponse)
@router.get("", response_model=IdeasListResponse)
async def get_ideas(
    empresa_id: Optional[UUID] = Query(None, description="Filter by empresa ID"),
    proyecto_id: Optional[UUID] = Query(None, description="Filter by proyecto ID"),
    estado: Optional[EstadoIdea] = Query(None, description="Filter by estado"),
    prioridad: Optional[PrioridadIdea] = Query(None, description="Filter by prioridad"),
    search: Optional[str] = Query(None, description="Search term for titulo and descripcion"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get ideas with filtering and pagination
    """
    try:
        logger.info(f"Getting ideas for user {current_user_id} with filters: empresa_id={empresa_id}, estado={estado}, prioridad={prioridad}")
        
        filters = IdeaFilters(
            empresa_id=empresa_id,
            proyecto_id=proyecto_id,
            estado=estado,
            prioridad=prioridad,
            search=search,
            limit=limit,
            offset=offset
        )
        
        result = await idea_service.get_ideas(filters)
        
        logger.info(f"Retrieved {len(result.ideas)} ideas (total: {result.total})")
        return result
        
    except Exception as e:
        logger.error(f"Error getting ideas: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving ideas: {str(e)}"
        )

@router.get("/{idea_id}", response_model=IdeaResponse)
async def get_idea(
    idea_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a single idea by ID
    """
    try:
        logger.info(f"Getting idea {idea_id} for user {current_user_id}")
        
        idea = await idea_service.get_idea_by_id(idea_id)
        
        logger.info(f"Retrieved idea: {idea.titulo}")
        return idea
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving idea: {str(e)}"
        )

@router.post("/", response_model=IdeaResponse, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=IdeaResponse, status_code=status.HTTP_201_CREATED)
async def create_idea(
    idea_data: IdeaCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new idea
    """
    try:
        logger.info(f"Creating idea '{idea_data.titulo}' for user {current_user_id}")
        
        idea = await idea_service.create_idea(idea_data)
        
        logger.info(f"Created idea with ID: {idea.id}")
        return idea
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating idea: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating idea: {str(e)}"
        )

@router.put("/{idea_id}", response_model=IdeaResponse)
async def update_idea(
    idea_id: UUID,
    idea_data: IdeaUpdate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update an existing idea
    """
    try:
        logger.info(f"Updating idea {idea_id} for user {current_user_id}")
        
        idea = await idea_service.update_idea(idea_id, idea_data)
        
        logger.info(f"Updated idea: {idea.titulo}")
        return idea
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating idea: {str(e)}"
        )

@router.delete("/{idea_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_idea(
    idea_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete an idea
    """
    try:
        logger.info(f"Deleting idea {idea_id} for user {current_user_id}")
        
        await idea_service.delete_idea(idea_id)
        
        logger.info(f"Deleted idea {idea_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting idea {idea_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting idea: {str(e)}"
        )
