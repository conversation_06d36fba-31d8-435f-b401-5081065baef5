"""
Tools package for AI agent tools.

This package contains individual tool implementations that can be used by AI agents
through n8n workflows or other automation systems.

Available tools:
- SQL Execution Tool: Execute SQL commands with security validation (DEPRECATED)
- SELECT Query Tool: Execute SELECT queries with read-only database pool
- Database Operations Tool: Execute INSERT/UPDATE/DELETE with structured JSON
- Text Modification Tool: Perform granular text field modifications
"""

import logging
from fastapi import APIRouter

# Import all tool routers for easy access
from .sql_execution import router as sql_execution_router
from .select_query import router as select_query_router
from .database_operations import router as database_operations_router
from .text_modification import router as text_modification_router

logger = logging.getLogger(__name__)

# Create main router that includes all tool routers
router = APIRouter()

# Include individual tool routers
logger.info("Registering SQL execution tool (deprecated)...")
router.include_router(sql_execution_router, tags=["SQL Execution Tool (Deprecated)"])

logger.info("Registering SELECT query tool...")
router.include_router(select_query_router, tags=["SELECT Query Tool"])

logger.info("Registering database operations tool...")
router.include_router(database_operations_router, tags=["Database Operations Tool"])

logger.info("Registering text modification tool...")
router.include_router(text_modification_router, tags=["Text Modification Tool"])

__all__ = [
    "router",
    "sql_execution_router",
    "select_query_router",
    "database_operations_router",
    "text_modification_router"
]
