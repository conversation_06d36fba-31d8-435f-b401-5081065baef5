from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List # Import List
from uuid import UUID

from app.models.crm_lead_empresa import LeadEmpresaCreate, LeadEmpresa as LeadEmpresaResponse
from app.services import crm_lead_empresa_service
from app.core.security import get_current_user_id

router = APIRouter()

@router.post("", response_model=LeadEmpresaResponse, status_code=status.HTTP_201_CREATED) # Removed trailing slash
async def create_new_lead_empresa(
    lead_empresa_in: LeadEmpresaCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new lead empresa.
    """
    try:
        # db=None no longer needed
        created_lead_empresa_data = await crm_lead_empresa_service.create_lead_empresa(
            lead_empresa_in=lead_empresa_in, user_id=current_user_id
        )
        
        # Assuming the service returns a dict compatible with LeadEmpresaResponse
        return LeadEmpresaResponse(**created_lead_empresa_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create lead empresa: {str(e)}"
        )

@router.get("", response_model=List[LeadEmpresaResponse]) # Removed trailing slash
async def list_leads_empresas(
    search: str | None = Query(None, alias="search"),
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id) # Protect endpoint
):
    """
    Retrieve a list of lead empresas, with optional search.
    """
    try:
        leads_empresas_data = await crm_lead_empresa_service.get_leads_empresas(
            search=search, skip=skip, limit=limit
        )
        # The service now returns a list of Pydantic model instances (or raises an error)
        return leads_empresas_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve lead empresas: {str(e)}"
        )

# Add other endpoints (get by ID, update, delete) as needed.
