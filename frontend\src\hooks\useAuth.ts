import { useContext } from 'react';
// Adjust the import path based on the actual location of AuthContext
import { AuthContext, AuthContextType } from '../contexts/AuthContext';

// Create and export the custom hook for using the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};