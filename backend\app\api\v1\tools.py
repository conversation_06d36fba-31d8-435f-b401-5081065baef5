"""
Tools API Router

This module serves as the main entry point for all AI agent tools.
It imports and exposes the consolidated router from the tools package.

The actual tool implementations are organized in the tools/ subdirectory:
- tools/sql_execution.py: SQL execution tool
- tools/text_modification.py: Text field modification tool
- tools/__init__.py: Consolidated router registration

This approach provides better organization and maintainability as we add more tools.
"""

# Import the consolidated router from the tools package
from .tools import router