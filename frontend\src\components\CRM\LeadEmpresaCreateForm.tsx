import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON>, SubmitH<PERSON>ler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import SearchableDropdown, { SearchableDropdownOption } from '../UI/SearchableDropdown';
import { useAuth } from '../../hooks/useAuth';

// Define Zod schema for validation
const leadEmpresaSchema = z.object({
  nombre: z.string().min(1, "Nombre es requerido"),
  fuente: z.string().optional(),
  estado: z.string().optional(),
  descripcion: z.string().optional(),
  asignado_usuario_id: z.string().uuid({ message: "ID de Usuario debe ser un UUID válido" }).optional().or(z.literal('')),
  info_adicional: z.string().optional(),
});

type LeadEmpresaFormValues = z.infer<typeof leadEmpresaSchema>;

export interface LeadEmpresaApiResponse extends LeadEmpresaFormValues {
  id: string; // UUID
  created_at: string;
  updated_at: string;
  fecha_conversion?: string;
  convertido_a_empresa_id?: string;
  convertido_a_oportunidad_id?: string;
  convertido_a_persona_id?: string;
}

interface LeadEmpresaCreateFormProps {
  onSubmitSuccess: (data: LeadEmpresaApiResponse) => void;
  onCancel: () => void;
  currentUserId?: string; // Optional: To prefill 'asignado_usuario_id'
}

const LeadEmpresaCreateForm: React.FC<LeadEmpresaCreateFormProps> = ({
  onSubmitSuccess,
  onCancel,
  currentUserId
}) => {
  const { control, register, handleSubmit, formState: { errors, isSubmitting }, setValue } = useForm<LeadEmpresaFormValues>({
    resolver: zodResolver(leadEmpresaSchema),
    defaultValues: {
      nombre: '',
      fuente: '',
      estado: '',
      descripcion: '',
      asignado_usuario_id: currentUserId || '',
      info_adicional: '',
    }
  });
  const { session, user } = useAuth(); // Get current user from useAuth
  const [usuarioOptions, setUsuarioOptions] = useState<SearchableDropdownOption[]>([]);
  const [isLoadingUsuarios, setIsLoadingUsuarios] = useState(false);

  useEffect(() => {
    // Prefill 'asignado_usuario_id' with currentUserId if provided,
    // otherwise, default to the logged-in user's ID if available.
    const defaultUserId = currentUserId || user?.id;
    if (defaultUserId) {
      setValue('asignado_usuario_id', defaultUserId);
    }
  }, [currentUserId, user, setValue]);

  const fetchUsuarios = useCallback(async (searchTerm: string): Promise<SearchableDropdownOption[]> => {
    setIsLoadingUsuarios(true);
    console.log('Fetching usuarios with term:', searchTerm);
    // Simulate API call - replace with actual to /api/v1/usuarios?search=searchTerm (or similar)
    await new Promise(resolve => setTimeout(resolve, 700));
    const mockData: { id: string, nombre: string, apellidos?: string, email: string }[] = [
      { id: 'u1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6', nombre: 'Alice', apellidos: 'Smith', email: '<EMAIL>' },
      { id: 'u2g3h4i5-j6k7-l8m9-n0o1-p2q3r4s5t6u7', nombre: 'Bob', apellidos: 'Johnson', email: '<EMAIL>' },
      { id: 'u0b1c2d3-e4f5-a6b7-c8d9-e0f1a2b3c4d5', nombre: 'Charlie', apellidos: 'Brown', email: '<EMAIL>' },
      // Add the current user to the mock list if not already there, for testing pre-selection
    ];
     if (user && !mockData.find(u => u.id === user.id)) {
      mockData.push({ id: user.id, nombre: user.user_metadata?.nombre || 'Current', apellidos: user.user_metadata?.apellidos || 'User', email: user.email!});
    }

    const filtered = mockData
      .filter(u =>
        (u.nombre && u.nombre.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (u.apellidos && u.apellidos.toLowerCase().includes(searchTerm.toLowerCase())) ||
        u.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map(u => ({
        value: u.id,
        label: `${u.nombre || ''} ${u.apellidos || ''}`.trim() || u.email,
        subLabel: u.email
      }));
    setUsuarioOptions(filtered);
    setIsLoadingUsuarios(false);
    return filtered;
  }, [user]);

  useEffect(() => {
    fetchUsuarios(''); // Initial fetch
  }, [fetchUsuarios]);

  const onSubmit: SubmitHandler<LeadEmpresaFormValues> = async (data) => {
    const payload = {
      ...data,
      asignado_usuario_id: data.asignado_usuario_id === '' ? undefined : data.asignado_usuario_id,
    };
    try {
      console.log('Submitting Lead Empresa Data:', payload);
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/leads-empresas`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Error al crear lead empresa');
      }
      const result = await response.json();
      onSubmitSuccess(result as LeadEmpresaApiResponse);
    } catch (error) {
      console.error('Error submitting lead empresa form:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formFieldClass = "mb-4";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm";
  const errorClass = "mt-1 text-xs text-red-600";

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 bg-white shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Crear Nuevo Lead Empresa</h2>

      <div className={formFieldClass}>
        <label htmlFor="nombre" className={labelClass}>Nombre*</label>
        <input id="nombre" {...register("nombre")} className={inputClass} />
        {errors.nombre && <p className={errorClass}>{errors.nombre.message}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="fuente" className={labelClass}>Fuente</label>
          <input id="fuente" {...register("fuente")} className={inputClass} placeholder="Web, Referral, Evento..." />
          {/* Consider dropdown/select for predefined options */}
          {errors.fuente && <p className={errorClass}>{errors.fuente.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="estado" className={labelClass}>Estado</label>
          <input id="estado" {...register("estado")} className={inputClass} placeholder="Nuevo, Contactado, Calificado..." />
          {/* Consider dropdown/select for predefined options */}
          {errors.estado && <p className={errorClass}>{errors.estado.message}</p>}
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="descripcion" className={labelClass}>Descripción</label>
        <textarea id="descripcion" {...register("descripcion")} rows={3} className={inputClass}></textarea>
        {errors.descripcion && <p className={errorClass}>{errors.descripcion.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="asignado_usuario_id" className={labelClass}>Asignado a Usuario</label>
        <Controller
          name="asignado_usuario_id"
          control={control}
          render={({ field }) => (
            <SearchableDropdown
              options={usuarioOptions}
              value={field.value || null}
              onChange={(val) => field.onChange(val)}
              placeholder="Buscar o seleccionar Usuario..."
              fetchOptions={fetchUsuarios}
              isLoading={isLoadingUsuarios}
              // No onAddNew for users for now, unless a user creation form is planned here
              disabled={isSubmitting}
            />
          )}
        />
        {errors.asignado_usuario_id && <p className={errorClass}>{errors.asignado_usuario_id.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="info_adicional" className={labelClass}>Info Adicional</label>
        <textarea id="info_adicional" {...register("info_adicional")} rows={3} className={inputClass}></textarea>
        {errors.info_adicional && <p className={errorClass}>{errors.info_adicional.message}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button type="button" onClick={onCancel} disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          Cancelar
        </button>
        <button type="submit" disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          {isSubmitting ? 'Creando...' : 'Crear Lead Empresa'}
        </button>
      </div>
    </form>
  );
};

export default LeadEmpresaCreateForm;