import React, { memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tarea, ESTADOS_TAREA, PRIORIDADES_TAREA } from '../../types/tarea';
import { InlineEditableCell } from '../UI/InlineEditableCell';
import { Plus, Search, Target, RefreshCw } from 'lucide-react';

interface ProjectTasksSectionProps {
  tasks: Tarea[];
  tasksLoading: boolean;
  tasksSearch: string;
  tasksFilter: string;
  groupBy: 'none' | 'estado' | 'prioridad' | 'urgencia';
  onTasksSearchChange: (search: string) => void;
  onTasksFilterChange: (filter: string) => void;
  onGroupByChange: (groupBy: 'none' | 'estado' | 'prioridad' | 'urgencia') => void;
  onCreateTask: () => Promise<void>;
  onUpdateTaskField: (taskId: string, field: keyof import('../../types/tarea').TareaUpdate, value: string | number | boolean | null) => Promise<void>;
  onDeleteTask: (taskId: string) => Promise<void>;
  groupedTasks: Record<string, Tarea[]>;
}

const ProjectTasksSection: React.FC<ProjectTasksSectionProps> = memo(({
  tasks,
  tasksLoading,
  tasksSearch,
  tasksFilter,
  groupBy,
  onTasksSearchChange,
  onTasksFilterChange,
  onGroupByChange,
  onCreateTask,
  onUpdateTaskField,
  onDeleteTask,
  groupedTasks
}) => {
  const navigate = useNavigate();

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Tareas Asociadas ({tasks.length})
          </h2>
          <button
            onClick={onCreateTask}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nueva Tarea
          </button>
        </div>

        {/* Task Filters */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                id="tasks-search"
                aria-label="Buscar tareas"
                type="text"
                placeholder="Buscar tareas..."
                value={tasksSearch}
                onChange={(e) => onTasksSearchChange(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              aria-label="Filtrar por estado de tarea"
              value={tasksFilter}
              onChange={(e) => onTasksFilterChange(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todos los estados</option>
              {ESTADOS_TAREA.map((estado) => (
                <option key={estado} value={estado}>
                  {estado}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Agrupar por:</span>
            <select
              aria-label="Agrupar tareas por"
              value={groupBy}
              onChange={(e) => onGroupByChange(e.target.value as 'none' | 'estado' | 'prioridad' | 'urgencia')}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="none">Sin agrupar</option>
              <option value="estado">Estado</option>
              <option value="prioridad">Prioridad</option>
              <option value="urgencia">Urgencia</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tasks List */}
      <div className="p-6">
        {tasksLoading ? (
          <div className="text-center py-8">
            <RefreshCw className="h-6 w-6 mx-auto mb-2 animate-spin text-blue-600" />
            <p className="text-gray-600">Cargando tareas...</p>
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No hay tareas
            </h3>
            <p className="text-gray-600 mb-4">
              Crea la primera tarea para este proyecto.
            </p>
            <button
              onClick={onCreateTask}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Crear Tarea
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(groupedTasks).map(([groupName, groupTasks]) => (
              <div key={groupName}>
                {groupBy !== 'none' && (
                  <h3 className="text-md font-medium text-gray-900 mb-3 border-b border-gray-200 pb-2">
                    {groupName} ({groupTasks.length})
                  </h3>
                )}
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200" role="table" aria-label="Lista de tareas del proyecto">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tarea
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Estado
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Prioridad
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Asignado
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vencimiento
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Acciones
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {groupTasks.map((task) => (
                        <tr key={task.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <InlineEditableCell
                              value={task.titulo}
                              type="text"
                              onSave={(value) => onUpdateTaskField(task.id, 'titulo', value)}
                              className="font-medium text-gray-900"
                              required
                            />
                          </td>
                          <td className="px-6 py-4">
                            <InlineEditableCell
                              value={task.estado}
                              type="select"
                              options={ESTADOS_TAREA.map(estado => ({ value: estado, label: estado }))}
                              onSave={(value) => onUpdateTaskField(task.id, 'estado', value)}
                            />
                          </td>
                          <td className="px-6 py-4">
                            <InlineEditableCell
                              value={task.prioridad}
                              type="select"
                              options={PRIORIDADES_TAREA.map(prioridad => ({ value: prioridad, label: prioridad }))}
                              onSave={(value) => onUpdateTaskField(task.id, 'prioridad', value)}
                            />
                          </td>
                          <td className="px-6 py-4">
                            <span className="text-sm text-gray-600">
                              {task.asignado_usuario?.nombre || 'Sin asignar'}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <InlineEditableCell
                              value={task.fecha_vencimiento || ''}
                              type="date"
                              onSave={(value) => onUpdateTaskField(task.id, 'fecha_vencimiento', value)}
                            />
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => navigate(`/tareas/${task.id}`)}
                                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              >
                                Ver
                              </button>
                              <button
                                onClick={() => {
                                  if (window.confirm('¿Estás seguro de que deseas eliminar esta tarea?')) {
                                    onDeleteTask(task.id);
                                  }
                                }}
                                className="text-red-600 hover:text-red-800 p-1 rounded"
                                title="Eliminar tarea"
                              >
                                ×
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

ProjectTasksSection.displayName = 'ProjectTasksSection';

export default ProjectTasksSection;
