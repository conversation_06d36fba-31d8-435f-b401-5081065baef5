{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc -b && vite build", "build:prod": "tsc -b && vite build --mode production", "build:dev": "tsc -b && vite build --mode development", "lint": "eslint .", "preview": "vite preview", "preview:prod": "vite preview --mode production", "test": "echo 'No tests configured yet'"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.80.7", "@types/react-select": "^5.0.0", "lucide-react": "^0.460.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "react-select": "^5.10.1", "remark-gfm": "^4.0.1", "tus-js-client": "^4.3.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/vite": "^4.1.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}