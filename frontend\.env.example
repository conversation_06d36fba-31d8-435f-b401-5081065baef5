# Frontend Environment Variables Example
# Copy this file to .env for development or .env.production for production

# Supabase configuration (exposed to frontend)
VITE_SUPABASE_URL=https://aceleralia-database.aceleralia.com/
VITE_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0MTE4OTQ0MCwiZXhwIjo0ODk2ODYzMDQwLCJyb2xlIjoiYW5vbiJ9.1ul6hLeDwOhMPosVltTeRJaoGpLYonC1rzlxUYdsGXg

# Backend API URL for Project Management System
# For development (local):
VITE_API_URL=http://localhost:8000
# For production, use: https://backend.aceleralia.com

# Environment identifier
# Options: development, production
VITE_ENVIRONMENT=development

# Note: In Coolify, these variables should be set as Build Arguments:
# VITE_SUPABASE_URL=https://aceleralia-database.aceleralia.com/
# VITE_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0MTE4OTQ0MCwiZXhwIjo0ODk2ODYzMDQwLCJyb2xlIjoiYW5vbiJ9.1ul6hLeDwOhMPosVltTeRJaoGpLYonC1rzlxUYdsGXg
# VITE_API_URL=https://backend.aceleralia.com
# VITE_ENVIRONMENT=production