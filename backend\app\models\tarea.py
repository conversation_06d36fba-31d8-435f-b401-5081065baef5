from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime, date
from enum import Enum

# Import related models
from .user import User

class EstadoTarea(str, Enum):
    """Enum for tarea states."""
    PENDIENTE = "Pendiente"
    EN_PROGRESO = "En Progreso"
    COMPLETADA = "Completada"
    EN_REVISION = "En Revisión"
    BLOQUEADA = "Bloqueada"

class PrioridadTarea(str, Enum):
    """Enum for tarea priority."""
    BAJA = "Baja"
    MEDIA = "Media"
    ALTA = "Alta"
    URGENTE = "Urgente"

class UrgenciaTarea(str, Enum):
    """Enum for tarea urgency."""
    URGENTE = "Urgente"
    NO_URGENTE = "No Urgente"

class TareaBase(BaseModel):
    """Base model for tarea data."""
    titulo: str = Field(..., description="Título de la tarea")
    descripcion: Optional[str] = Field(None, description="Descripción detallada")
    proyecto_id: Optional[UUID] = Field(None, description="Proyecto al que pertenece")
    workflow_id: Optional[UUID] = Field(None, description="Workflow asociado")
    estado: EstadoTarea = Field(EstadoTarea.PENDIENTE, description="Estado de la tarea")
    prioridad: PrioridadTarea = Field(PrioridadTarea.MEDIA, description="Prioridad de la tarea")
    urgencia: UrgenciaTarea = Field(UrgenciaTarea.NO_URGENTE, description="Urgencia de la tarea")
    fecha_vencimiento: Optional[date] = Field(None, description="Fecha límite")
    fecha_completado: Optional[date] = Field(None, description="Fecha de completado")
    asignado_a: Optional[UUID] = Field(None, description="Usuario asignado")
    creado_por: Optional[UUID] = Field(None, description="Usuario que creó la tarea")
    tarea_padre_id: Optional[UUID] = Field(None, description="Tarea padre si es subtarea")
    info_adicional: Optional[str] = Field(None, description="Información adicional")

class TareaCreate(TareaBase):
    """Model for creating a new tarea."""
    # Additional fields for creation
    empresas_asociadas_ids: Optional[List[UUID]] = Field(default_factory=list, description="Empresas asociadas")
    etiquetas_ids: Optional[List[UUID]] = Field(default_factory=list, description="Etiquetas asociadas")

class TareaUpdate(BaseModel):
    """Model for updating tarea data."""
    titulo: Optional[str] = None
    descripcion: Optional[str] = None
    proyecto_id: Optional[UUID] = None
    workflow_id: Optional[UUID] = None
    estado: Optional[EstadoTarea] = None
    prioridad: Optional[PrioridadTarea] = None
    urgencia: Optional[UrgenciaTarea] = None
    fecha_vencimiento: Optional[date] = None
    fecha_completado: Optional[date] = None
    asignado_a: Optional[UUID] = None
    tarea_padre_id: Optional[UUID] = None
    info_adicional: Optional[str] = None

class TareaInDBBase(TareaBase):
    """Base model for tarea data stored in the database."""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Tarea(TareaInDBBase):
    """Model representing a tarea as returned from the API."""
    # Populated by service layer
    asignado_usuario: Optional[User] = None
    creador_usuario: Optional[User] = None
    proyecto_nombre: Optional[str] = None
    tarea_padre: Optional[str] = None  # Título de la tarea padre
    
    # Related data
    subtareas: Optional[List['Tarea']] = Field(default_factory=list)
    empresas_asociadas: Optional[List[dict]] = Field(default_factory=list)
    etiquetas: Optional[List[dict]] = Field(default_factory=list)
    
    # Calculated fields
    dias_vencimiento: Optional[int] = Field(None, description="Días hasta vencimiento (negativo si vencida)")
    es_vencida: bool = Field(False, description="Si la tarea está vencida")

class TareaInDB(TareaInDBBase):
    """Model representing a tarea stored in the database."""
    pass

# Response models
class TareaResponse(Tarea):
    """Response model for tarea API endpoints."""
    pass

class TareaListResponse(BaseModel):
    """Response model for tarea list endpoints."""
    tareas: List[Tarea]
    total: int
    page: int
    size: int

# Dashboard specific models
class TareaMatrix(BaseModel):
    """Model for task matrix in dashboard."""
    urgente_importante: List['TareaSummary'] = Field(default_factory=list)
    no_urgente_importante: List['TareaSummary'] = Field(default_factory=list)
    urgente_no_importante: List['TareaSummary'] = Field(default_factory=list)
    no_urgente_no_importante: List['TareaSummary'] = Field(default_factory=list)

class TareaSummary(BaseModel):
    """Simplified tarea model for dashboard and lists."""
    id: UUID
    titulo: str
    estado: EstadoTarea
    prioridad: PrioridadTarea
    urgencia: UrgenciaTarea
    fecha_vencimiento: Optional[date]
    asignado_a: Optional[UUID]
    asignado_nombre: Optional[str] = None
    proyecto_id: Optional[UUID]
    proyecto_nombre: Optional[str] = None
    dias_vencimiento: Optional[int] = None
    es_vencida: bool = False

# Filter models
class TareaFilters(BaseModel):
    """Model for tarea filtering."""
    estado: Optional[EstadoTarea] = None
    prioridad: Optional[PrioridadTarea] = None
    urgencia: Optional[UrgenciaTarea] = None
    proyecto_id: Optional[UUID] = None
    asignado_a: Optional[UUID] = None
    empresa_id: Optional[UUID] = None
    vencidas: Optional[bool] = None
    search: Optional[str] = None

# Kanban models
class TareaKanbanColumn(BaseModel):
    """Model for Kanban column."""
    estado: EstadoTarea
    tareas: List[TareaSummary]
    total: int

class TareaKanbanBoard(BaseModel):
    """Model for complete Kanban board."""
    columnas: List[TareaKanbanColumn]
    total_tareas: int
