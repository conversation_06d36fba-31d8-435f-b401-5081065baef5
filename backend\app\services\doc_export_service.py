import logging
from uuid import UUI<PERSON>
from typing import Optional, Dict, Any
import httpx # Added for making HTTP requests
import json # Added for parsing JSON response
from supabase import AsyncClient # Correct import
from fastapi import HTTPException, status
 

from app.models.doc_export import DocExport, DocExportCreate, DocExportUpdate
from app.core.config import get_settings # Added for accessing settings

logger = logging.getLogger(__name__)

# Timeout for the n8n webhook call (5 minutes + buffer)
N8N_WEBHOOK_TIMEOUT_SECONDS = 305

async def get_doc_export_by_threads_row_id(
    db: AsyncClient, *, user_id: UUID, threads_row_id: UUID
) -> Optional[DocExport]:
    """
    Retrieves a document export record by threads_row_id and user_id.
    """
    try:
        # Remove 'await' as .execute() after .maybe_single() might be synchronous
        response = (
            db.table("doc_exports")
            .select("*")
            .eq("threads_row_id", str(threads_row_id)) # Ensure UUIDs are strings for query
            .eq("user_id", str(user_id)) # Ensure UUIDs are strings for query
            .maybe_single() # Returns one record or None, doesn't raise error if not found
            .execute()
        )
        # Check if response object itself exists and has data
        if response and response.data:
            return DocExport(**response.data)
        # If response is None or response.data is empty/None, no record found
        return None
    except Exception as e:
        logger.error(f"Error fetching doc_export by threads_row_id {threads_row_id} for user {user_id}: {e}")
        # Depending on desired behavior, you might re-raise or return None
        # For now, let's return None and let the caller handle it.
        return None

async def create_doc_export_request(
    db: AsyncClient, *, user_id: UUID, threads_row_id: UUID
) -> Optional[DocExport]:
    """
    Creates a new document export request with 'pending' status.
    """
    doc_export_in = DocExportCreate(
        threads_row_id=threads_row_id,
        user_id=user_id,
        status="pending"
    )
    try:
        # Convert UUIDs to strings for JSON serialization during insert
        insert_payload = doc_export_in.model_dump(exclude_unset=True)
        insert_payload["threads_row_id"] = str(insert_payload["threads_row_id"])
        insert_payload["user_id"] = str(insert_payload["user_id"])

        # Execute insert first, then process response
        # Remove 'await' as .execute() after .insert() might be synchronous
        response = (
            db.table("doc_exports")
            .insert(insert_payload)
            .execute()
        )
        # Supabase insert response usually contains the inserted data in response.data
        if response and response.data and len(response.data) > 0:
             # Assuming the first record in the list is the one inserted
            return DocExport(**response.data[0])
        return None # Should not happen if insert was successful and single() was used
    except Exception as e:
        logger.error(f"Error creating doc_export for threads_row_id {threads_row_id}, user {user_id}: {e}")
        # This could be due to DB constraints (e.g., FK violation) or other issues
        # Consider raising HTTPException if this is called from an API route
        return None


async def update_doc_export_status(
    db: AsyncClient, *, export_id: UUID, status_val: str, doc_url: Optional[str] = None
) -> Optional[DocExport]:
    """
    Updates the status and optionally the doc_url of an existing document export record.
    """
    update_data: Dict[str, Any] = {"status": status_val}
    if doc_url is not None:
        update_data["doc_url"] = doc_url
    
    # Pydantic model for validation (optional here, but good for consistency)
    # doc_export_update = DocExportUpdate(status=status_val, doc_url=doc_url)
    # update_payload = doc_export_update.model_dump(exclude_none=True)
 
    try:
        # Execute update first, then process response
        # Remove 'await' as .execute() after .update() might be synchronous
        response = (
            db.table("doc_exports")
            .update(update_data)
            .eq("id", str(export_id))
            .execute()
        )
        # Supabase update response usually contains the updated data in response.data
        if response and response.data and len(response.data) > 0:
            # Assuming the first record in the list is the one updated
            return DocExport(**response.data[0])
        return None # Record with export_id not found or update failed silently
    except Exception as e:
        logger.error(f"Error updating doc_export {export_id} to status {status_val}: {e}")
        # Consider raising HTTPException
        return None
 
async def process_send_to_docs(db_client: AsyncClient, export_id: UUID, message_content: str):
    """
    Background task to trigger the n8n 'Send to Docs' workflow.
    Sends the message content and the export_id (for n8n to update status later).
    Handles only immediate errors during the webhook trigger.
    """
    settings = get_settings()
    n8n_webhook_url = settings.N8N_WEBHOOK_URL_ENVIARAGOOGLEDOCS
    # Include export_id (as string) in the payload for n8n
    payload = {
        "text": message_content,
        "export_id": str(export_id)
    }
 
    logger.info(f"Background task started for export_id: {export_id}. Triggering n8n: {n8n_webhook_url}")
 
    try:
        # Use a shorter timeout for just triggering the webhook
        async with httpx.AsyncClient(timeout=60.0) as client: # e.g., 60 seconds timeout
            response = await client.post(n8n_webhook_url, json=payload)
            response.raise_for_status()  # Raises HTTPStatusError for 4xx/5xx responses
 
            # Log success - n8n will handle the actual status update later
            logger.info(f"Successfully triggered n8n workflow for export_id: {export_id}. Response status: {response.status_code}")
            # DO NOT update status here. n8n is responsible for final status.
 
    except httpx.TimeoutException:
        logger.error(f"N8n webhook trigger call timed out for export_id: {export_id}")
        # Update status to 'error' as the workflow was likely never started
        await update_doc_export_status(db=db_client, export_id=export_id, status_val="error")
    except httpx.HTTPStatusError as e:
        logger.error(f"N8n webhook trigger call failed for export_id: {export_id}. Status: {e.response.status_code}, Response: {e.response.text}")
        # Update status to 'error' as the workflow was likely never started
        await update_doc_export_status(db=db_client, export_id=export_id, status_val="error")
    except Exception as e:
        logger.exception(f"Unexpected error triggering n8n for export_id: {export_id}: {e}")
        # Update status to 'error' as the trigger failed
        await update_doc_export_status(db=db_client, export_id=export_id, status_val="error")
    finally:
        logger.info(f"Background task finished triggering n8n for export_id: {export_id}")