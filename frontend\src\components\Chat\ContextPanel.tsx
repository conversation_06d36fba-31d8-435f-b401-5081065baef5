import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { IoClose, IoAddCircleOutline, IoTimeOutline, IoChevronDown, IoChevronForward } from 'react-icons/io5'; // Import icons
import AgentSelector from './AgentSelector'; // Import AgentSelector component
import { useChat } from '../../hooks/useChat'; // Import useChat hook
import { useMobileNavigation } from '../../hooks/useNavigation'; // Import navigation hook
import { useMobileDetection } from '../../hooks/useMobileDetection'; // Import mobile detection

interface ContextPanelProps {
  isOpen: boolean;
  onClose: () => void; // Function to close the panel
  totalTokens?: number; // Optional token count
}

const ContextPanel: React.FC<ContextPanelProps> = ({ isOpen, onClose, totalTokens }) => {
  const { startNewConversation, currentThreadId } = useChat();
  const { mainSections, groupedSections, isCurrentPath } = useMobileNavigation();
  const { isMobile, isIOS } = useMobileDetection();
  const [isNavigationExpanded, setIsNavigationExpanded] = useState(false);
  return (
    <>
      {/* Desktop Context Panel - Exactly 50% width */}
      <aside
        className={`hidden md:block fixed top-0 left-0 h-full w-1/2 bg-white shadow-2xl border-r border-gray-200 overflow-y-auto transition-transform duration-300 ease-in-out transform z-40 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        aria-hidden={!isOpen}
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold text-gray-800">Contexto del Chat</h2>
                <p className="text-sm text-gray-500 mt-1">Configura tu agente y contexto</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                aria-label="Cerrar panel de contexto"
              >
                <IoClose size={24} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Agent Selector Section */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <span className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></span>
                Selección de Agente
              </h3>
              <AgentSelector />
            </div>

            {/* Future context selectors */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                Contexto Adicional
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <p className="text-sm text-gray-600 italic">
                  Próximamente: Selectores para Empresa, Proyecto, y más contexto específico.
                </p>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Mobile Context Panel - Slide up from bottom with enhanced mobile detection */}
      {isOpen && (
        <div className={`${isMobile ? 'block' : 'hidden'} md:hidden fixed inset-0 z-60 flex flex-col justify-end mobile-context-panel`}>
          {/* Backdrop */}
          <div
            className="flex-1 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Panel */}
          <aside className={`w-full bg-white rounded-t-3xl shadow-2xl max-h-[90vh] overflow-hidden animate-slide-up ${isIOS ? 'pb-safe' : ''}`}>
            <div className="h-full flex flex-col">
              {/* Handle bar */}
              <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                <div className="w-12 h-1.5 bg-gray-300 rounded-full mx-auto mb-4"></div>
                <div className="flex justify-between items-center">
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-xl font-bold text-gray-800">Contexto del Chat</h2>
                        <p className="text-sm text-gray-600">Configura tu agente y contexto</p>
                      </div>
                      {/* Token Counter */}
                      {totalTokens && currentThreadId && (
                        <div className="flex items-center bg-white bg-opacity-70 rounded-lg px-3 py-2 border border-indigo-200 shadow-sm">
                          <span className="text-xs text-gray-500 mr-2">Tokens:</span>
                          <span className="text-sm font-semibold text-indigo-700">
                            {totalTokens > 1000 ? `${(totalTokens / 1000).toFixed(1)}k` : totalTokens}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={onClose}
                    className="p-2.5 rounded-full text-gray-500 hover:text-gray-700 hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 ml-4"
                    aria-label="Cerrar panel"
                  >
                    <IoClose size={22} />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
                {/* Agent Selector - Prominent on mobile */}
                <div className="md:hidden">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                    <span className="mr-2">🤖</span>
                    Seleccionar Agente
                  </h3>
                  <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-4">
                    <AgentSelector />
                  </div>
                </div>

                {/* Navigation Dropdown - Mobile Only */}
                <div className="md:hidden">
                  <button
                    onClick={() => setIsNavigationExpanded(!isNavigationExpanded)}
                    className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl hover:from-indigo-100 hover:to-purple-100 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <span className="mr-3 text-xl">🧭</span>
                      <span className="text-lg font-semibold text-gray-800">Navegación</span>
                    </div>
                    {isNavigationExpanded ? (
                      <IoChevronDown className="text-gray-600" size={20} />
                    ) : (
                      <IoChevronForward className="text-gray-600" size={20} />
                    )}
                  </button>

                  {isNavigationExpanded && (
                    <div className="mt-3 bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                      {/* Main Sections - Dynamic */}
                      {mainSections.map((item) => (
                        <Link
                          key={item.path}
                          to={item.path}
                          onClick={onClose}
                          className={`flex items-center px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-100 ${
                            isCurrentPath(item.path) ? 'bg-indigo-50 border-indigo-200' : ''
                          }`}
                        >
                          <span className="mr-3 text-lg">{item.icon}</span>
                          <span className={`font-medium ${
                            isCurrentPath(item.path) ? 'text-indigo-700' : 'text-gray-700'
                          }`}>
                            {item.label}
                          </span>
                        </Link>
                      ))}

                      {/* Grouped Sections - Dynamic */}
                      {groupedSections.map((group) => (
                        <div key={group.id} className="bg-gray-25">
                          <div className="px-4 py-2 bg-gray-50 border-b border-gray-100">
                            <span className="text-sm font-semibold text-gray-600 flex items-center">
                              <span className="mr-2">{group.icon}</span>
                              {group.label}
                            </span>
                          </div>

                          {group.items.map((item, itemIndex) => (
                            <Link
                              key={item.path}
                              to={item.path}
                              onClick={onClose}
                              className={`flex items-center px-8 py-2.5 hover:bg-gray-50 transition-colors ${
                                itemIndex < group.items.length - 1 ? 'border-b border-gray-100' : ''
                              } ${
                                isCurrentPath(item.path) ? 'bg-indigo-50 border-indigo-200' : ''
                              }`}
                            >
                              <span className="mr-3 text-base">{item.icon}</span>
                              <span className={`${
                                isCurrentPath(item.path) ? 'text-indigo-700' : 'text-gray-700'
                              }`}>
                                {item.label}
                              </span>
                            </Link>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Quick Actions - Mobile Only */}
                <div className="md:hidden">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="mr-2">⚡</span>
                    Chat - Acciones Rápidas
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {/* New Conversation */}
                    <button
                      onClick={() => {
                        startNewConversation();
                        onClose();
                      }}
                      className="flex flex-col items-center justify-center p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200 rounded-xl hover:from-indigo-100 hover:to-indigo-200 transition-all duration-200 active:scale-95"
                    >
                      <IoAddCircleOutline size={28} className="text-indigo-600 mb-2" />
                      <span className="text-sm font-medium text-indigo-700">Nueva Chat</span>
                    </button>

                    {/* History */}
                    <Link
                      to="/history"
                      onClick={onClose}
                      className="flex flex-col items-center justify-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 active:scale-95"
                    >
                      <IoTimeOutline size={28} className="text-purple-600 mb-2" />
                      <span className="text-sm font-medium text-purple-700">Historial</span>
                    </Link>
                  </div>

                  {/* Current Thread Info */}
                  {currentThreadId && (
                    <div className="mt-4 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                        <span className="text-sm font-medium text-green-700">
                          Conversación activa: {currentThreadId}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Agent Selector is now in the shared desktop section above */}

                {/* Future context */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="mr-2">⚙️</span>
                    Contexto Adicional
                  </h3>
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                    <p className="text-sm text-gray-700 font-medium mb-2">
                      🚀 Próximamente
                    </p>
                    <p className="text-sm text-gray-600">
                      Más opciones de contexto como proyectos, empresas y documentos.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      )}
    </>
  );
};

export default ContextPanel;