import { useState, useCallback, useEffect } from 'react';

export type FieldValue = string | number | boolean | null | undefined;

interface UseInlineEditOptions<T> {
  initialData: T[];
  updateFunction: (id: string, field: keyof T, value: FieldValue) => Promise<void>;
  onError?: (error: Error) => void;
  onSuccess?: (id: string, field: keyof T, value: FieldValue) => void;
}

interface UseInlineEditReturn<T> {
  data: T[];
  isUpdating: (id: string, field: keyof T) => boolean;
  updateField: (id: string, field: keyof T, value: FieldValue) => Promise<void>;
  setData: React.Dispatch<React.SetStateAction<T[]>>;
  errors: Record<string, string>;
  clearError: (id: string, field: keyof T) => void;
}

export function useInlineEdit<T extends { id: string }>({
  initialData,
  updateFunction,
  onError,
  onSuccess,
}: UseInlineEditOptions<T>): UseInlineEditReturn<T> {
  const [data, setData] = useState<T[]>(initialData);
  const [updatingFields, setUpdatingFields] = useState<Set<string>>(new Set());
  const [errors, setErrors] = useState<Record<string, string>>({});

  const getFieldKey = useCallback((id: string, field: keyof T) => `${id}-${String(field)}`, []);

  const isUpdating = useCallback(
    (id: string, field: keyof T) => {
      return updatingFields.has(getFieldKey(id, field));
    },
    [updatingFields, getFieldKey]
  );

  const clearError = useCallback(
    (id: string, field: keyof T) => {
      const fieldKey = getFieldKey(id, field);
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    },
    [getFieldKey]
  );

  const updateField = useCallback(
    async (id: string, field: keyof T, value: FieldValue) => {
      const fieldKey = getFieldKey(id, field);

      // Marcar como actualizando
      setUpdatingFields(prev => new Set(prev).add(fieldKey));

      // Limpiar error previo
      clearError(id, field);

      // Capturar el valor original antes del cambio optimista
      let originalValue: T[keyof T];

      try {
        // Actualizar optimísticamente en el estado local y capturar valor original
        setData(prevData => {
          const item = prevData.find(item => item.id === id);
          if (item) {
            originalValue = item[field];
          }
          return prevData.map(item =>
            item.id === id ? { ...item, [field]: value } : item
          );
        });

        // Llamar a la función de actualización
        await updateFunction(id, field, value);

        // Callback de éxito
        if (onSuccess) {
          onSuccess(id, field, value);
        }
      } catch (error) {
        // Revertir el cambio optimista usando el valor original capturado
        setData(prevData =>
          prevData.map(item => {
            if (item.id === id) {
              return { ...item, [field]: originalValue };
            }
            return item;
          })
        );

        // Establecer error
        const errorMessage = error instanceof Error ? error.message : 'Error al actualizar';
        setErrors(prev => ({ ...prev, [fieldKey]: errorMessage }));

        // Callback de error
        if (onError) {
          onError(error instanceof Error ? error : new Error('Error al actualizar'));
        }
      } finally {
        // Quitar de la lista de actualizando
        setUpdatingFields(prev => {
          const newSet = new Set(prev);
          newSet.delete(fieldKey);
          return newSet;
        });
      }
    },
    [getFieldKey, clearError, updateFunction, onSuccess, onError]
  );

  return {
    data,
    isUpdating,
    updateField,
    setData,
    errors,
    clearError,
  };
}

// Hook especializado para listas con paginación
interface UseInlineEditWithPaginationOptions<T> extends UseInlineEditOptions<T> {
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

export function useInlineEditWithPagination<T extends { id: string }>({
  currentPage,
  pageSize,
  totalItems,
  ...options
}: UseInlineEditWithPaginationOptions<T>) {
  const baseHook = useInlineEdit(options);

  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);

  return {
    ...baseHook,
    currentPageData: baseHook.data.slice(startIndex, endIndex),
    pagination: {
      currentPage,
      pageSize,
      totalItems,
      totalPages: Math.ceil(totalItems / pageSize),
      startIndex: startIndex + 1,
      endIndex,
    },
  };
}

// Utilidades para validación común
// Hook especializado para objetos individuales (como detalles de tarea)
interface UseInlineEditSingleOptions<T> {
  initialData: T | null;
  updateFunction: (id: string, field: keyof T, value: FieldValue) => Promise<void>;
  onError?: (error: Error) => void;
  onSuccess?: (id: string, field: keyof T, value: FieldValue) => void;
}

interface UseInlineEditSingleReturn<T> {
  data: T | null;
  isUpdating: boolean;
  updateField: (id: string, field: keyof T, value: FieldValue) => Promise<void>;
  setData: React.Dispatch<React.SetStateAction<T | null>>;
  errors: Record<string, string>;
  clearError: (field: keyof T) => void;
}

export function useInlineEditSingle<T extends { id: string }>({
  initialData,
  updateFunction,
  onError,
  onSuccess,
}: UseInlineEditSingleOptions<T>): UseInlineEditSingleReturn<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [updatingFields, setUpdatingFields] = useState<Set<string>>(new Set());
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update data when initialData changes
  useEffect(() => {
    setData(initialData);
  }, [initialData]);

  const isUpdating = updatingFields.size > 0;

  const clearError = useCallback(
    (field: keyof T) => {
      const fieldKey = String(field);
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    },
    []
  );

  const updateField = useCallback(
    async (id: string, field: keyof T, value: FieldValue) => {
      if (!data) return;

      const fieldKey = String(field);

      // Marcar como actualizando
      setUpdatingFields(prev => new Set(prev).add(fieldKey));

      // Limpiar error previo
      clearError(field);

      // Capturar el valor original antes del cambio optimista
      const originalValue = data[field];

      try {
        // Actualizar optimísticamente en el estado local
        setData(prevData =>
          prevData ? { ...prevData, [field]: value } : null
        );

        // Llamar a la función de actualización
        await updateFunction(id, field, value);

        // Callback de éxito
        if (onSuccess) {
          onSuccess(id, field, value);
        }
      } catch (error) {
        // Revertir el cambio optimista usando el valor original capturado
        setData(prevData =>
          prevData ? { ...prevData, [field]: originalValue } : null
        );

        // Establecer error
        const errorMessage = error instanceof Error ? error.message : 'Error al actualizar';
        setErrors(prev => ({ ...prev, [fieldKey]: errorMessage }));

        // Callback de error
        if (onError) {
          onError(error instanceof Error ? error : new Error('Error al actualizar'));
        }
      } finally {
        // Quitar de la lista de actualizando
        setUpdatingFields(prev => {
          const newSet = new Set(prev);
          newSet.delete(fieldKey);
          return newSet;
        });
      }
    },
    [data, clearError, updateFunction, onSuccess, onError]
  );

  return {
    data,
    isUpdating,
    updateField,
    setData,
    errors,
    clearError,
  };
}

export const commonValidations = {
  required: (value: FieldValue) => {
    if (
      value === null ||
      value === undefined ||
      (typeof value === 'string' && value.trim() === '')
    ) {
      return 'Este campo es requerido';
    }
    return null;
  },
  
  email: (value: string) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Formato de email inválido';
    }
    return null;
  },
  
  phone: (value: string) => {
    if (!value) return null;
    const phoneRegex = /^[+]?[\d\s\-()]{9,}$/;
    if (!phoneRegex.test(value)) {
      return 'Formato de teléfono inválido';
    }
    return null;
  },
  
  minLength: (min: number) => (value: string) => {
    if (!value) return null;
    if (value.length < min) {
      return `Mínimo ${min} caracteres`;
    }
    return null;
  },
  
  maxLength: (max: number) => (value: string) => {
    if (!value) return null;
    if (value.length > max) {
      return `Máximo ${max} caracteres`;
    }
    return null;
  },
  
  numberRange: (min?: number, max?: number) => (value: number) => {
    if (value === null || value === undefined) return null;
    if (min !== undefined && value < min) {
      return `Valor mínimo: ${min}`;
    }
    if (max !== undefined && value > max) {
      return `Valor máximo: ${max}`;
    }
    return null;
  },
};
