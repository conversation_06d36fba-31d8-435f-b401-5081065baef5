import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import SearchableDropdown, { SearchableDropdownOption } from '../UI/SearchableDropdown';
import { useAuth } from '../../hooks/useAuth'; // Assuming API calls need auth

// Define Zod schema for validation
const leadContactoSchema = z.object({
  lead_empresa_id: z.string().uuid({ message: "ID de Lead Empresa es requerido y debe ser un UUID válido" }),
  nombre: z.string().min(1, "Nombre es requerido"),
  apellidos: z.string().optional(),
  email: z.string().email("Debe ser un email válido").optional().or(z.literal('')),
  telefono: z.string().optional(),
  cargo: z.string().optional(),
  linkedin_url: z.string().url("Debe ser una URL válida para LinkedIn").optional().or(z.literal('')),
  es_contacto_principal: z.boolean(), // Rely on RHF defaultValues
  rol_decision: z.enum(['Tomador de decision', 'Influenciador', 'Otro']).optional(),
  info_adicional: z.string().optional(),
});

type LeadContactoFormValues = z.infer<typeof leadContactoSchema>;

export interface LeadContactoApiResponse extends LeadContactoFormValues {
  id: string; // UUID
  created_at: string;
  updated_at: string;
}

interface LeadContactoCreateFormProps {
  onSubmitSuccess: (data: LeadContactoApiResponse) => void;
  onCancel: () => void;
  // Props for handling Lead Empresa selection/creation if needed later
  openLeadEmpresaModal?: () => void; // For "Add New" functionality
  initialLeadEmpresaId?: string; // To prefill if creating from a Lead Empresa context
}

const LeadContactoCreateForm: React.FC<LeadContactoCreateFormProps> = ({
  onSubmitSuccess,
  onCancel,
  initialLeadEmpresaId,
  openLeadEmpresaModal
}) => {
  const { control, handleSubmit, formState: { errors, isSubmitting }, register, setValue } = useForm<LeadContactoFormValues>({
    resolver: zodResolver(leadContactoSchema),
    defaultValues: {
      lead_empresa_id: initialLeadEmpresaId || '',
      nombre: '',
      apellidos: '',
      email: '',
      telefono: '',
      cargo: '',
      linkedin_url: '',
      es_contacto_principal: false,
      rol_decision: undefined,
      info_adicional: '',
    }
  });
  const { session } = useAuth();
  const [leadEmpresaOptions, setLeadEmpresaOptions] = useState<SearchableDropdownOption[]>([]);
  const [isLoadingLeadEmpresas, setIsLoadingLeadEmpresas] = useState(false);

  useEffect(() => {
    // Pre-fill lead_empresa_id if initialLeadEmpresaId is provided
    if (initialLeadEmpresaId) {
      setValue('lead_empresa_id', initialLeadEmpresaId);
    }
  }, [initialLeadEmpresaId, setValue]);
  
  // Mock fetch for Lead Empresas - replace with actual API call
  const fetchLeadEmpresas = async (searchTerm: string): Promise<SearchableDropdownOption[]> => {
    setIsLoadingLeadEmpresas(true);
    console.log('Fetching lead empresas with term:', searchTerm);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 700));
    // Mock data - replace with actual API call to /api/v1/leads-empresas?search=searchTerm
    const mockData: { id: string, nombre: string, fuente?: string }[] = [
      { id: 'a1b2c3d4-e5f6-7890-1234-567890abcdef', nombre: 'Lead Corp Central', fuente: 'Web' },
      { id: 'b2c3d4e5-f6a7-8901-2345-67890abcdef0', nombre: 'Innovate Solutions Lead', fuente: 'Referral' },
      { id: 'c3d4e5f6-a7b8-9012-3456-7890abcdef01', nombre: 'Future Tech Prospects', fuente: 'Event' },
      { id: 'd4e5f6a7-b8c9-0123-4567-890abcdef012', nombre: 'Global Leads Inc.', fuente: 'Web' },
    ];

    const filtered = mockData
        .filter(le => le.nombre.toLowerCase().includes(searchTerm.toLowerCase()))
        .map(le => ({
            value: le.id,
            label: le.nombre,
            subLabel: le.fuente ? `Fuente: ${le.fuente}` : undefined
        }));
    setLeadEmpresaOptions(filtered); // Keep a local copy if needed for "Add New" logic
    setIsLoadingLeadEmpresas(false);
    return filtered;
  };

  // Initial fetch for dropdown (optional, if you want to populate it without typing)
   useEffect(() => {
    fetchLeadEmpresas(''); // Fetch all or top N initially
  }, []);


  const onSubmit: SubmitHandler<LeadContactoFormValues> = async (data) => {
    const payload = { ...data };
    if (payload.rol_decision === undefined || payload.rol_decision === null) {
        delete payload.rol_decision; // Ensure optional enum is not sent as empty string
    }

    try {
      console.log('Submitting Lead Contacto Data:', payload);
      // Replace with actual API call
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/lead-contactos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Error al crear lead contacto');
      }
      const result = await response.json();
      onSubmitSuccess(result as LeadContactoApiResponse);
    } catch (error) {
      console.error('Error submitting lead contacto form:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formFieldClass = "mb-4";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm";
  const errorClass = "mt-1 text-xs text-red-600";
  const checkboxLabelClass = "ml-2 block text-sm text-gray-900";
  const checkboxClass = "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500";

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 bg-white shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Crear Nuevo Lead Contacto</h2>

      <div className={formFieldClass}>
        <label htmlFor="lead_empresa_id" className={labelClass}>Lead Empresa*</label>
        <Controller
          name="lead_empresa_id"
          control={control}
          rules={{ required: "Lead Empresa es requerida" }}
          render={({ field }) => (
            <SearchableDropdown
              options={leadEmpresaOptions}
              value={field.value}
              onChange={(val) => field.onChange(val)}
              placeholder="Buscar o seleccionar Lead Empresa..."
              fetchOptions={fetchLeadEmpresas}
              isLoading={isLoadingLeadEmpresas}
              onAddNew={openLeadEmpresaModal} // Pass the function to open LeadEmpresa modal
              addNewLabel="+ Nueva Lead Empresa"
              disabled={isSubmitting}
            />
          )}
        />
        {errors.lead_empresa_id && <p className={errorClass}>{errors.lead_empresa_id.message}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="nombre" className={labelClass}>Nombre*</label>
          <input id="nombre" {...register("nombre")} className={inputClass} />
          {errors.nombre && <p className={errorClass}>{errors.nombre.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="apellidos" className={labelClass}>Apellidos</label>
          <input id="apellidos" {...register("apellidos")} className={inputClass} />
          {errors.apellidos && <p className={errorClass}>{errors.apellidos.message}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="email" className={labelClass}>Email</label>
          <input id="email" type="email" {...register("email")} className={inputClass} />
          {errors.email && <p className={errorClass}>{errors.email.message}</p>}
        </div>
        <div className={formFieldClass}>
          <label htmlFor="telefono" className={labelClass}>Teléfono</label>
          <input id="telefono" {...register("telefono")} className={inputClass} />
          {errors.telefono && <p className={errorClass}>{errors.telefono.message}</p>}
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="cargo" className={labelClass}>Cargo</label>
        <input id="cargo" {...register("cargo")} className={inputClass} />
        {errors.cargo && <p className={errorClass}>{errors.cargo.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="linkedin_url" className={labelClass}>LinkedIn URL</label>
        <input id="linkedin_url" type="url" {...register("linkedin_url")} className={inputClass} />
        {errors.linkedin_url && <p className={errorClass}>{errors.linkedin_url.message}</p>}
      </div>
      
      <div className={formFieldClass}>
        <label htmlFor="rol_decision" className={labelClass}>Rol Decisión</label>
        <select
          id="rol_decision"
          {...register("rol_decision", { setValueAs: v => v === "" ? undefined : v })}
          className={inputClass}
        >
          <option value="">Seleccione un rol</option>
          <option value="Tomador de decision">Tomador de decisión</option>
          <option value="Influenciador">Influenciador</option>
          <option value="Otro">Otro</option>
        </select>
        {errors.rol_decision && <p className={errorClass}>{errors.rol_decision.message}</p>}
      </div>

      <div className={`${formFieldClass} flex items-center`}>
        <input id="es_contacto_principal" type="checkbox" {...register("es_contacto_principal")} className={checkboxClass} />
        <label htmlFor="es_contacto_principal" className={checkboxLabelClass}>Es Contacto Principal</label>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="info_adicional" className={labelClass}>Info Adicional</label>
        <textarea id="info_adicional" {...register("info_adicional")} rows={3} className={inputClass}></textarea>
        {errors.info_adicional && <p className={errorClass}>{errors.info_adicional.message}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button type="button" onClick={onCancel} disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          Cancelar
        </button>
        <button type="submit" disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          {isSubmitting ? 'Creando...' : 'Crear Lead Contacto'}
        </button>
      </div>
    </form>
  );
};

export default LeadContactoCreateForm;