import { MeetingState, MeetingAction } from '../types/meeting.types';

export const initialMeetingState: MeetingState = {
  id: null,
  status: null,
  titulo: null,
  observacionesIniciales: null,
  fechaReunion: null,
  entrevista: null,
  video: null,
  rawTranscript: null,
  displayTranscript: null,
  identifiedSpeakers: [],
  speakerAssignments: {},
  audioSegments: new Map(),
  associatedEntities: [],
  potentialSpeakers: [],
  currentStep: 1,
  isUploading: false,
  uploadProgress: 0,
  connectionStatus: 'disconnected',
  connectionError: null,
};

export function meetingReducer(state: MeetingState, action: MeetingAction): MeetingState {
  switch (action.type) {
    case 'RESET':
      return initialMeetingState;

    case 'LOAD_MEETING':
      return {
        ...state,
        ...action.payload,
      };

    case 'SET_ID':
      return {
        ...state,
        id: action.id,
      };

    case 'UPDATE_STATUS':
      return {
        ...state,
        status: action.status,
      };

    case 'SET_ENTREVISTA':
      return {
        ...state,
        entrevista: action.entrevista,
      };

    case 'SET_VIDEO':
      return {
        ...state,
        video: action.video,
      };

    case 'SET_TRANSCRIPT':
      return {
        ...state,
        rawTranscript: action.rawTranscript,
        displayTranscript: action.displayTranscript,
      };

    case 'SET_SPEAKERS': {
      // Initialize empty assignments for new speakers
      const newAssignments = { ...state.speakerAssignments };
      action.speakers.forEach(speaker => {
        if (!newAssignments[speaker]) {
          newAssignments[speaker] = {
            id: '',
            tipo: 'persona',
            nombre: '',
            apellidos: null,
            email: undefined
          };
        }
      });

      return {
        ...state,
        identifiedSpeakers: action.speakers,
        speakerAssignments: newAssignments,
      };
    }

    case 'UPDATE_ASSIGNMENT':
      return {
        ...state,
        speakerAssignments: {
          ...state.speakerAssignments,
          [action.speakerTag]: action.assignment,
        },
      };

    case 'SET_AUDIO_SEGMENTS':
      return {
        ...state,
        audioSegments: action.segments,
      };

    case 'ADD_ASSOCIATED_ENTITY': {
      // Validate entity data
      if (!action.entity.id || !action.entity.nombre || !action.entity.tipo) {
        console.warn('Invalid entity data:', action.entity);
        return state;
      }

      // Prevent duplicates
      const exists = state.associatedEntities.some(
        e => e.id === action.entity.id && e.tipo === action.entity.tipo
      );
      if (exists) return state;

      return {
        ...state,
        associatedEntities: [...state.associatedEntities, action.entity],
      };
    }

    case 'REMOVE_ASSOCIATED_ENTITY':
      return {
        ...state,
        associatedEntities: state.associatedEntities.filter(
          e => !(e.id === action.id && e.tipo === action.tipo)
        ),
      };

    case 'SET_POTENTIAL_SPEAKERS':
      return {
        ...state,
        potentialSpeakers: action.speakers,
      };

    case 'SET_STEP':
      return {
        ...state,
        currentStep: action.step,
      };

    case 'SET_UPLOAD_PROGRESS':
      return {
        ...state,
        uploadProgress: action.progress,
      };

    case 'SET_UPLOADING':
      return {
        ...state,
        isUploading: action.isUploading,
      };

    case 'SET_CONNECTION_STATUS':
      if (state.connectionStatus === action.status) {
        return state; // Avoid re-render if status hasn't changed
      }
      return {
        ...state,
        connectionStatus: action.status,
      };

    case 'SET_CONNECTION_ERROR':
      return {
        ...state,
        connectionError: action.error,
      };

    default:
      return state;
  }
}