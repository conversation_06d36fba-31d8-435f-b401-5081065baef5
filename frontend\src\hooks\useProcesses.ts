import { useState, useEffect, useCallback } from 'react';
import {
  Proceso,
  ProcesoCreate,
  ProcesoUpdate,
  ProcesoListResponse,
  ProcesoSummary,
  ProcesoFilters,
  TipoProceso
} from '../types/proceso';
import { apiClient, api } from '../lib/api';

export const useProcesses = () => {
  const [processes, setProcesses] = useState<Proceso[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  const fetchProcesses = useCallback(async (
    page: number = 1,
    filters?: ProcesoFilters
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        skip: ((page - 1) * pageSize).toString(),
        limit: pageSize.toString(),
      });

      if (filters?.tipo_proceso) params.append('tipo_proceso', filters.tipo_proceso);
      if (filters?.empresa_id) params.append('empresa_id', filters.empresa_id);
      if (filters?.es_cuello_botella !== undefined) params.append('es_cuello_botella', filters.es_cuello_botella.toString());
      if (filters?.search) params.append('search', filters.search);

      const response = await apiClient.procesos.getAll(Object.fromEntries(params)) as ProcesoListResponse;
      
      setProcesses(response.procesos);
      setTotal(response.total);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching processes');
      console.error('Error fetching processes:', err);
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  const createProcess = useCallback(async (processData: ProcesoCreate): Promise<Proceso> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.procesos.create(processData) as Proceso;
      
      // Refresh the list
      await fetchProcesses(currentPage);
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating process';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchProcesses, currentPage]);

  const updateProcess = useCallback(async (
    processId: string, 
    updateData: ProcesoUpdate
  ): Promise<Proceso> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.procesos.update(processId, updateData) as Proceso;
      
      // Update the process in the local state
      setProcesses(prev => prev.map(p => p.id === processId ? response : p));
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating process';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProcess = useCallback(async (processId: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await apiClient.procesos.delete(processId);
      
      // Remove the process from local state
      setProcesses(prev => prev.filter(p => p.id !== processId));
      setTotal(prev => prev - 1);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting process';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getProcess = useCallback(async (processId: string): Promise<Proceso> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.procesos.getById(processId) as Proceso;
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching process';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const linkToProject = useCallback(async (processId: string, projectId: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await api.post(`/api/v1/procesos/${processId}/link-proyecto/${projectId}`);
      
      // Optionally refresh the process data
      const updatedProcess = await getProcess(processId);
      setProcesses(prev => prev.map(p => p.id === processId ? updatedProcess : p));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error linking process to project';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getProcess]);

  // Initialize with first page
  useEffect(() => {
    fetchProcesses(1);
  }, [fetchProcesses]);

  return {
    processes,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    fetchProcesses,
    createProcess,
    updateProcess,
    deleteProcess,
    getProcess,
    linkToProject,
    setError
  };
};

export const useProcessSummary = () => {
  const [processesSummary, setProcessesSummary] = useState<ProcesoSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProcessesSummary = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.get<ProcesoSummary[]>('/api/v1/procesos/dashboard');
      setProcessesSummary(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching processes summary');
      console.error('Error fetching processes summary:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProcessesSummary();
  }, [fetchProcessesSummary]);

  return {
    processesSummary,
    loading,
    error,
    fetchProcessesSummary,
    setError
  };
};

export const useProcessStates = () => {
  const [tipos, setTipos] = useState<TipoProceso[]>([]);
  // Estados removed - procesos table doesn't have estado column
  const [complejidades, setComplejidades] = useState<string[]>([]);
  const [prioridades, setPrioridades] = useState<string[]>([]);
  const [valoresNegocio, setValoresNegocio] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchStates = useCallback(async () => {
    setLoading(true);
    
    try {
      const [
        tiposResponse,
        complejidadResponse,
        prioridadResponse,
        valorResponse
      ] = await Promise.all([
        apiClient.procesos.getTipos(),
        api.get('/api/v1/procesos/complejidad/available'),
        api.get('/api/v1/procesos/prioridad-automatizacion/available'),
        api.get('/api/v1/procesos/valor-negocio/available')
      ]) as [
        { tipos: TipoProceso[] },
        { complejidad: string[] },
        { prioridades: string[] },
        { valores: string[] }
      ];
      
      setTipos(tiposResponse.tipos);
      setComplejidades(complejidadResponse.complejidad);
      setPrioridades(prioridadResponse.prioridades);
      setValoresNegocio(valorResponse.valores);
    } catch (err) {
      console.error('Error fetching process states:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStates();
  }, [fetchStates]);

  return {
    tipos,
    complejidades,
    prioridades,
    valoresNegocio,
    loading,
    fetchStates
  };
};
