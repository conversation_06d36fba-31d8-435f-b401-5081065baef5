from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from uuid import UUID
from supabase import Client # Import Supabase client type
from app.core.database import get_supabase_client # Import Supabase client dependency
from app.models import agent as agent_models # Import agent models
from app.core.security import get_current_user_id # Import auth dependency
from app.services import agent_service # Import agent service
import logging # Import logging

router = APIRouter()
logger = logging.getLogger(__name__) # Add logger

# Use Agent model for the list response, assuming it has the necessary fields
@router.get("", response_model=List[agent_models.Agent]) # Changed path from "/" to ""
async def read_agents(
    # Use the service function instead of direct Supabase client here
    # supabase: Client = Depends(get_supabase_client),
    current_user_id: str = Depends(get_current_user_id), # Protect endpoint and get user ID
    skip: int = 0,
    limit: int = 100,
    activo: Optional[bool] = True # Default to fetching active agents
):
    """
    Retrieve a list of agents.
    Optionally filter by active status.
    Requires authentication.
    """
    try:
        # Call the service function to get active agents (or all if activo=None)
        # Note: The service function needs pagination parameters if we want to keep skip/limit
        # For now, assuming the service fetches all matching agents based on 'activo'
        if activo is True: # Only call service if filtering for active
             agents = await agent_service.get_active_agents()
             # Apply slicing for pagination if needed (less efficient than DB pagination)
             return agents[skip : skip + limit]
        else:
             # If activo is False or None, we need a different service function or modify get_active_agents
             logger.warning("Fetching non-active or all agents is not fully implemented in the service layer yet.")
             # Placeholder: return empty list or raise 501 Not Implemented
             # For now, let's use the old direct DB access for non-active/all fetch
             logger.info("Using direct DB access for fetching non-active/all agents.")
             supabase: Client = await get_supabase_client() # Get client here
             query = supabase.table("agentes").select("*")
             if activo is not None:
                 query = query.eq("activo", activo)
             query = query.range(skip, skip + limit - 1)
             response = await query.execute()
             if response.data:
                 # Validate using the base Agent model
                 validated_agents = [agent_models.Agent.model_validate(agent_data) for agent_data in response.data]
                 return validated_agents
             else:
                 return []


    except HTTPException as http_exc:
        raise http_exc # Re-raise service layer exceptions
    except Exception as e:
        logger.exception(f"Error fetching agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching agents."
        )

# Use the AgentDetails model for the response, which includes tools/companions
@router.get("/{agent_id}", response_model=agent_models.AgentDetails)
async def read_agent(
    agent_id: UUID,
    current_user_id: str = Depends(get_current_user_id) # Protect endpoint
):
    """
    Retrieve details for a specific agent by ID, including tools and companions.
    Requires authentication.
    """
    logger.info(f"User {current_user_id} requesting details for agent {agent_id}")
    try:
        agent_details = await agent_service.get_agent_details(agent_id=agent_id)
        return agent_details
    except HTTPException as http_exc:
        # Re-raise exceptions from the service layer (e.g., 404 Not Found)
        raise http_exc
    except Exception as e:
        logger.exception(f"Unexpected error fetching details for agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while fetching agent details."
        )


# --- Placeholder for future CRUD operations if needed ---
# @router.post("/", response_model=agent_models.Agent, status_code=status.HTTP_201_CREATED)
# async def create_agent(...): ...

# @router.put("/{agent_id}", response_model=agent_models.Agent)
# async def update_agent(...): ...

# @router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
# async def delete_agent(...): ...