from pydantic import BaseModel, EmailStr, HttpUrl
from typing import Optional, Literal
from uuid import UUID
import datetime

# Basic model for nested LeadEmpresa information
class LeadEmpresaBasicInfo(BaseModel):
    id: UUID
    nombre: str

    class Config:
        from_attributes = True

class LeadContactoBase(BaseModel):
    lead_empresa_id: UUID # Required, link to leads_empresas.id
    nombre: str
    apellidos: Optional[str] = None
    email: Optional[EmailStr] = None
    telefono: Optional[str] = None
    cargo: Optional[str] = None
    linkedin_url: Optional[HttpUrl] = None
    es_contacto_principal: Optional[bool] = False
    rol_decision: Optional[Literal['Tomador de decision', 'Influenciador', 'Otro']] = None # ENUM from DB
    info_adicional: Optional[str] = None

class LeadContactoCreate(LeadContactoBase):
    pass

class LeadContacto(LeadContactoBase):
    id: UUID
    lead_empresa: Optional[LeadEmpresaBasicInfo] = None # For nested data

    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True
