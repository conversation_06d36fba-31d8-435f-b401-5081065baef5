from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List # Import List
from uuid import UUID

from app.models.crm_lead_contacto import LeadContactoCreate, LeadContacto as LeadContactoResponse
from app.services import crm_lead_contacto_service
from app.core.security import get_current_user_id

router = APIRouter()

@router.post("", response_model=LeadContactoResponse, status_code=status.HTTP_201_CREATED) # Removed trailing slash
async def create_new_lead_contacto(
    lead_contacto_in: LeadContactoCreate,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new lead contacto.
    """
    try:
        # db=None no longer needed
        created_lead_contacto_data = await crm_lead_contacto_service.create_lead_contacto(
            lead_contacto_in=lead_contacto_in, user_id=current_user_id
        )
        
        # Ensure lead_empresa_id is correctly typed for the response model if it differs
        # from what Supabase returns (e.g. string vs UUID). Pydantic should handle it.
        return LeadContactoResponse(**created_lead_contacto_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create lead contacto: {str(e)}"
        )

@router.get("", response_model=List[LeadContactoResponse]) # Removed trailing slash
async def list_lead_contactos(
    search: str | None = Query(None, alias="search"),
    lead_empresa_id: UUID | None = Query(None, alias="lead_empresa_id"), # For filtering
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id) # Protect endpoint
):
    """
    Retrieve a list of lead contactos, with optional search and filtering.
    """
    try:
        lead_contactos_data = await crm_lead_contacto_service.get_lead_contactos(
            search=search, lead_empresa_id=lead_empresa_id, skip=skip, limit=limit
        )
        # The service now returns a list of Pydantic model instances (or raises an error)
        return lead_contactos_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve lead contactos: {str(e)}"
        )

# Add other endpoints (get by ID, update, delete) as needed.
