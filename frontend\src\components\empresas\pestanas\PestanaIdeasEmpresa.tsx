/**
 * PestanaIdeasEmpresa - Ideas tab component for company details
 * Shows comprehensive ideas information with filtering, grouping, and inline editing
 */

import React, { useState } from 'react';
import { useIdeas } from '../../../hooks/useIdeas';
import LoadingSpinner from '../../UI/LoadingSpinner';
import IdeaCreateModal from './IdeaCreateModal';
import type {
  PestanaIdeasEmpresaProps,
  IdeasFilterState,
  IdeasGroupBy,
  IdeaUpdate,
  Idea,
  EstadoIdea,
  PrioridadIdea,
} from '../../../types/idea';
import {
  ESTADOS_IDEA,
  PRIORIDADES_IDEA,
  ESTADO_IDEA_COLORS,
  PRIORIDAD_IDEA_COLORS,
  getGroupDisplayLabel,
} from '../../../types/idea';
import {
  Search,
  Lightbulb,
  Plus,
  ChevronDown,
  ChevronUp,
  Edit2,
  Check,
  X,
} from 'lucide-react';

interface PestanaIdeasEmpresaPropsExtended extends PestanaIdeasEmpresaProps {
  empresaNombre?: string;
}

const PestanaIdeasEmpresa: React.FC<PestanaIdeasEmpresaPropsExtended> = ({ empresaId, empresaNombre = 'Empresa' }) => {
  // Hook for ideas data
  const {
    ideas,
    loading,
    error,
    filters,
    groupBy,
    groupedIdeas,
    totalCount,
    filteredCount,
    setFilters,
    setGroupBy,
    clearFilters,
    updateIdea,
  } = useIdeas({
    empresaId,
    autoFetch: true,
    enableRealtime: true,
  });

  // UI state
  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<string>>(new Set());
  const [editingField, setEditingField] = useState<{ ideaId: string; field: string } | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<IdeasFilterState>) => {
    setFilters({ ...filters, ...newFilters });
  };

  // Handle clear filters
  const handleClearFilters = () => {
    clearFilters();
  };

  // Handle grouping change
  const handleGroupByChange = (newGroupBy: IdeasGroupBy) => {
    setGroupBy(newGroupBy);
  };

  // Handle description expand/collapse
  const toggleDescription = (ideaId: string) => {
    setExpandedDescriptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(ideaId)) {
        newSet.delete(ideaId);
      } else {
        newSet.add(ideaId);
      }
      return newSet;
    });
  };

  // Handle inline editing
  const startEditing = (ideaId: string, field: string, currentValue: string) => {
    setEditingField({ ideaId, field });
    setEditingValue(currentValue);
  };

  const cancelEditing = () => {
    setEditingField(null);
    setEditingValue('');
  };

  const saveEditing = async () => {
    if (!editingField) return;

    try {
      const updates: IdeaUpdate = {
        [editingField.field]: editingValue,
      };
      
      await updateIdea(editingField.ideaId, updates);
      setEditingField(null);
      setEditingValue('');
    } catch (error) {
      console.error('Error updating idea:', error);
      // Error is handled by the hook
    }
  };

  // Handle key press in editing
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      saveEditing();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  // Handle idea creation
  const handleIdeaCreated = (newIdea: Idea) => {
    console.log('New idea created:', newIdea);
    // The hook should handle the update automatically, but we can force a refresh if needed
    // The createIdea function in the hook already adds the idea to the local state
  };

  // Render idea item
  const renderIdeaItem = (idea: Idea) => {
    const isDescriptionExpanded = expandedDescriptions.has(idea.id);
    const hasDescription = idea.descripcion && idea.descripcion.trim();

    return (
      <div key={idea.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        {/* Header with title and priority */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            {/* Title - editable */}
            {editingField?.ideaId === idea.id && editingField?.field === 'titulo' ? (
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={editingValue}
                  onChange={(e) => setEditingValue(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                />
                <button
                  onClick={saveEditing}
                  className="p-1 text-green-600 hover:text-green-800"
                >
                  <Check className="h-4 w-4" />
                </button>
                <button
                  onClick={cancelEditing}
                  className="p-1 text-red-600 hover:text-red-800"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <h3
                className="text-lg font-medium text-gray-900 cursor-pointer hover:text-blue-600 flex items-center group"
                onDoubleClick={() => startEditing(idea.id, 'titulo', idea.titulo)}
              >
                <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
                {idea.titulo}
                <Edit2 className="h-4 w-4 ml-2 opacity-0 group-hover:opacity-50" />
              </h3>
            )}
          </div>
          
          {/* Priority badge */}
          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${PRIORIDAD_IDEA_COLORS[idea.prioridad as PrioridadIdea]}`}>
            {idea.prioridad}
          </span>
        </div>

        {/* Estado and metadata */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            {/* Estado - editable */}
            {editingField?.ideaId === idea.id && editingField?.field === 'estado' ? (
              <div className="flex items-center space-x-2">
                <select
                  value={editingValue}
                  onChange={(e) => setEditingValue(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                >
                  {ESTADOS_IDEA.map(estado => (
                    <option key={estado} value={estado}>
                      {estado.charAt(0).toUpperCase() + estado.slice(1)}
                    </option>
                  ))}
                </select>
                <button
                  onClick={saveEditing}
                  className="p-1 text-green-600 hover:text-green-800"
                >
                  <Check className="h-4 w-4" />
                </button>
                <button
                  onClick={cancelEditing}
                  className="p-1 text-red-600 hover:text-red-800"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <span
                className={`px-2 py-1 text-xs font-medium rounded-full border cursor-pointer hover:opacity-80 ${ESTADO_IDEA_COLORS[idea.estado as EstadoIdea]}`}
                onDoubleClick={() => startEditing(idea.id, 'estado', idea.estado)}
              >
                {idea.estado.charAt(0).toUpperCase() + idea.estado.slice(1)}
              </span>
            )}
            
            {/* Created date */}
            <span className="text-sm text-gray-500">
              {new Date(idea.created_at).toLocaleDateString()}
            </span>
          </div>
          
          {/* Project info if available */}
          {idea.proyecto_relacionado && (
            <span className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
              {idea.proyecto_relacionado.nombre}
            </span>
          )}
        </div>

        {/* Description */}
        {hasDescription && (
          <div className="mt-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Descripción</span>
              <button
                onClick={() => toggleDescription(idea.id)}
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                {isDescriptionExpanded ? (
                  <>
                    Contraer <ChevronUp className="h-4 w-4 ml-1" />
                  </>
                ) : (
                  <>
                    Expandir <ChevronDown className="h-4 w-4 ml-1" />
                  </>
                )}
              </button>
            </div>
            
            {/* Description content - editable */}
            {editingField?.ideaId === idea.id && editingField?.field === 'descripcion' ? (
              <div className="space-y-2">
                <textarea
                  value={editingValue}
                  onChange={(e) => setEditingValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') cancelEditing();
                    // Don't save on Enter for textarea, allow line breaks
                  }}
                  className="w-full px-3 py-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                  autoFocus
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={saveEditing}
                    className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                  >
                    Guardar
                  </button>
                  <button
                    onClick={cancelEditing}
                    className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            ) : (
              <>
                {isDescriptionExpanded && (
                  <div
                    className="text-sm text-gray-600 cursor-pointer hover:bg-gray-50 p-2 rounded transition-all duration-200"
                    onDoubleClick={() => startEditing(idea.id, 'descripcion', idea.descripcion || '')}
                  >
                    {idea.descripcion}
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">
          <Lightbulb className="h-12 w-12 mx-auto mb-2" />
          <p className="text-lg font-medium">Error al cargar las ideas</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
            Ideas de la Empresa
          </h3>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {filteredCount} de {totalCount} ideas
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nueva Idea
            </button>
          </div>
        </div>
        
        {/* Quick stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {ESTADOS_IDEA.map(estado => {
            const count = ideas.filter(idea => idea.estado === estado).length;
            return (
              <div key={estado} className="text-center">
                <div className="text-2xl font-semibold text-gray-900">{count}</div>
                <div className={`text-sm px-2 py-1 rounded-full ${ESTADO_IDEA_COLORS[estado as EstadoIdea]}`}>
                  {estado.charAt(0).toUpperCase() + estado.slice(1)}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Filters and controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Search */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar ideas..."
                value={filters.search || ''}
                onChange={(e) => handleFiltersChange({ search: e.target.value })}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center space-x-4">
            {/* Estado filter */}
            <select
              value={filters.estado || 'all'}
              onChange={(e) => handleFiltersChange({ estado: e.target.value === 'all' ? undefined : e.target.value as EstadoIdea })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todos los estados</option>
              {ESTADOS_IDEA.map(estado => (
                <option key={estado} value={estado}>
                  {estado.charAt(0).toUpperCase() + estado.slice(1)}
                </option>
              ))}
            </select>

            {/* Prioridad filter */}
            <select
              value={filters.prioridad || 'all'}
              onChange={(e) => handleFiltersChange({ prioridad: e.target.value === 'all' ? undefined : e.target.value as PrioridadIdea })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todas las prioridades</option>
              {PRIORIDADES_IDEA.map(prioridad => (
                <option key={prioridad} value={prioridad}>
                  {prioridad}
                </option>
              ))}
            </select>

            {/* Group by */}
            <select
              value={groupBy}
              onChange={(e) => handleGroupByChange(e.target.value as IdeasGroupBy)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="none">Sin agrupar</option>
              <option value="estado">Agrupar por Estado</option>
              <option value="prioridad">Agrupar por Prioridad</option>
            </select>

            {/* Clear filters */}
            {(filters.search || filters.estado || filters.prioridad) && (
              <button
                onClick={handleClearFilters}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Limpiar filtros
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Ideas content */}
      {ideas.length === 0 ? (
        <div className="text-center py-12">
          <Lightbulb className="h-16 w-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay ideas registradas</h3>
          <p className="text-gray-500">
            {filters.search || filters.estado || filters.prioridad
              ? 'No se encontraron ideas que coincidan con los filtros aplicados.'
              : 'Esta empresa aún no tiene ideas registradas.'}
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedIdeas).map(([groupKey, groupIdeas]) => (
            <div key={groupKey} className="space-y-4">
              {groupBy !== 'none' && (
                <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  {getGroupDisplayLabel(groupKey, groupBy)} ({groupIdeas.length})
                </h4>
              )}
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {groupIdeas.map(renderIdeaItem)}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Idea Modal */}
      <IdeaCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        empresaId={empresaId}
        empresaNombre={empresaNombre}
        onIdeaCreated={handleIdeaCreated}
      />
    </div>
  );
};

export default PestanaIdeasEmpresa;
