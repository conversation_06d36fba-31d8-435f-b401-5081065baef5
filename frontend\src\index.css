@import "tailwindcss";

/* Ensure full height layout for responsive design */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* Prevent iOS Safari bounce scrolling */
  position: fixed;
  width: 100%;
}

/* Root container should fill viewport */
#root {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100); /* iOS Safari fix */
  width: 100vw;
  overflow: hidden;
}

/* Ensure smooth scrolling and better mobile experience */
* {
  box-sizing: border-box;
}

/* Enhanced mobile detection and touch targets */
@media screen and (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent text selection on mobile for better UX */
  .mobile-no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* Specific styles for mobile devices (phones) */
@media screen and (max-width: 640px) and (orientation: portrait),
       screen and (max-width: 896px) and (orientation: landscape) {
  /* Force mobile layout */
  .force-mobile {
    display: block !important;
  }

  .hide-mobile {
    display: none !important;
  }
}

/* Custom animations */
@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Improved scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Better focus styles for accessibility */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
}

/* Smooth transitions for all interactive elements */
button, input, textarea, select {
  transition: all 0.2s ease-in-out;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-specific improvements for chat */
@media screen and (max-width: 768px) {
  /* Ensure chat input is always visible above mobile keyboards */
  .chat-input-container {
    position: sticky;
    bottom: 0;
    z-index: 30;
  }

  /* Better touch targets for mobile */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }

  /* Optimize text size for mobile readability */
  .mobile-text-optimize {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }

  /* Mobile chat specific styles */
  .mobile-chat-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 50 !important;
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
  }

  /* Mobile context panel */
  .mobile-context-panel {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 60 !important;
    background: white !important;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari viewport height fix */
  .ios-vh-fix {
    height: -webkit-fill-available;
  }

  /* Prevent iOS Safari zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Safe area support for devices with notches */
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.pt-safe {
  padding-top: env(safe-area-inset-top);
}

/* Improved mobile scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
