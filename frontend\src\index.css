@import "tailwindcss";

/* Ensure full height layout for responsive design */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Ensure smooth scrolling and better mobile experience */
* {
  box-sizing: border-box;
}

/* Improve mobile touch targets */
@media (max-width: 640px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Custom animations */
@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Improved scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Better focus styles for accessibility */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
}

/* Smooth transitions for all interactive elements */
button, input, textarea, select {
  transition: all 0.2s ease-in-out;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-specific improvements for chat */
@media (max-width: 640px) {
  /* Ensure chat input is always visible above mobile keyboards */
  .chat-input-container {
    position: sticky;
    bottom: 0;
    z-index: 30;
  }

  /* Better touch targets for mobile */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }

  /* Optimize text size for mobile readability */
  .mobile-text-optimize {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }
}

/* Safe area support for devices with notches */
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.pt-safe {
  padding-top: env(safe-area-inset-top);
}

/* Improved mobile scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
