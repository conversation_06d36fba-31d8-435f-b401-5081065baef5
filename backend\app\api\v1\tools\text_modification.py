"""
Text Field Modification Tool

This tool allows authorized services (like n8n) to perform granular modifications
on text fields in the database using an API key for authentication.

Features:
- API key authentication
- Granular text modifications without full content replacement
- Multiple instruction types (replace, add, delete, delete sections)
- Sequential instruction processing
- Atomic operations with transaction support
- Comprehensive error handling and logging

Supported Operations:
- reemplazar_fragmento: Replace specific text with new content
- anadir_texto: Insert text at specific positions (start, end, before/after markers)
- eliminar_fragmento: Delete specific text
- eliminar_seccion_delimitada: Delete sections between markers
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body

from ....core.security import verify_api_key
from ....services.text_modification_service import text_modification_service
from ....models.tool import (
    TextModificationRequest,
    TextModificationResponse,
    ErrorDetail
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post(
    "/modificar_texto_campo_bd",
    response_model=TextModificationResponse,
    summary="Modify Text Field Content via API Key",
    description="Allows authorized services (like n8n) to perform granular modifications on text fields using an API key.",
    dependencies=[Depends(verify_api_key)], # Apply API key authentication
    responses={
        400: {"model": ErrorDetail, "description": "Invalid request data or table/column not found"},
        401: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        403: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        404: {"model": ErrorDetail, "description": "Record not found"},
        500: {"model": ErrorDetail, "description": "Database error or unexpected error"}
    }
)
async def modify_text_field_endpoint(
    request_body: TextModificationRequest = Body(...),
):
    """
    Endpoint to modify text field content with granular instructions.
    Requires `X-API-Key` header for authentication.
    Applies a sequence of modification instructions to the specified text field.
    
    Args:
        request_body: Text modification request containing table, column, record ID, and instructions
        
    Returns:
        TextModificationResponse: Status, message, and details about the operation
        
    Raises:
        HTTPException 400: Invalid request data or table/column not found
        HTTPException 401/403: Missing or invalid API key
        HTTPException 404: Record not found
        HTTPException 500: Database error or unexpected error
    """
    logger.info(f"Received request to modify text field {request_body.tabla}.{request_body.columna}")
    try:
        # Log basic request info for debugging (avoid logging sensitive content)
        logger.debug(f"Processing {len(request_body.instrucciones_de_cambio)} instructions for record {request_body.id_fila}")

        result = await text_modification_service.process_text_modification(request_body)

        logger.info(f"Text modification completed. Status: {result.status}, Changes made: {result.cambios_realizados_en_bd}")
        return result

    except HTTPException as http_exc:
        # Log the known HTTP exception before re-raising
        logger.warning(f"HTTPException during text modification: Status={http_exc.status_code}, Detail={http_exc.detail}")
        # Re-raise HTTPExceptions raised by the service
        raise HTTPException(status_code=http_exc.status_code, detail=http_exc.detail)
    except Exception as e:
        # Catch and log any other unexpected errors during endpoint processing
        logger.error(f"Unexpected error in /modificar_texto_campo_bd endpoint: {e}", exc_info=True) # Include traceback
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )
